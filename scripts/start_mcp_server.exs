#!/usr/bin/env elixir

# Standalone script to start the MCP server for Dynamic Forms system
# Usage: elixir scripts/start_mcp_server.exs

defmodule MCPServerStarter do
  @moduledoc """
  Standalone script to start the MCP server for Dynamic Forms system.
  
  This script can be used by Claude Code or other MCP clients to start
  the server independently of the main Phoenix application.
  """
  
  def start() do
    IO.puts("Starting MCP Server for Dynamic Forms System...")
    IO.puts("=" |> String.duplicate(50))

    # Check environment
    env = System.get_env("MIX_ENV") || "dev"
    IO.puts("Environment: #{env}")

    # Load application dependencies
    Mix.install([
      {:service_manager, path: "."}
    ])

    # Start the application
    case Application.ensure_all_started(:service_manager) do
      {:ok, _apps} ->
        IO.puts("✅ Application started successfully")

        # Wait for services to initialize
        IO.puts("⏳ Waiting for services to initialize...")
        Process.sleep(3000)

        # Check MCP server status with retries
        case wait_for_mcp_server(10) do
          {:ok, _info} ->
            IO.puts("✅ MCP Server is ready!")

            # Print connection information
            print_connection_info()

            # Keep server running
            IO.puts("\n🚀 MCP Server is running!")
            IO.puts("Press Ctrl+C to stop the server")

            # Keep alive
            Process.sleep(:infinity)

          {:error, reason} ->
            IO.puts("❌ MCP Server failed to start: #{reason}")
            System.halt(1)
        end

      {:error, {app, reason}} ->
        IO.puts("❌ Failed to start application #{app}: #{inspect(reason)}")
        System.halt(1)

      {:error, reason} ->
        IO.puts("❌ Failed to start application: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  defp wait_for_mcp_server(retries) when retries > 0 do
    case ServiceManager.MCP.Server.get_server_info() do
      {:ok, info} ->
        IO.puts("✅ MCP Server is running")
        IO.puts("   Name: #{info.name}")
        IO.puts("   Version: #{info.version}")
        IO.puts("   Status: #{info.status}")
        {:ok, info}

      {:error, _reason} ->
        IO.puts("⏳ Waiting for MCP server... (#{retries} retries left)")
        Process.sleep(1000)
        wait_for_mcp_server(retries - 1)
    end
  end

  defp wait_for_mcp_server(0) do
    {:error, "MCP server failed to start after multiple retries"}
  end
  
  defp print_connection_info() do
    config = Application.get_env(:service_manager, :mcp_server, %{})
    host = Map.get(config, :host, "localhost")
    port = Map.get(config, :port, 8080)
    
    IO.puts("\n" <> ("=" |> String.duplicate(50)))
    IO.puts("MCP SERVER CONNECTION INFORMATION")
    IO.puts("=" |> String.duplicate(50))
    IO.puts("Host: #{host}")
    IO.puts("Port: #{port}")
    IO.puts("URL: http://#{host}:#{port}")
    IO.puts("")
    IO.puts("Available Resources:")
    
    case ServiceManager.MCP.Server.list_resources() do
      {:ok, resources} ->
        Enum.each(resources, fn resource ->
          IO.puts("  • #{resource.name} - #{resource.uri}")
        end)
      _ -> 
        IO.puts("  (Unable to list resources)")
    end
    
    IO.puts("\nAvailable Tools:")
    case ServiceManager.MCP.Server.list_tools() do
      {:ok, tools} ->
        Enum.take(tools, 5)
        |> Enum.each(fn tool ->
          IO.puts("  • #{tool.name} - #{tool.description}")
        end)
        
        if length(tools) > 5 do
          IO.puts("  ... and #{length(tools) - 5} more tools")
        end
      _ -> 
        IO.puts("  (Unable to list tools)")
    end
    
    IO.puts("\nMCP Client Configuration Example:")
    IO.puts("""
    {
      "mcpServers": {
        "dynamic-forms": {
          "command": "elixir",
          "args": ["scripts/start_mcp_server.exs"],
          "cwd": "#{File.cwd!()}",
          "env": {
            "MIX_ENV": "dev"
          }
        }
      }
    }
    """)
    
    IO.puts("=" |> String.duplicate(50))
  end
end

# Start the server
MCPServerStarter.start()