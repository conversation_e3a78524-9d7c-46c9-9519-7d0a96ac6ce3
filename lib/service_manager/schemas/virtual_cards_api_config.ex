defmodule ServiceManager.Schemas.VirtualCardsApiConfig do
  use Ecto.Schema
  import Ecto.Changeset

  schema "virtual_cards_api_configs" do
    field :timeout, :integer, default: 30
    field :status, :string, default: "inactive"
    field :version, :string
    field :api_key, :string
    field :provider_name, :string
    field :base_url, :string
    field :auth_token, :string
    field :api_secret, :string
    field :notes, :string
    field :is_default, :boolean, default: false

    field :created_by, :integer
    field :updated_by, :integer

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(api_config, attrs) do
    api_config
    |> cast(attrs, [
      :provider_name,
      :base_url,
      :updated_by,
      :created_by,
      :is_default,
      :auth_token,
      :api_key,
      :api_secret,
      :version,
      :timeout,
      :status,
      :notes
    ])
    |> validate_required([
      :provider_name,
      :base_url,
      :auth_token,
      :api_key,
      :api_secret,
      :version,
      :timeout
    ])
  end
end
