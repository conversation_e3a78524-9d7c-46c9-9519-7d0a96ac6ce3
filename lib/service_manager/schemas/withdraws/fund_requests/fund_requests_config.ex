defmodule ServiceManager.Schemas.Withdraws.FundRequests.FundRequestsConfig do
  @moduledoc """
  Configuration for fund requests functionality.
  """

  use Ecto.Schema
  import Ecto.Changeset

  schema "fund_requests_config" do
    field :validate_receiver_account, :boolean, default: true
    timestamps()
  end

  def changeset(config, attrs) do
    config
    |> cast(attrs, [:validate_receiver_account])
    |> validate_required([:validate_receiver_account])
  end
end
