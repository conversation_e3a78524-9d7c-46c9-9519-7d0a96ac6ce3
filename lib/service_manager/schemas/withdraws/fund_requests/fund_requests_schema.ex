defmodule ServiceManager.Schemas.Withdraws.FundRequests.FundRequestsSchema do
  use Ecto.Schema
  import Ecto.Changeset

  schema "fund_requests" do
    field :amount, :decimal
    field :status, :string, default: "pending"
    field :transaction_reference, :string
    field :description, :string
    field :receiver_account, :string
    field :sender_account, :string

    belongs_to :sender, ServiceManager.Accounts.User
    belongs_to :receiver, ServiceManager.Accounts.User

    timestamps()
  end

  @doc false
  def changeset(fund_request, attrs) do
    fund_request
    |> cast(attrs, [
      :amount,
      :status,
      :transaction_reference,
      :description,
      :receiver_account,
      :sender_account,
      :sender_id,
      :receiver_id
    ])
    |> validate_required([:amount, :receiver_account, :sender_account])
    |> validate_number(:amount, greater_than: 0)
    |> validate_inclusion(:status, ["pending", "approved", "rejected"])
    |> unique_constraint(:transaction_reference)
  end

  def create_changeset(fund_request, attrs) do
    fund_request
    |> cast(attrs, [:amount, :description, :receiver_account, :sender_account, :sender_id])
    |> validate_required([:amount, :receiver_account, :sender_account, :sender_id])
    |> validate_number(:amount, greater_than: 0)
    |> put_change(:status, "pending")
    |> put_change(:transaction_reference, generate_reference())
  end

  def approve_changeset(fund_request, attrs) do
    fund_request
    |> cast(attrs, [:receiver_id])
    |> validate_required([:receiver_id])
    |> put_change(:status, "approved")
  end

  def reject_changeset(fund_request) do
    change(fund_request)
    |> put_change(:status, "rejected")
  end

  defp generate_reference do
    "FR" <> ServiceManager.Utilities.Generator.random_string(8)
  end
end
