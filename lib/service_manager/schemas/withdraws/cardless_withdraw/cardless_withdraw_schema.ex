defmodule ServiceManager.Schemas.Withdraws.CardlessWithdraw.CardlessWithdrawSchema do
  use Ecto.Schema
  import Ecto.Changeset

  schema "cardless_withdraws" do
    field :amount, :decimal
    field :reference_number, :string
    field :status, :string, default: "pending"
    field :otp, :string
    field :otp_verified, :boolean, default: false
    field :suspense_account, :string
    field :narration, :string
    field :source_account, :string
    field :beneficiary_phone, :string
    field :beneficiary_name, :string

    belongs_to :user, ServiceManager.Accounts.User,
      foreign_key: :user_id,
      references: :id,
      type: :id

    timestamps()
  end

  @required_fields ~w(amount reference_number source_account beneficiary_phone beneficiary_name user_id)a
  @optional_fields ~w(status otp otp_verified suspense_account narration)a

  def changeset(cardless_withdraw, attrs) do
    cardless_withdraw
    |> cast(attrs, @required_fields ++ @optional_fields)
    |> validate_required(@required_fields)
    |> validate_number(:amount, greater_than: 0)
    |> validate_length(:reference_number, min: 6)
    |> validate_length(:otp, is: 6, message: "OTP must be 6 digits")
    |> validate_format(:beneficiary_phone, ~r/^\+?[0-9]+$/,
      message: "Invalid phone number format"
    )
    |> foreign_key_constraint(:user_id)
  end

  def create_changeset(cardless_withdraw, attrs) do
    attrs =
      Map.merge(attrs, %{
        "reference_number" => generate_reference_number(),
        "otp" => generate_otp()
      })

    changeset(cardless_withdraw, attrs)
  end

  defp generate_reference_number do
    :crypto.strong_rand_bytes(8)
    |> Base.encode16()
    |> String.slice(0, 8)
  end

  defp generate_otp do
    :rand.uniform(999_999)
    |> Integer.to_string()
    |> String.pad_leading(6, "0")
  end
end
