defmodule ServiceManager.Schemas.Accounting.AccountingLedger do
  @moduledoc """
  Schema for accounting ledgers (chart of accounts).
  
  This schema represents the chart of accounts in a double-entry accounting system.
  Each record represents an account in the chart of accounts with its type, category,
  and other metadata.
  
  Account types follow standard accounting principles:
  - asset: Resources owned by the business (debit increases, credit decreases)
  - liability: Obligations owed by the business (credit increases, debit decreases)
  - equity: Owner's interest in the business (credit increases, debit decreases)
  - revenue: Income earned by the business (credit increases, debit decreases)
  - expense: Costs incurred by the business (debit increases, credit decreases)
  """
  use Ecto.Schema
  import Ecto.Changeset
  use Endon

  @account_types ["asset", "liability", "equity", "revenue", "expense"]
  @account_categories [
    # Asset categories
    "current_asset", "fixed_asset", "non_current_asset", "intangible_asset",
    # Liability categories
    "current_liability", "non_current_liability", "long_term_liability",
    # Equity categories
    "capital", "retained_earnings", "reserves",
    # Revenue categories
    "operating_revenue", "non_operating_revenue",
    # Expense categories
    "operating_expense", "non_operating_expense", "tax_expense"
  ]

  @derive {Jason.Encoder,
           only: [
             :id,
             :code,
             :name,
             :description,
             :account_type,
             :account_category,
             :parent_id,
             :is_active,
             :is_system,
             :balance,
             :currency
           ]}
  schema "accounting_ledgers" do
    field :code, :string
    field :name, :string
    field :description, :string
    field :account_type, :string
    field :account_category, :string
    field :is_active, :boolean, default: true
    field :is_system, :boolean, default: false
    field :balance, :decimal, default: Decimal.new(0)
    field :currency, :string, default: "MWK"
    field :created_by, :integer
    field :updated_by, :integer

    # Self-referential relationship for hierarchical chart of accounts
    belongs_to :parent, __MODULE__
    has_many :children, __MODULE__, foreign_key: :parent_id
    
    # Relationship with accounting entries
    has_many :entries, ServiceManager.Schemas.Accounting.AccountingEntry, foreign_key: :ledger_id

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(ledger, attrs) do
    ledger
    |> cast(attrs, [
      :code,
      :name,
      :description,
      :account_type,
      :account_category,
      :parent_id,
      :is_active,
      :is_system,
      :balance,
      :currency,
      :created_by,
      :updated_by
    ])
    |> validate_required([:code, :name, :account_type, :account_category])
    |> validate_inclusion(:account_type, @account_types)
    |> validate_inclusion(:account_category, @account_categories)
    |> validate_account_category_type_match()
    |> unique_constraint(:code)
    |> foreign_key_constraint(:parent_id)
  end

  # Validate that the account category matches the account type
  defp validate_account_category_type_match(changeset) do
    account_type = get_field(changeset, :account_type)
    account_category = get_field(changeset, :account_category)

    if account_type && account_category do
      valid_match =
        case account_type do
          "asset" -> Enum.member?(["current_asset", "fixed_asset", "non_current_asset", "intangible_asset"], account_category)
          "liability" -> Enum.member?(["current_liability", "non_current_liability", "long_term_liability"], account_category)
          "equity" -> Enum.member?(["capital", "retained_earnings", "reserves"], account_category)
          "revenue" -> Enum.member?(["operating_revenue", "non_operating_revenue"], account_category)
          "expense" -> Enum.member?(["operating_expense", "non_operating_expense", "tax_expense"], account_category)
          _ -> false
        end

      if valid_match do
        changeset
      else
        add_error(changeset, :account_category, "does not match the account type")
      end
    else
      changeset
    end
  end
end
