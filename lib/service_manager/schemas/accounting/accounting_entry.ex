defmodule ServiceManager.Schemas.Accounting.AccountingEntry do
  @moduledoc """
  Schema for accounting entries in a double-entry accounting system.
  
  This schema represents individual accounting entries that make up double-entry
  accounting records. Each financial transaction will have at least two entries:
  one debit and one credit.
  
  Entry types:
  - debit: Increases asset and expense accounts, decreases liability, equity, and revenue accounts
  - credit: Decreases asset and expense accounts, increases liability, equity, and revenue accounts
  
  Status values:
  - draft: Entry is created but not yet posted to the ledger
  - posted: Entry has been posted to the ledger and affects account balances
  - voided: Entry has been voided and does not affect account balances
  """
  use Ecto.Schema
  import Ecto.Changeset
  use Endon

  alias ServiceManager.Schemas.Accounting.AccountingLedger
  alias ServiceManager.Transactions.Transaction
  alias ServiceManager.Transactions.WalletTransactions

  @entry_types ["debit", "credit"]
  @status_types ["draft", "posted", "voided"]

  @derive {Jason.Encoder,
           only: [
             :id,
             :entry_type,
             :amount,
             :description,
             :transaction_date,
             :value_date,
             :reference,
             :external_reference,
             :status,
             :currency,
             :ledger_id,
             :transaction_id,
             :wallet_transaction_id,
             :metadata
           ]}
  schema "accounting_entries" do
    field :entry_type, :string
    field :amount, :decimal
    field :description, :string
    field :transaction_date, :utc_datetime
    field :value_date, :date
    field :reference, :string
    field :external_reference, :string
    field :status, :string, default: "posted"
    field :currency, :string, default: "MWK"
    field :metadata, :map, default: %{}
    field :created_by, :integer
    field :updated_by, :integer

    # Relationships
    belongs_to :ledger, AccountingLedger
    belongs_to :transaction, Transaction
    belongs_to :wallet_transaction, WalletTransactions

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(entry, attrs) do
    entry
    |> cast(attrs, [
      :entry_type,
      :amount,
      :description,
      :transaction_date,
      :value_date,
      :reference,
      :external_reference,
      :status,
      :currency,
      :metadata,
      :ledger_id,
      :transaction_id,
      :wallet_transaction_id,
      :created_by,
      :updated_by
    ])
    |> validate_required([
      :entry_type,
      :amount,
      :transaction_date,
      :value_date,
      :reference,
      :ledger_id
    ])
    |> validate_inclusion(:entry_type, @entry_types)
    |> validate_inclusion(:status, @status_types)
    |> validate_number(:amount, greater_than: 0)
    |> foreign_key_constraint(:ledger_id)
    |> foreign_key_constraint(:transaction_id)
    |> foreign_key_constraint(:wallet_transaction_id)
    |> validate_transaction_reference()
  end

  # Ensure that at least one transaction reference is provided
  defp validate_transaction_reference(changeset) do
    transaction_id = get_field(changeset, :transaction_id)
    wallet_transaction_id = get_field(changeset, :wallet_transaction_id)

    if is_nil(transaction_id) && is_nil(wallet_transaction_id) do
      add_error(
        changeset,
        :transaction_reference,
        "At least one transaction reference (transaction_id or wallet_transaction_id) must be provided"
      )
    else
      changeset
    end
  end
end
