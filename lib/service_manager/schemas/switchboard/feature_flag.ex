defmodule ServiceManager.Schemas.Switchboard.FeatureFlag do
  use Ecto.Schema
  import Ecto.Changeset

  schema "feature_flags" do
    field :name, :string
    field :description, :string
    field :category, :string
    field :enabled, :boolean, default: false
    # Store all values as strings
    field :default_value, :string
    # Store all values as strings
    field :value, :string
    field :registered, :boolean, default: false
    # "boolean" or "integer"
    field :value_type, :string, default: "boolean"
    field :node_name, :string, default: Node.self() |> to_string()

    timestamps()
  end

  @doc false
  def changeset(feature_flag, attrs) do
    feature_flag
    |> cast(attrs, [
      :name,
      :description,
      :category,
      :enabled,
      :default_value,
      :value,
      :registered,
      :value_type,
      :node_name
    ])
    |> validate_required([:name, :category, :value_type, :node_name])
    |> validate_value_type()
    |> validate_value_format()
    |> unique_constraint([:name, :node_name], name: :feature_flags_name_node_index)
  end

  defp validate_value_type(changeset) do
    case get_field(changeset, :value_type) do
      type when type in ["boolean", "integer"] -> changeset
      _ -> add_error(changeset, :value_type, "must be either 'boolean' or 'integer'")
    end
  end

  defp validate_value_format(changeset) do
    value_type = get_field(changeset, :value_type)
    value = get_field(changeset, :value)
    default_value = get_field(changeset, :default_value)

    case value_type do
      "boolean" ->
        validate_boolean_strings(changeset, value, default_value)

      "integer" ->
        validate_integer_strings(changeset, value, default_value)

      _ ->
        changeset
    end
  end

  defp validate_boolean_strings(changeset, value, default_value) do
    cond do
      is_nil(value) and valid_boolean_string?(default_value) ->
        changeset

      is_nil(value) ->
        add_error(changeset, :default_value, "must be 'true' or 'false'")

      valid_boolean_string?(value) and valid_boolean_string?(default_value) ->
        changeset

      not valid_boolean_string?(value) ->
        add_error(changeset, :value, "must be 'true' or 'false'")

      not valid_boolean_string?(default_value) ->
        add_error(changeset, :default_value, "must be 'true' or 'false'")

      true ->
        changeset
    end
  end

  defp validate_integer_strings(changeset, value, default_value) do
    cond do
      is_nil(value) and valid_integer_string?(default_value) ->
        changeset

      is_nil(value) ->
        add_error(changeset, :default_value, "must be a valid integer string")

      valid_integer_string?(value) and valid_integer_string?(default_value) ->
        changeset

      not valid_integer_string?(value) ->
        add_error(changeset, :value, "must be a valid integer string")

      not valid_integer_string?(default_value) ->
        add_error(changeset, :default_value, "must be a valid integer string")

      true ->
        changeset
    end
  end

  defp valid_boolean_string?(nil), do: false
  defp valid_boolean_string?(value) when is_boolean(value), do: true

  defp valid_boolean_string?(value) when is_binary(value) do
    value in ["true", "false"]
  end

  defp valid_boolean_string?(_), do: false

  defp valid_integer_string?(nil), do: false
  defp valid_integer_string?(value) when is_integer(value), do: true

  defp valid_integer_string?(value) when is_binary(value) do
    case Integer.parse(value) do
      {_, ""} -> true
      _ -> false
    end
  end

  defp valid_integer_string?(_), do: false
end
