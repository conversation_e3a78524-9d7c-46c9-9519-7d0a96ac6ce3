defmodule ServiceManager.Schemas.PaymentMethod do
  use Ecto.Schema
  import Ecto.Changeset

  schema "payment_methods" do
    field :name, :string
    field :status, :string, default: "inactive"
    field :type, :string
    field :description, :string
    field :fees_id, :integer
    field :created_by, :integer
    field :updated_by, :integer

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(payment_method, attrs) do
    payment_method
    |> cast(attrs, [:name, :type, :description, :status, :fees_id, :created_by, :updated_by])
    |> validate_required([:name, :type, :description, :status, :fees_id])
  end
end
