defmodule ServiceManager.Schemas.MerchantTransaction do
  use Ecto.Schema
  import Ecto.Changeset

  schema "merchant_transactions" do
    field :transaction_id, :string
    field :amount, :decimal
    field :currency, :string
    field :qr_code_data, :string
    field :description, :string
    field :callback_url, :string
    field :status, :string, default: "pending"
    field :callback_status, :string
    field :callback_response, :string
    field :callback_sent, :boolean, default: false

    belongs_to :merchant, ServiceManager.Schemas.Merchant

    timestamps()
  end

  @doc false
  def changeset(transaction, attrs) do
    transaction
    |> cast(attrs, [
      :merchant_id,
      :transaction_id,
      :amount,
      :currency,
      :qr_code_data,
      :description,
      :callback_url,
      :status,
      :callback_status,
      :callback_response,
      :callback_sent
    ])
    |> validate_required([:merchant_id, :transaction_id, :amount, :currency, :status])
    |> unique_constraint(:transaction_id)
    |> foreign_key_constraint(:merchant_id)
  end
end
