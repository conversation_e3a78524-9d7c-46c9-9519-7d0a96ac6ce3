defmodule ServiceManager.Schemas.Seeds.SetUser do
  @moduledoc """
  This module provides functionality to seed the database with an initial admin user and role.
  """

  alias ServiceManager.Context.SystemAuthorizationContext, as: Accounts
  alias ServiceManager.Repo
  alias ServiceManager.Contexts.RolesAndPermissionsContext

  @doc """
  Runs the seeding process to create an admin role and user.

  This function performs the following steps:
  1. Creates a "Super Admin" role with extensive permissions.
  2. Creates an admin user associated with the newly created role.

  Returns a tuple with the result of the transaction.
  """
  def run do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:admin_role, fn _, _ ->
      # Create the Super Admin role with full permissions
      permissions_params()
      |> RolesAndPermissionsContext.create_roles_and_permission()
    end)
    |> Ecto.Multi.run(:admin_user, fn _repo, %{admin_role: admin_role} ->
      # Create the admin user with the newly created role
      Accounts.register_user(%{
        email: "<EMAIL>",
        password: "Qwerty123456",
        first_name: "<PERSON><PERSON>",
        last_name: "<PERSON><PERSON>",
        nickname: "admin",
        phone_number: "************",
        roles_and_permission_id: admin_role.id
      })
    end)
    |> Repo.transaction()
  end

  def permissions_params() do
    %{
      name: "System Super Admin",
      status: "active",
      rights: %{
        backend:
          Enum.reduce(
            [
              :miscellaneous,
              :currency,
              :exchange_rate,
              :fees,
              :virtual_cards,
              :paylink,
              :gift_cards,
              :users,
              :agents,
              :merchants,
              :customers,
              :beneficiaries,
              :web_settings,
              :application_settings,
              :apis_settings,
              :transactions,
              :customer_reports,
              :agent_reports,
              :village_banking_reports,
              :merchants_reports,
              :wallets_reports,
              :loan_products,
              :loan_partnership,
              :loan_upload_products,
              :loan_customers,
              :loan_upload_customers,
              :loan_customers_batches,
              :loan_reports,
              :loan_transactions,
              :account_details
            ],
            %{},
            fn module, acc ->
              Map.put(acc, module, %{
                index: true,
                view: true,
                create: true,
                update: true,
                review: true,
                approve: true,
                activate: true,
                debug: true,
                delete: true,
                download: true
              })
            end
          )
      }
    }
  end
end
