defmodule ServiceManager.Schamas.SystemLog do
  use Ecto.Schema
  import Ecto.Changeset

  @levels ~w(debug info warn error fatal)
  @categories ~w(authentication database api system)

  schema "system_logs" do
    field :timestamp, :utc_datetime_usec
    field :level, :string
    field :category, :string
    field :message, :string
    field :metadata, :map
    field :source, :string
    field :trace_id, :string
    field :user_id, :integer
    # EctoNetwork.INET
    field :ip_address, :string

    timestamps()
  end

  @doc false
  def changeset(system_log, attrs) do
    system_log
    |> cast(attrs, [
      :timestamp,
      :level,
      :category,
      :message,
      :metadata,
      :source,
      :trace_id,
      :user_id,
      :ip_address
    ])
    |> validate_required([:level, :category, :message])
    |> validate_inclusion(:level, @levels)
    |> validate_inclusion(:category, @categories)
  end
end
