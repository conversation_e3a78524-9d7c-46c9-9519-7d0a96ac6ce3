defmodule ServiceManager.Schemas.Embedded.EmbeddedT24Urls do
  use Ecto.Schema
  import Ecto.Changeset

  @fields ~w(
      create_fund_transfer
      get_customer_profile 
      get_account_balance
      open_current_account
      get_book_balance
      reserve_funds
      release_reserved_funds
      get_transaction
      get_account_transaction
      get_currency_exchange
    )a

  embedded_schema do
    field :create_fund_transfer, :string
    field :get_customer_profile, :string
    field :get_account_balance, :string
    field :open_current_account, :string
    field :get_book_balance, :string
    field :reserve_funds, :string
    field :release_reserved_funds, :string
    field :get_transaction, :string
    field :get_account_transaction, :string
    field :get_currency_exchange, :string
  end

  @doc false
  def changeset(user, attrs) do
    user
    |> cast(attrs, @fields)
    |> validate_required(@fields)
  end
end
