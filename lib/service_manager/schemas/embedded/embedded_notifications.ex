defmodule ServiceManager.Schemas.Embedded.EmbeddedNotifications do
  use Ecto.Schema
  import Ecto.Changeset

  embedded_schema do
    # Email notification settings
    field :email_notifications_enabled, :boolean, default: true
    field :email_from_name, :string
    field :email_from_address, :string
    field :smtp_host, :string
    field :smtp_port, :integer
    # tls, ssl, none
    field :smtp_encryption, :string
    field :smtp_username, :string
    field :smtp_password, :string

    # SMS notification settings  
    field :sms_notifications_enabled, :boolean, default: true
    # twilio, africasTalking etc
    field :sms_provider, :string
    field :sms_api_key, :string
    field :sms_sender_id, :string

    # Push notification settings
    field :push_notifications_enabled, :boolean, default: true
    field :fcm_server_key, :string
    field :apn_key_id, :string
    field :apn_team_id, :string
    field :apn_bundle_id, :string
    field :apn_key_file, :string
  end

  @doc false
  def changeset(notifications, attrs) do
    notifications
    |> cast(attrs, [
      :email_notifications_enabled,
      :email_from_name,
      :email_from_address,
      :smtp_host,
      :smtp_port,
      :smtp_encryption,
      :smtp_username,
      :smtp_password,
      :sms_notifications_enabled,
      :sms_provider,
      :sms_api_key,
      :sms_sender_id,
      :push_notifications_enabled,
      :fcm_server_key,
      :apn_key_id,
      :apn_team_id,
      :apn_bundle_id,
      :apn_key_file
    ])
    |> validate_required([
      :email_notifications_enabled,
      :sms_notifications_enabled,
      :push_notifications_enabled
    ])
    |> validate_inclusion(:smtp_encryption, ["tls", "ssl", "none"])
  end
end
