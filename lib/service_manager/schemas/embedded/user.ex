defmodule ServiceManager.Schemas.User do
  use Ecto.Schema
  import Ecto.Changeset

  embedded_schema do
    field :email, :string
    field :name, :string, virtual: true
    field :nickname, :string
    field :first_name, :string
    field :last_name, :string
    field :phone_number, :string
    field :date_of_birth, :date
    field :address, :string
    field :city, :string
    field :state, :string
    field :zip, :string
    field :country, :string
    field :approved, :boolean
    field :account_balance, :float, default: 0.0
    field :account_number, :string
    field :first_time_login, :boolean
    field :currency, :string
    field :account_type, :string
    field :notifications, :string
  end

  @doc false
  def changeset(user, attrs) do
    user
    |> cast(attrs, [
      :nickname,
      :email,
      :currency,
      :account_type,
      :notifications,
      :first_name,
      :last_name,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_number,
      :account_balance
    ])
    |> validate_required([:first_name, :email, :last_name])
  end

  def change_data(params \\ %{}) do
    %ServiceManager.Schemas.User{}
    |> ServiceManager.Schemas.User.changeset(params)
  end

  def change_data(schema, params) do
    schema
    |> ServiceManager.Schemas.User.changeset(params)
  end
end
