defmodule ServiceManager.Schemas.Embedded.Transactions do
  use Ecto.Schema
  import Ecto.Changeset

  embedded_schema do
    field :from_account, :string
    field :amount, :decimal
    field :to_account, :string
    field :email, :string
  end

  @doc false
  def changeset(user, attrs) do
    user
    |> cast(attrs, [:from_account, :amount, :to_account, :email])
    |> validate_required([:from_account, :amount, :to_account])
  end

  def change_data(params \\ %{}) do
    %ServiceManager.Schemas.Embedded.Transactions{}
    |> ServiceManager.Schemas.Embedded.Transactions.changeset(params)
  end

  def change_data(schema, params) do
    schema
    |> ServiceManager.Schemas.Embedded.Transactions.changeset(params)
  end
end
