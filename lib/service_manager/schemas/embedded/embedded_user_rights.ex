defmodule ServiceManager.Schema.Embedded.Rights do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Schema.Embedded.EmbeddedUserActions, as: Actions

  @behaviour Access

  @modules [
    :web_settings,
    :application_settings,
    :apis_settings,
    :system_configurations,
    :transactions,
    :customer_reports,
    :agent_reports,
    :village_banking_reports,
    :merchants_reports,
    :wallets_reports,
    :accounts_reports,
    :loan_products,
    :loan_partnership,
    :loan_partnerships,
    :loan_upload_products,
    :loan_customers,
    :loan_upload_customers,
    :loan_customers_batches,
    :loan_reports,
    :loan_transactions,
    :loan_charges,
    :account_details,
    :accounts,
    :wallets,
    :wallet_transactions,
    :wallet_tiers,
    :maintenance,
    :agents,
    :users,
    :system_users,
    :merchants,
    :beneficiaries,
    :customers,
    :settings,
    :reports,
    :logs,
    :sms_logs,
    :callbacks,
    :miscellaneous,
    :roles_and_permissions,
    :currency,
    :currencies,
    :exchange_rate,
    :exchange_rates,
    :fees,
    :payment_methods,
    :virtual_cards,
    :virtual_cards_api_configs,
    :cards,
    :paylink,
    :gift_cards,
    :cheque_requests,
    :cheque_book_requests,
    :fund_requests,
    :cardless_withdraws,
    :ip_whitelist,
    :dynamic_forms,
    :mobile_forms
  ]

  embedded_schema do
    for module <- @modules do
      embeds_one module, Actions, on_replace: :update
      field :"#{module}_all", :boolean, default: false
      field :"#{module}_menu", :boolean, default: false
    end
  end

  @doc false
  def changeset(rights, attrs) do
    rights
    |> cast(
      attrs,
      Enum.map(@modules, fn module ->
        [:"#{module}_all", :"#{module}_menu"]
      end)
      |> List.flatten()
    )
    |> cast_embedded_modules(attrs)
    |> update_menu_fields()
  end

  defp cast_embedded_modules(changeset, attrs) do
    Enum.reduce(@modules, changeset, fn module, acc_changeset ->
      cast_embed(acc_changeset, module, with: &Actions.changeset/2)
    end)
  end

  defp update_menu_fields(changeset) do
    Enum.reduce(@modules, changeset, fn module, acc_changeset ->
      embedded_changeset = get_change(acc_changeset, module)

      if embedded_changeset && any_action_true?(embedded_changeset) do
        put_change(acc_changeset, :"#{module}_menu", true)
      else
        acc_changeset
      end
    end)
  end

  defp any_action_true?(embedded_changeset) do
    embedded_changeset.changes
    |> Map.values()
    |> Enum.any?(&(&1 == true))
  end

  # Implement Access behaviour
  def fetch(struct, key), do: Map.fetch(struct, key)
  def get(struct, key, default), do: Map.get(struct, key, default)
  def get_and_update(struct, key, fun), do: Map.get_and_update(struct, key, fun)
  def pop(struct, key), do: Map.pop(struct, key)
end
