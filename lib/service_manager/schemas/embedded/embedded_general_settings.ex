defmodule ServiceManager.Schemas.Embedded.EmbeddedGeneralSettings do
  use Ecto.Schema
  import Ecto.Changeset

  embedded_schema do
    field :site_title, :string
    field :currency, :string
    field :currency_symbol, :string
    field :timezone, :string
    field :base_color, :string, default: "#154E9E"
    field :secondary_color, :string, default: "#f59e0b"
    field :opt_expiration_time, :string
    field :user_idle_time, :string
    field :account_number_length, :string
    field :minimum_limit, :integer, default: 1
    field :daily_limit, :integer
    field :monthly_limit, :integer
    field :ip_open_channel, :boolean, default: true
  end

  @doc false
  def changeset(user, attrs) do
    user
    |> cast(attrs, [
      :site_title,
      :currency,
      :currency_symbol,
      :timezone,
      :base_color,
      :secondary_color,
      :opt_expiration_time,
      :user_idle_time,
      :account_number_length,
      :minimum_limit,
      :daily_limit,
      :monthly_limit,
      :ip_open_channel
    ])
    |> validate_required([
      :site_title,
      :currency,
      :currency_symbol,
      :timezone,
      :base_color,
      :secondary_color,
      :opt_expiration_time,
      :user_idle_time,
      :account_number_length,
      :minimum_limit,
      :daily_limit,
      :monthly_limit,
      :ip_open_channel
    ])
  end
end
