defmodule ServiceManager.Schema.Embedded.ModulesEmbedded do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Schema.Embedded.Rights

  @behaviour Access

  @modules [:backend, :agency_banking, :end_users, :merchants, :village_banking]

  embedded_schema do
    for module <- @modules do
      embeds_one module, Rights, on_replace: :update
    end
  end

  @doc false
  def changeset(rights, attrs) do
    rights
    |> cast(attrs, [])
    |> cast_embedded_modules(attrs)
  end

  defp cast_embedded_modules(changeset, attrs) do
    Enum.reduce(@modules, changeset, fn module, acc_changeset ->
      cast_embed(acc_changeset, module)
    end)
  end

  # Implement Access behaviour
  def fetch(struct, key), do: Map.fetch(struct, key)
  def get(struct, key, default), do: Map.get(struct, key, default)
  def get_and_update(struct, key, fun), do: Map.get_and_update(struct, key, fun)
  def pop(struct, key), do: Map.pop(struct, key)
end
