defmodule ServiceManager.Schemas.Embedded.EmbeddedT24 do
  use Ecto.Schema
  import Ecto.Changeset

  embedded_schema do
    field :base_url, :string
    field :username, :string
    field :password, :string
    field :token, :string
    embeds_one :urls, ServiceManager.Schemas.Embedded.EmbeddedT24Urls
  end

  @doc false
  def changeset(user, attrs) do
    user
    |> cast(attrs, [:base_url, :username, :password, :token])
    |> cast_embed(:urls)
    |> validate_required([:base_url])
  end
end
