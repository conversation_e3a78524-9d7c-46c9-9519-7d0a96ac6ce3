defmodule ServiceManager.Schemas.Embedded.EmbeddedForm do
  use Ecto.Schema
  import Ecto.Changeset

  alias ServiceManager.Schemas.Embedded.EmbeddedForm

  @primary_key false
  embedded_schema do
    field :start_date, :date
    field :end_date, :date
    field :status, :string
    field :type, :string
    field :amount_from, :decimal
    field :amount_to, :decimal
    field :reference, :string
    field :from_account, :string
    field :to_account, :string

    # User fields for filtering
    field :email, :string
    field :name, :string
    field :nickname, :string
    field :first_name, :string
    field :last_name, :string
    field :phone_number, :string
    field :approved, :boolean

    # Bank account fields for filtering
    field :tag, :string
    field :account_name, :string
    field :account_number, :string
    field :account_type, :string
    field :currency, :string
    field :balance_from, :decimal
    field :balance_to, :decimal
  end

  def change_form(form \\ %__MODULE__{}, attrs \\ %{}), do: changeset(form, attrs)

  def changeset(form, attrs \\ %{}) do
    form
    |> cast(attrs, [
      :start_date,
      :end_date,
      :status,
      :type,
      :amount_from,
      :amount_to,
      :reference,
      :from_account,
      :to_account,
      :email,
      :name,
      :nickname,
      :first_name,
      :last_name,
      :phone_number,
      :approved,
      :tag,
      :account_name,
      :account_number,
      :account_type,
      :currency,
      :balance_from,
      :balance_to
    ])
    |> validate_required([])
    |> validate_dates()
    |> validate_amounts()
    |> validate_balances()
  end

  defp validate_dates(changeset) do
    today = Date.utc_today()

    changeset
    |> validate_start_date(today)
    |> validate_date_range()
  end

  defp validate_start_date(changeset, today) do
    case get_field(changeset, :start_date) do
      nil ->
        changeset

      start_date ->
        if Date.compare(start_date, today) == :gt do
          add_error(changeset, :start_date, "cannot be greater than today")
        else
          changeset
        end
    end
  end

  defp validate_date_range(changeset) do
    case {get_field(changeset, :start_date), get_field(changeset, :end_date)} do
      {start_date, end_date} when not is_nil(start_date) and not is_nil(end_date) ->
        if Date.compare(start_date, end_date) == :gt do
          add_error(changeset, :end_date, "must be greater than start date")
        else
          changeset
        end

      _ ->
        changeset
    end
  end

  defp validate_amounts(changeset) do
    case {get_field(changeset, :amount_from), get_field(changeset, :amount_to)} do
      {from, to} when not is_nil(from) and not is_nil(to) ->
        if Decimal.compare(from, to) == :gt do
          add_error(changeset, :amount_to, "must be greater than amount from date")
        else
          changeset
        end

      _ ->
        changeset
    end
  end

  defp validate_balances(changeset) do
    case {get_field(changeset, :balance_from), get_field(changeset, :balance_to)} do
      {from, to} when not is_nil(from) and not is_nil(to) ->
        if Decimal.compare(from, to) == :gt do
          add_error(changeset, :balance_to, "must be greater than balance from")
        else
          changeset
        end

      _ ->
        changeset
    end
  end
end
