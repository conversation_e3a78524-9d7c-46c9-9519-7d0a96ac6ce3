defmodule ServiceManager.Schemas.Embedded.EmbeddedImages do
  use Ecto.Schema
  import Ecto.Changeset

  embedded_schema do
    # Logo and branding images
    field :logo_url, :string
    field :favicon_url, :string
    field :login_background_url, :string
    field :dashboard_banner_url, :string

    # Image settings
    # 5MB default
    field :max_file_size, :integer, default: 5_000_000
    field :allowed_formats, {:array, :string}, default: ["jpg", "jpeg", "png", "gif"]
    field :compression_enabled, :boolean, default: true
    # 1-100
    field :compression_quality, :integer, default: 80

    # Storage configuration
    # local, s3, cloudinary etc
    field :storage_provider, :string
    field :storage_bucket, :string
    field :storage_region, :string
    field :storage_access_key, :string
    field :storage_secret_key, :string
  end

  @doc false
  def changeset(images, attrs) do
    images
    |> cast(attrs, [
      :logo_url,
      :favicon_url,
      :login_background_url,
      :dashboard_banner_url,
      :max_file_size,
      :allowed_formats,
      :compression_enabled,
      :compression_quality,
      :storage_provider,
      :storage_bucket,
      :storage_region,
      :storage_access_key,
      :storage_secret_key
    ])
    |> validate_required([
      :max_file_size,
      :allowed_formats,
      :compression_enabled,
      :compression_quality
    ])
    |> validate_number(:max_file_size, greater_than: 0)
    |> validate_number(:compression_quality, greater_than: 0, less_than_or_equal_to: 100)
    |> validate_inclusion(:storage_provider, ["local", "s3", "cloudinary"])
  end
end
