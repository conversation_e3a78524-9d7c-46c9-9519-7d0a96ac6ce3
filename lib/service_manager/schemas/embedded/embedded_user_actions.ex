defmodule ServiceManager.Schema.Embedded.EmbeddedUserActions do
  use Ecto.Schema
  import Ecto.Changeset
  ~w(
  create delete update index view download approve activate debug review
  )a

  embedded_schema do
    field :create, :boolean, default: false
    field :delete, :boolean, default: false
    field :update, :boolean, default: false
    field :index, :boolean, default: false
    field :view, :boolean, default: false
    field :download, :boolean, default: false
    field :approve, :boolean, default: false
    field :activate, :boolean, default: false
    field :debug, :boolean, default: false
    field :review, :boolean, default: false
  end

  @doc false
  def changeset(actions, attrs) do
    actions
    |> cast(attrs, [
      :create,
      :delete,
      :update,
      :index,
      :view,
      :download,
      :approve,
      :activate,
      :debug,
      :review
    ])
  end
end
