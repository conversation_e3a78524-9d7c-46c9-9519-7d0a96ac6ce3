defmodule ServiceManager.Schemas.Tracking.UserTracking do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Accounts.User
  alias ServiceManager.WalletAccounts.WalletUser

  @derive {Jason.Encoder, only: [
    :id,
    :user_id,
    :wallet_user_id,
    :page_path,
    :page_name,
    :section,
    :action,
    :device_id,
    :device_name,
    :ip_address,
    :user_agent,
    :session_id,
    :duration_seconds,
    :entry_timestamp,
    :exit_timestamp,
    :previous_page_path,
    :next_page_path,
    :status
  ]}

  schema "user_tracking" do
    # User identification
    field :user_id, :integer
    field :wallet_user_id, :integer
    field :session_id, :string

    # Page/location information
    field :page_path, :string
    field :page_name, :string
    field :section, :string
    field :action, :string
    field :previous_page_path, :string
    field :next_page_path, :string

    # Device and connection information
    field :device_id, :string
    field :device_name, :string
    field :ip_address, :string
    field :user_agent, :string

    # Timing information
    field :entry_timestamp, :utc_datetime
    field :exit_timestamp, :utc_datetime
    field :duration_seconds, :integer

    # Status information
    field :status, :string, default: "active"

    # Additional metadata
    field :metadata, :map, default: %{}

    # Relationships
    belongs_to :user, User, foreign_key: :user_id, define_field: false
    belongs_to :wallet_user, WalletUser, foreign_key: :wallet_user_id, define_field: false

    timestamps(type: :utc_datetime)
  end

  @required_fields ~w(page_path entry_timestamp)a
  @optional_fields ~w(
    user_id wallet_user_id session_id page_name section action
    device_id device_name ip_address user_agent exit_timestamp
    duration_seconds previous_page_path next_page_path status metadata
  )a

  @doc """
  Creates a changeset for tracking a user's position in the application.
  """
  def changeset(user_tracking, attrs) do
    user_tracking
    |> cast(attrs, @required_fields ++ @optional_fields)
    |> validate_required(@required_fields)
    |> validate_user_identification()
    |> validate_inclusion(:status, ~w(active inactive completed abandoned))
    |> calculate_duration()
  end

  @doc """
  Creates a changeset for entry tracking (when a user enters a page).
  """
  def entry_changeset(user_tracking, attrs) do
    attrs = Map.put(attrs, "entry_timestamp", DateTime.utc_now())

    user_tracking
    |> cast(attrs, @required_fields ++ @optional_fields)
    |> validate_required(@required_fields)
    |> validate_user_identification()
  end

  @doc """
  Creates a changeset for exit tracking (when a user leaves a page).
  """
  def exit_changeset(user_tracking, attrs) do
    exit_time = DateTime.utc_now()
    attrs = Map.put(attrs, "exit_timestamp", exit_time)

    user_tracking
    |> cast(attrs, [:exit_timestamp, :next_page_path, :status])
    |> validate_required([:exit_timestamp])
    |> calculate_duration()
  end

  # Private functions

  # Ensures that at least one user identification field is provided
  defp validate_user_identification(changeset) do
    case get_field(changeset, :user_id) || get_field(changeset, :wallet_user_id) || get_field(changeset, :session_id) do
      nil -> add_error(changeset, :user_identification, "at least one of user_id, wallet_user_id, or session_id must be provided")
      _ -> changeset
    end
  end

  # Calculates the duration between entry and exit timestamps
  defp calculate_duration(changeset) do
    entry_timestamp = get_field(changeset, :entry_timestamp)
    exit_timestamp = get_field(changeset, :exit_timestamp)

    if entry_timestamp && exit_timestamp do
      duration_seconds = DateTime.diff(exit_timestamp, entry_timestamp)
      put_change(changeset, :duration_seconds, duration_seconds)
    else
      changeset
    end
  end
end