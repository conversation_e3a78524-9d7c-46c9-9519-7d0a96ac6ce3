defmodule ServiceManager.WalletAccounts.WalletTokenBk do
  use Endon
  use Ecto.Schema
  import Ecto.Changeset

  schema "wallet_tokens" do
    field :token, :string
    field :user_id, :integer
    field :expires_at, :utc_datetime
    field :revoked_at, :utc_datetime

    timestamps()
  end

  def changeset(token, attrs) do
    token
    |> cast(attrs, [:token, :user_id, :expires_at, :revoked_at])
    |> validate_required([:token, :user_id, :expires_at])
    |> unique_constraint(:token)
  end
end
