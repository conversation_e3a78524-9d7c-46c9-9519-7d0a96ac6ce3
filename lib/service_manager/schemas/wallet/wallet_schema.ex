defmodule ServiceManager.Schemas.Accounts.WalletAccount do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Accounts.User

  @derive {Jason.Encoder,
           only: [
             :mobile_number,
             :first_name,
             :last_name,
             :holder_id,
             :currency,
             :balance,
             :active,
             :account_number,
             :account_type,
             :last_transaction_date
           ]}
  schema "wallets" do
    field :mobile_number, :string
    field :first_name, :string
    field :last_name, :string
    field :holder_id, :string
    field :currency, :string
    field :balance, :decimal
    field :active, :boolean, default: false
    field :account_number, :string, virtual: true
    field :account_type, :string, virtual: true
    field :last_transaction_date, :naive_datetime, virtual: true

    timestamps()
  end

  def changeset(bank_account, attrs) do
    bank_account
    |> cast(attrs, [
      :mobile_number,
      :first_name,
      :last_name,
      :holder_id,
      :currency,
      :balance,
      :active
    ])
    |> validate_required([:name, :number, :type, :currency, :balance, :user_id])
    |> unique_constraint([:mobile_number, :holder_id])
  end
end
