defmodule ServiceManager.Schemas.DeletedArchive do
  use Ecto.Schema
  import Ecto.Changeset

  schema "deleted_archive" do
    field :item, :string
    field :item_id, :integer
    field :description, :string
    field :data, :map

    belongs_to :created, ServiceManager.SystemUser, foreign_key: :created_by, type: :id
    belongs_to :updated, ServiceManager.SystemUser, foreign_key: :updated_by, type: :id

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(deleted_archive, attrs) do
    deleted_archive
    |> cast(attrs, [:item, :item_id, :description, :data, :created_by, :updated_by])
    |> validate_required([:item, :item_id])
    |> foreign_key_constraint(:created_by)
    |> foreign_key_constraint(:updated_by)
  end
end
