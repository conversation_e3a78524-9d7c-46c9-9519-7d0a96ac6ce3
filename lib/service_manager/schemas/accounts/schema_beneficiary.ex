defmodule ServiceManager.Schemas.Accounts.SchemaBeneficiary do
  use Ecto.Schema
  import Ecto.Changeset

  schema "beneficiaries" do
    field :name, :string
    field :account_number, :string
    field :bank_code, :string
    field :currency, :string
    field :beneficiary_type, :string
    field :description, :string
    field :status, :string, default: "active"
    field :is_default, :boolean, default: false
    field :wallet_id, :id

    belongs_to :user, ServiceManager.Accounts.User

    timestamps()
  end

  @required_fields ~w(name account_number bank_code beneficiary_type)a
  @optional_fields ~w(currency description status is_default user_id wallet_id)a

  def changeset(beneficiary, attrs) do
    beneficiary
    |> cast(attrs, @required_fields ++ @optional_fields)
    |> validate_required(@required_fields)
    |> validate_length(:name, min: 2, max: 255)
    # IBAN can be up to 34 characters
    |> validate_length(:account_number, min: 5, max: 34)
    # SWIFT/BIC codes are 8 or 11 characters
    |> validate_length(:bank_code, min: 3, max: 15)
    |> validate_inclusion(:status, ~w(active inactive suspended))
    |> validate_inclusion(:beneficiary_type, ~w(wallet mobile_banking other))
    |> unique_constraint([:account_number, :user_id, :beneficiary_type],
      name: :beneficiaries_account_user_type_index
    )
    |> unsafe_validate_unique(
      [:account_number, :wallet_id, :beneficiary_type],
      ServiceManager.Repo
    )
  end
end
