defmodule ServiceManager.Accounts.FundAccounts do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :email,
             :account_number,
             :balance,
             :working_balance,
             :cleared_balance,
             :currency,
             :account_type,
             :status,
             :last_transaction_date,
             :is_frozen,
             :is_hidden,
             :nickname,
             :bank_name,
             :bank_code,
             :branch_code,
             :enable_alerts,
             :large_transaction_alert,
             :large_transaction_threshold,
             :low_balance_alert,
             :low_balance_threshold,
             :suspicous_activity_alert,
             :suspicous_activity_seconds_between_transactions
           ]}
  schema "fund_accounts" do
    field :email, :string, default: ""
    field :account_number, :string, default: ""
    field :balance, :decimal, default: Decimal.new(0)
    field :working_balance, :decimal, default: Decimal.new(0)
    field :cleared_balance, :decimal, default: Decimal.new(0)
    field :currency, :string, default: "MWK"
    field :account_type, :string, default: "CURRENT"
    field :status, :string, default: "ACTIVE"
    field :is_frozen, :boolean, default: false
    field :is_hidden, :boolean, default: false
    field :nickname, :string, default: ""
    field :bank_name, :string, default: ""
    field :bank_code, :string, default: ""
    field :branch_code, :string, default: ""
    field :enable_alerts, :boolean, default: false
    field :frozen, :boolean, default: false
    field :hidden, :boolean, default: false
    field :large_transaction_alert, :boolean, default: false
    field :large_transaction_threshold, :decimal, default: Decimal.new(0)
    field :low_balance_alert, :boolean, default: false
    field :low_balance_threshold, :decimal, default: Decimal.new(0)
    field :suspicous_activity_alert, :boolean, default: false
    field :suspicous_activity_seconds_between_transactions, :integer, default: 0
    field :last_transaction_date, :naive_datetime
    belongs_to :user, ServiceManager.Accounts.User
    belongs_to :owner, ServiceManager.Accounts.User
    # has_many :cards, ServiceManager.Accounts.Card

    timestamps()
  end

  def changeset(user_account, attrs) do
    user_account
    |> cast(attrs, [
      :user_id,
      :owner_id,
      :email,
      :account_number,
      :balance,
      :working_balance,
      :cleared_balance,
      :currency,
      :account_type,
      :status,
      :last_transaction_date,
      :is_frozen,
      :is_hidden,
      :nickname,
      :hidden,
      :frozen
    ])
    |> validate_required([:account_number, :balance, :currency, :account_type, :status])
    |> unique_constraint(:account_number)
  end
end
