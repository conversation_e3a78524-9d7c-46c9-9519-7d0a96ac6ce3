defmodule ServiceManager.Schemas.Accounts.Cheques.ChequeRequest do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Accounts.User

  @actions ["cancel", "stop"]
  @status_types ["pending", "approved", "rejected"]

  @derive {Jason.Encoder,
           only: [
             :action,
             :cheque_number,
             :account_number,
             :status,
             :processed_at,
             :processed_by,
             :remarks,
             :inserted_at,
             :updated_at
           ]}
  schema "cheque_requests" do
    field :action, :string
    field :cheque_number, :string
    field :account_number, :string
    field :status, :string, default: "pending"
    field :processed_at, :utc_datetime
    field :processed_by, :string
    field :remarks, :string

    belongs_to :user, User

    timestamps(type: :utc_datetime)
  end

  def changeset(cheque_request, attrs) do
    cheque_request
    |> cast(attrs, [
      :action,
      :cheque_number,
      :account_number,
      :status,
      :processed_at,
      :processed_by,
      :remarks,
      :user_id
    ])
    |> validate_required([:action, :cheque_number, :account_number])
    |> validate_inclusion(:action, @actions)
    |> validate_inclusion(:status, @status_types)
    |> validate_cheque_number()
    |> validate_account_number()
    |> foreign_key_constraint(:user_id)
  end

  def update_status_changeset(cheque_request, attrs) do
    cheque_request
    |> cast(attrs, [:status, :processed_at, :processed_by, :remarks])
    |> validate_required([:status, :processed_by])
    |> validate_inclusion(:status, @status_types)
    |> validate_required_remarks_on_status_change()
  end

  defp validate_cheque_number(changeset) do
    validate_format(changeset, :cheque_number, ~r/^\d+$/, message: "must contain only numbers")
  end

  defp validate_account_number(changeset) do
    validate_format(changeset, :account_number, ~r/^\d+$/, message: "must contain only numbers")
  end

  defp validate_required_remarks_on_status_change(changeset) do
    case get_change(changeset, :status) do
      nil ->
        changeset

      _ ->
        validate_required(changeset, [:remarks], message: "must be provided when changing status")
    end
  end
end
