defmodule ServiceManager.Schemas.Accounts.Cheques.ChequeBookRequest do
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :uid,
             :account_number,
             :branch_code,
             :number_of_leaves,
             :request_status,
             :issued_at,
             :fulfilled_at,
             :inserted_at,
             :updated_at
           ]}
  schema "cheque_book_requests" do
    field :uid, Ecto.UUID, autogenerate: true
    field :account_number, :string
    field :request_status, :string, default: "pending"
    field :number_of_leaves, :integer, default: 25
    field :request_reason, :string
    field :rejection_reason, :string
    field :issued_at, :utc_datetime
    field :fulfilled_at, :utc_datetime
    field :request_reference, :string
    field :branch_code, :string

    belongs_to :user, ServiceManager.Accounts.User

    timestamps(type: :utc_datetime)
  end

  def changeset(cheque_book_request, attrs) do
    cheque_book_request
    |> cast(attrs, [
      :uid,
      :account_number,
      :request_status,
      :number_of_leaves,
      :request_reason,
      :rejection_reason,
      :issued_at,
      :fulfilled_at,
      :request_reference,
      :user_id
    ])
    |> validate_required([:account_number, :number_of_leaves])
    |> validate_inclusion(:request_status, ["pending", "approved", "rejected", "fulfilled"])
    |> validate_number(:number_of_leaves, greater_than: 0)
    |> validate_length(:request_reason, min: 3, max: 500)
    |> maybe_generate_uid()
    |> maybe_generate_reference()
  end

  defp maybe_generate_uid(changeset) do
    case get_field(changeset, :uid) do
      nil -> put_change(changeset, :uid, Ecto.UUID.generate())
      _ -> changeset
    end
  end

  defp maybe_generate_reference(changeset) do
    case get_field(changeset, :request_reference) do
      nil -> put_change(changeset, :request_reference, generate_reference())
      _ -> changeset
    end
  end

  defp generate_reference do
    "CHQ" <> (Enum.random(100_000..999_999) |> Integer.to_string())
  end
end
