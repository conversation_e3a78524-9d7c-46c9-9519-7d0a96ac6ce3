defmodule ServiceManager.Accounts.BalanceSnapshot do
  @moduledoc """
  Schema for tracking account balance snapshots.
  Records balance snapshots before and after transactions to provide a complete audit trail.
  """
  use Ecto.Schema
  import Ecto.Changeset
  use Endon

  @derive {Jason.Encoder,
           only: [
             :account_id,
             :transaction_id,
             :balance_type,
             :balance,
             :working_balance,
             :cleared_balance,
             :currency,
             :snapshot_reason,
             :inserted_at
           ]}

  schema "balance_snapshots" do
    field :account_id, :integer
    field :transaction_id, :integer
    field :balance_type, :string  # "pre" or "post"
    field :balance, :decimal
    field :working_balance, :decimal
    field :cleared_balance, :decimal
    field :currency, :string
    field :snapshot_reason, :string  # "transaction", "reversal", "adjustment", etc.
    
    timestamps()
  end

  @doc false
  def changeset(snapshot, attrs) do
    snapshot
    |> cast(attrs, [
      :account_id, 
      :transaction_id, 
      :balance_type,
      :balance,
      :working_balance,
      :cleared_balance,
      :currency,
      :snapshot_reason
    ])
    |> validate_required([:account_id, :balance_type, :balance])
    |> validate_inclusion(:balance_type, ["pre", "post"])
  end
end
