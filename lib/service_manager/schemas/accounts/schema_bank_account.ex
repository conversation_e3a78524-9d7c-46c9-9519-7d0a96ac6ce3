defmodule ServiceManager.Schemas.Accounts.BankAccount do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Accounts.User

  @derive {Jason.Encoder, only: [:tag, :name, :number, :type, :currency, :balance, :user_id]}
  schema "bank_accounts" do
    field :tag, :string
    field :name, :string
    field :number, :string
    field :type, :string
    field :currency, :string
    field :balance, :decimal

    belongs_to :user, ServiceManager.Accounts.User

    timestamps()
  end

  def changeset(bank_account, attrs) do
    bank_account
    |> cast(attrs, [:tag, :name, :number, :type, :currency, :balance, :user_id])
    |> validate_required([:name, :number, :type, :currency, :balance, :user_id])
    |> unique_constraint(:number)
  end
end
