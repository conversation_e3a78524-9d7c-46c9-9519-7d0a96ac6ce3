defmodule ServiceManager.Transfers.Transfer do
  use Ecto.Schema
  import Ecto.Changeset

  schema "transfers" do
    field :amount, :decimal
    field :currency, :string
    field :description, :string
    field :transfer_type, :string
    field :bank_code, :string
    field :schedule_date, :date
    field :recurring_frequency, :string
    field :status, :string
    field :fee, :decimal
    field :exchange_rate, :decimal
    field :total_amount, :decimal

    # belongs_to :from_account, BankingApp.Accounts.BankAccount
    # belongs_to :to_account, BankingApp.Accounts.BankAccount
    # belongs_to :beneficiary, BankingApp.Beneficiaries.Beneficiary

    timestamps()
  end

  def changeset(transfer, attrs) do
    transfer
    |> cast(attrs, [
      :amount,
      :currency,
      :description,
      :transfer_type,
      :bank_code,
      :schedule_date,
      :recurring_frequency,
      :status,
      :fee,
      :exchange_rate,
      :total_amount,
      :from_account_id,
      :to_account_id,
      :beneficiary_id
    ])
    |> validate_required([:amount, :currency, :transfer_type, :status, :from_account_id])
  end
end
