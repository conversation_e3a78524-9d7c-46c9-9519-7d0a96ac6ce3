defmodule ServiceManager.Schemas.Accounts.SchemaCard do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @derive {Jason.Encoder,
           only: [
             :card_number,
             :card_type,
             :expiry_date,
             :status,
             :currency,
             :daily_limit,
             :monthly_limit
           ]}

  schema "cards" do
    field :card_number, :string
    # VISA, MASTERCARD, etc.
    field :card_type, :string
    field :expiry_date, :date
    field :cvv, :string
    field :pin_hash, :string
    # active, blocked, expired
    field :status, :string, default: "active"
    field :currency, :string
    field :daily_limit, :decimal
    field :monthly_limit, :decimal
    field :last_used_at, :utc_datetime
    # pending, activated
    field :activation_status, :string, default: "pending"
    field :pin_tries, :integer, default: 0
    field :is_virtual, :boolean, default: false
    field :metadata, :map, default: %{}

    # Relationships
    belongs_to :user, ServiceManager.Accounts.User
    belongs_to :wallet_user, ServiceManager.WalletAccounts.WalletUser
    belongs_to :beneficiary, ServiceManager.Schemas.Accounts.SchemaBeneficiary
    belongs_to :third_party_api_key, ServiceManager.ThirdParty.ThirdPartyApiKey

    timestamps(type: :utc_datetime)
  end

  @required_fields ~w(card_type currency daily_limit monthly_limit)a
  @optional_fields ~w(status activation_status pin_tries is_virtual metadata last_used_at)a

  def changeset(card, attrs) do
    card
    |> cast(attrs, @required_fields ++ @optional_fields)
    |> validate_required(@required_fields)
    |> validate_inclusion(:card_type, ~w(visa mastercard))
    |> validate_inclusion(:status, ~w(active blocked expired))
    |> validate_inclusion(:activation_status, ~w(pending activated))
    |> validate_number(:daily_limit, greater_than: 0)
    |> validate_number(:monthly_limit, greater_than: 0)
    |> maybe_generate_card_details()
  end

  def update_changeset(card, attrs) do
    card
    |> cast(attrs, [:status, :daily_limit, :monthly_limit, :activation_status, :pin_tries])
    |> validate_inclusion(:status, ~w(active blocked expired))
    |> validate_number(:daily_limit, greater_than: 0)
    |> validate_number(:monthly_limit, greater_than: 0)
  end

  def validate_pin_changeset(card, attrs) do
    card
    |> cast(attrs, [:pin_hash, :pin_tries, :activation_status])
    |> validate_required([:pin_hash])
  end

  defp maybe_generate_card_details(changeset) do
    if !get_field(changeset, :card_number) do
      # 5 years from now
      expiry_date = Date.utc_today() |> Date.add(1825)
      card_number = generate_card_number(get_field(changeset, :card_type))
      cvv = generate_cvv()

      changeset
      |> put_change(:card_number, card_number)
      |> put_change(:expiry_date, expiry_date)
      |> put_change(:cvv, cvv)
    else
      changeset
    end
  end

  defp generate_card_number(card_type) when card_type in ["visa", "mastercard"] do
    # Generate a valid card number using Luhn algorithm
    prefix =
      case card_type do
        "visa" -> "4"
        "mastercard" -> "5"
      end

    rest = for _ <- 1..15, into: "", do: Integer.to_string(Enum.random(0..9))
    number = prefix <> rest

    # Calculate check digit using Luhn algorithm
    sum =
      number
      |> String.graphemes()
      |> Enum.reverse()
      |> Enum.with_index()
      |> Enum.map(fn {digit, i} ->
        num = String.to_integer(digit)

        if rem(i, 2) == 1 do
          doubled = num * 2
          if doubled > 9, do: doubled - 9, else: doubled
        else
          num
        end
      end)
      |> Enum.sum()

    check_digit = rem(10 - rem(sum, 10), 10)
    number <> Integer.to_string(check_digit)
  end

  defp generate_card_number(_), do: nil

  defp generate_cvv do
    for _ <- 1..3, into: "", do: Integer.to_string(Enum.random(0..9))
  end

  def verify_pin(card, pin) do
    case Bcrypt.verify_pass(pin, card.pin_hash) do
      true ->
        {:ok, card}

      false ->
        # Increment pin tries
        card =
          card
          |> update_changeset(%{pin_tries: card.pin_tries + 1})
          |> ServiceManager.Repo.update!()

        # Block card if max tries exceeded
        if card.pin_tries >= 3 do
          card
          |> update_changeset(%{status: "blocked"})
          |> ServiceManager.Repo.update!()
        end

        {:error, :invalid_pin}
    end
  end
end
