defmodule ServiceManager.Schemas.T24Log do
  use Ecto.Schema
  import Ecto.Changeset

  schema "t_24_logs" do
    field :request, :string
    field :response, :string
    field :url, :string
    field :headers, :string
    field :message_id, :string
    field :method, :string

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(t24_log, attrs) do
    t24_log
    |> cast(attrs, [:message_id, :url, :method, :headers, :request, :response])
  end
end
