defmodule ServiceManager.Schemas.CallbackRegistry do
  use Ecto.Schema
  import Ecto.Changeset

  schema "callback_registry" do
    field :callback_url, :string
    field :api_key, :string
    field :secret_key, :string
    # active, inactive
    field :status, :string, default: "active"
    # transaction, wallet
    field :callback_type, :string
    field :retry_count, :integer, default: 0
    field :last_called_at, :utc_datetime
    field :metadata, :map, default: %{}
    field :headers, :map, default: %{}
    field :validation_scheme, :map, default: %{}

    timestamps()
  end

  @doc false
  def changeset(callback_registry, attrs) do
    callback_registry
    |> cast(attrs, [
      :callback_url,
      :api_key,
      :secret_key,
      :status,
      :callback_type,
      :retry_count,
      :last_called_at,
      :metadata,
      :headers,
      :validation_scheme
    ])
    |> validate_required([:callback_url, :callback_type])
    |> validate_inclusion(:status, ["active", "inactive"])
    |> validate_inclusion(:callback_type, ["transaction", "wallet"])
    |> validate_url(:callback_url)
    |> validate_schema()
  end

  @doc """
  Validates the validation_scheme map structure and field mappings.
  """
  def validate_schema(changeset) do
    case get_change(changeset, :validation_scheme) do
      nil ->
        changeset

      scheme ->
        case ServiceManager.Validators.CallbackSchemaValidator.validate_schema(
               scheme,
               get_field(changeset, :callback_type)
             ) do
          {:ok, _} -> changeset
          {:error, reason} -> add_error(changeset, :validation_scheme, reason)
        end
    end
  end

  defp validate_url(changeset, field) do
    validate_change(changeset, field, fn _, url ->
      case URI.parse(url) do
        %URI{scheme: scheme, host: host} when not is_nil(scheme) and not is_nil(host) -> []
        _ -> [{field, "must be a valid URL"}]
      end
    end)
  end
end
