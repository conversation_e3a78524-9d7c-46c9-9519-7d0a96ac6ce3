defmodule ServiceManager.Schemas.Callback do
  use Ecto.Schema
  import Ecto.Changeset

  schema "callbacks" do
    field :callback_type, :string
    field :callback_url, :string
    field :request_headers, :map
    field :request_body, :map
    field :response_status, :integer
    field :response_body, :map
    field :response_headers, :map
    field :duration_ms, :integer
    # pending, success, failed
    field :status, :string, default: "pending"
    field :error_message, :string
    field :retry_count, :integer, default: 0
    field :next_retry_at, :utc_datetime
    field :metadata, :map, default: %{}

    belongs_to :callback_registry, ServiceManager.Schemas.CallbackRegistry

    timestamps()
  end

  @doc false
  def changeset(callback, attrs) do
    callback
    |> cast(attrs, [
      :callback_type,
      :callback_url,
      :request_headers,
      :request_body,
      :response_status,
      :response_body,
      :response_headers,
      :duration_ms,
      :status,
      :error_message,
      :retry_count,
      :next_retry_at,
      :metadata,
      :callback_registry_id
    ])
    |> validate_required([
      :callback_type,
      :callback_url,
      :request_headers,
      :request_body,
      :status
    ])
    |> validate_inclusion(:status, ["pending", "success", "failed"])
    |> foreign_key_constraint(:callback_registry_id)
  end
end
