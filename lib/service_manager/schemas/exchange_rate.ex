defmodule ServiceManager.Schemas.ExchangeRate do
  use Ecto.Schema
  import Ecto.Changeset

  schema "exchange_rates" do
    field :from_currency_code, :string
    field :to_currency_code, :string
    field :rate, :decimal

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(exchange_rate, attrs) do
    exchange_rate
    |> cast(attrs, [:from_currency_code, :to_currency_code, :rate])
    |> validate_required([:from_currency_code, :to_currency_code, :rate])
  end
end
