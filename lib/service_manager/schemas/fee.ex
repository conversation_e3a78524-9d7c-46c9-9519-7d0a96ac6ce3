defmodule ServiceManager.Schemas.Fee do
  use Ecto.Schema
  import Ecto.Changeset

  schema "fees_and_charges" do
    field :code, :string
    field :name, :string
    field :status, :string, default: "inactive"
    field :description, :string
    field :amount, :decimal
    field :currency_code, :string
    field :charge_type, :string
    field :effective_date, :date
    field :expiration_date, :date
    field :is_feature, :boolean, default: false
    field :created_by, :integer
    field :updated_by, :integer

    # New fields
    field :category, :string
    field :calculation_method, :string
    field :percentage_rate, :decimal
    field :account_type, :string
    field :notification_enabled, :boolean, default: false
    field :notification_days_before, :integer
    field :comparison_data, :map, default: %{}
    field :min_amount, :decimal
    field :max_amount, :decimal
    field :frequency, :string
    field :application_time, :string
    field :transaction_type, :string
    field :conditions, :map, default: %{}
    field :exchange_rate_source, :string

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(fee, attrs) do
    fee
    |> cast(attrs, [
      :code,
      :name,
      :description,
      :amount,
      :currency_code,
      :charge_type,
      :effective_date,
      :expiration_date,
      :is_feature,
      :status,
      :created_by,
      :updated_by,
      :category,
      :calculation_method,
      :percentage_rate,
      :account_type,
      :notification_enabled,
      :notification_days_before,
      :comparison_data,
      :min_amount,
      :max_amount,
      :frequency,
      :application_time,
      :transaction_type,
      :conditions,
      :exchange_rate_source
    ])
    |> validate_required([
      :name,
      :description,
      :currency_code,
      :charge_type,
      :category,
      :calculation_method,
      :transaction_type
    ])
    |> validate_calculation_method()
    |> validate_code()
  end

  defp validate_code(changeset) do
    changeset
    |> validate_required([:code])
    |> validate_length(:code, max: 25)
    |> unsafe_validate_unique(:code, ServiceManager.Repo)
    |> unique_constraint(:code)
  end

  defp validate_calculation_method(changeset) do
    calculation_method = get_field(changeset, :calculation_method)

    case calculation_method do
      "percentage" ->
        changeset
        |> validate_required([:percentage_rate])
        |> validate_number(:percentage_rate, greater_than: 0, less_than_or_equal_to: 100)

      "flat_rate" ->
        changeset
        |> validate_required([:amount])
        |> validate_number(:amount, greater_than: 0)

      _ ->
        add_error(changeset, :calculation_method, "must be either percentage or flat_rate")
    end
  end
end
