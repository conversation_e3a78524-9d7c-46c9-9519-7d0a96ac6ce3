defmodule ServiceManager.Schemas.Merchant do
  use Ecto.Schema
  import Ecto.Changeset

  schema "merchants" do
    field :name, :string
    field :email, :string
    field :address, :string
    field :phone_number, :string
    field :is_active, :boolean, default: true
    field :merchant_code, :string
    field :balance, :float, default: 0.0

    has_many :transactions, ServiceManager.Schemas.MerchantTransaction

    timestamps()
  end

  @doc false
  def changeset(merchant, attrs) do
    merchant
    |> cast(attrs, [:name, :email, :address, :phone_number, :is_active, :merchant_code, :balance])
    |> validate_required([:name, :email, :merchant_code])
    |> validate_format(:email, ~r/^[^\s]+@[^\s]+$/, message: "must have the @ sign and no spaces")
    |> validate_length(:email, max: 160)
    |> unique_constraint(:email)
    |> unique_constraint(:merchant_code)
  end

  @doc """
  Generates a random merchant code.
  """
  def generate_merchant_code do
    Enum.random(10_000_000..99_999_999) |> Integer.to_string()
  end

  @spec balance_changeset(
          %{
            :__struct__ => atom() | %{:__changeset__ => map(), optional(any()) => any()},
            :balance => false | nil | binary() | integer() | Decimal.t(),
            optional(atom()) => any()
          },
          binary() | integer() | Decimal.t()
        ) :: Ecto.Changeset.t()
  @doc """
  Changeset for updating merchant balance
  """
  def balance_changeset(merchant, amount) do
    new_balance =
      Decimal.add(merchant.balance |> Decimal.from_float(), amount) |> Decimal.to_float()

    merchant
    |> cast(%{balance: new_balance}, [:balance])
    |> validate_required([:balance])
  end
end
