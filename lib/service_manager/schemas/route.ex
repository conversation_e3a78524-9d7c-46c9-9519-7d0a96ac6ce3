defmodule ServiceManager.Schemas.Route do
  use Ecto.Schema
  import Ecto.Changeset

  schema "routes" do
    field :name, :string
    field :host, :string
    field :path, :string
    field :method, :string

    timestamps()
  end

  @required_fields [:name, :host, :path, :method]

  def changeset(route, attrs) do
    route
    |> cast(attrs, @required_fields)
    |> validate_required(@required_fields)
    |> unique_constraint(:name)
    |> validate_inclusion(:method, ["GET", "POST", "PUT", "DELETE", "PATCH"])
  end

  def create_changeset(attrs) do
    changeset(%__MODULE__{}, attrs)
  end
end
