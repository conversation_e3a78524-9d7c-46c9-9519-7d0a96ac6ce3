defmodule ServiceManager.Schemas.Billers.BillerTransaction do
  @moduledoc """
  Schema for biller transactions handling various biller types and operations
  """
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :biller_type,
             :biller_name,
             :account_number,
             :our_transaction_id,
             :amount,
             :currency,
             :credit_account,
             :credit_account_type,
             :debit_account,
             :debit_account_type,
             :customer_account_number,
             :customer_account_name,
             :transaction_type,
             :account_type,
             :bundle_id,
             :status,
             :api_endpoint,
             :request_payload,
             :response_payload,
             :error_message,
             :processed_at,
             :inserted_at,
             :updated_at
           ]}

  schema "biller_transactions" do
    # Biller Information
    field :biller_type, :string
    field :biller_name, :string
    
    # Account Information
    field :account_number, :string
    field :account_type, :string
    
    # Transaction Details
    field :our_transaction_id, :string
    field :amount, :decimal
    field :currency, :string, default: "MWK"
    
    # Account References
    field :credit_account, :string
    field :credit_account_type, :string
    field :debit_account, :string
    field :debit_account_type, :string
    
    # Customer Information
    field :customer_account_number, :string
    field :customer_account_name, :string
    
    # Transaction Type (account_details, post_transaction, get_invoice, confirm_invoice, bundle_details, confirm_bundle)
    field :transaction_type, :string
    
    # Specific fields for certain billers
    field :bundle_id, :string  # For TNM bundles
    
    # Status and Processing
    field :status, :string, default: "pending"
    
    # API Information
    field :api_endpoint, :string
    field :request_payload, :map
    field :response_payload, :map
    field :error_message, :string
    
    # Timestamps
    field :processed_at, :utc_datetime
    timestamps()
  end

  @doc false
  def changeset(biller_transaction, attrs) do
    biller_transaction
    |> cast(attrs, [
      :biller_type,
      :biller_name,
      :account_number,
      :account_type,
      :our_transaction_id,
      :amount,
      :currency,
      :credit_account,
      :credit_account_type,
      :debit_account,
      :debit_account_type,
      :customer_account_number,
      :customer_account_name,
      :transaction_type,
      :bundle_id,
      :status,
      :api_endpoint,
      :request_payload,
      :response_payload,
      :error_message,
      :processed_at
    ])
    |> validate_required([
      :biller_type,
      :biller_name,
      :account_number,
      :our_transaction_id,
      :transaction_type,
      :status
    ])
    |> validate_inclusion(:biller_type, [
      "register_general",
      "bwb_postpaid",
      "lwb_postpaid",
      "srwb_postpaid",
      "srwb_prepaid",
      "masm",
      "airtel_validation",
      "tnm_bundles"
    ])
    |> validate_inclusion(:transaction_type, [
      "account_details",
      "post_transaction",
      "get_invoice",
      "confirm_invoice",
      "bundle_details",
      "confirm_bundle",
      "validation"
    ])
    |> validate_inclusion(:status, [
      "pending",
      "processing",
      "completed",
      "failed",
      "cancelled"
    ])
    |> validate_inclusion(:currency, ["MWK", "USD", "EUR", "GBP", "ZAR"])
    |> validate_inclusion(:credit_account_type, ["account", "wallet"])
    |> validate_inclusion(:debit_account_type, ["account", "wallet"])
    |> validate_number(:amount, greater_than: 0)
    |> unique_constraint(:our_transaction_id)
  end

  @doc """
  Changeset for account details/validation transactions
  """
  def account_details_changeset(biller_transaction, attrs) do
    biller_transaction
    |> changeset(attrs)
    |> put_change(:transaction_type, "account_details")
    |> validate_required([:account_number])
  end

  @doc """
  Changeset for payment transactions
  """
  def payment_changeset(biller_transaction, attrs) do
    biller_transaction
    |> changeset(attrs)
    |> put_change(:transaction_type, "post_transaction")
    |> validate_required([
      :amount,
      :credit_account,
      :debit_account,
      :customer_account_number,
      :customer_account_name
    ])
  end

  @doc """
  Changeset for invoice transactions
  """
  def invoice_changeset(biller_transaction, attrs) do
    biller_transaction
    |> changeset(attrs)
    |> validate_required([:account_number])
    |> validate_inclusion(:transaction_type, ["get_invoice", "confirm_invoice"])
  end

  @doc """
  Changeset for bundle transactions
  """
  def bundle_changeset(biller_transaction, attrs) do
    biller_transaction
    |> cast(attrs, [
      :biller_type,
      :biller_name,
      :account_number,
      :account_type,
      :our_transaction_id,
      :amount,
      :currency,
      :credit_account,
      :credit_account_type,
      :debit_account,
      :debit_account_type,
      :customer_account_number,
      :customer_account_name,
      :transaction_type,
      :bundle_id,
      :status,
      :api_endpoint,
      :request_payload,
      :response_payload,
      :error_message,
      :processed_at
    ])
    |> validate_required([
      :biller_type,
      :biller_name,
      :our_transaction_id,
      :transaction_type,
      :status,
      :bundle_id
    ])
    |> validate_inclusion(:biller_type, [
      "register_general",
      "bwb_postpaid",
      "lwb_postpaid",
      "srwb_postpaid",
      "srwb_prepaid",
      "masm",
      "airtel_validation",
      "tnm_bundles"
    ])
    |> validate_inclusion(:transaction_type, ["bundle_details", "confirm_bundle"])
    |> validate_inclusion(:status, [
      "pending",
      "processing",
      "completed",
      "failed",
      "cancelled"
    ])
    |> validate_inclusion(:currency, ["MWK", "USD", "EUR", "GBP", "ZAR"])
    |> validate_inclusion(:credit_account_type, ["account", "wallet"])
    |> validate_inclusion(:debit_account_type, ["account", "wallet"])
    |> validate_inclusion(:account_type, ["account", "wallet"])
    |> validate_number(:amount, greater_than: 0)
    |> unique_constraint(:our_transaction_id)
  end

  @doc """
  Mark transaction as completed
  """
  def complete_transaction(biller_transaction, response_payload) do
    biller_transaction
    |> changeset(%{
      status: "completed",
      response_payload: response_payload,
      processed_at: DateTime.utc_now()
    })
  end

  @doc """
  Mark transaction as failed
  """
  def fail_transaction(biller_transaction, error_message, response_payload \\ nil) do
    biller_transaction
    |> changeset(%{
      status: "failed",
      error_message: error_message,
      response_payload: response_payload,
      processed_at: DateTime.utc_now()
    })
  end
end