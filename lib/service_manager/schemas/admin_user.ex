defmodule ServiceManager.Schemas.AdminUsers do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :customer_no,
             :updated_at,
             :created_at,
             :name,
             :roles_and_permission_id,
             :nickname,
             :email,
             :account_number,
             :confirmed_at,
             :first_name,
             :last_name,
             :phone_number,
             :date_of_birth,
             :address,
             :approved,
             :city,
             :state,
             :zip,
             :country,
             :account_balance,
             :first_time_login,
             :email_notifications,
             :sms_notifications,
             :push_notifications,
             :profile_picture
           ]}
  schema "tbl_system_users" do
    field :email, :string
    field :name, :string, virtual: true
    field :customer_no, :string
    field :nickname, :string
    field :password, :string, virtual: true, redact: true
    field :hashed_password, :string, redact: true
    field :current_password, :string, virtual: true, redact: true
    field :password_confirmation, :string, virtual: true, redact: true
    field :confirmed_at, :utc_datetime
    field :first_name, :string
    field :last_name, :string
    field :phone_number, :string
    field :date_of_birth, :date
    field :address, :string
    field :city, :string
    field :state, :string
    field :zip, :string
    field :country, :string
    field :approved, :boolean
    field :account_balance, :float, default: 0.0
    field :account_number, :string
    field :first_time_login, :boolean, default: true
    field :created_at, :utc_datetime, virtual: true
    field :currency, :string, virtual: true
    field :account_type, :string, virtual: true
    field :notifications, :string, virtual: true
    field :email_notifications, :boolean, default: true
    field :sms_notifications, :boolean, default: true
    field :push_notifications, :boolean, default: true
    field :profile_picture, :string
    field :memorable_word, :string
    field :otp, :string
    field :status, :string, default: "pending"
    field :activation_status, :string, default: "pending"
    field :reason, :string
    has_many :accounts, ServiceManager.Accounts.FundAccounts

    belongs_to :user_permissions, ServiceManager.Schemas.RolesAndPermission,
      foreign_key: :roles_and_permission_id,
      type: :id

    belongs_to :maker, ServiceManager.Schemas.AdminUsers, foreign_key: :created_by, type: :id
    belongs_to :checker, ServiceManager.Schemas.AdminUsers, foreign_key: :updated_by, type: :id

    timestamps(type: :utc_datetime)
  end

  @doc """
  A user changeset for registration.

  It is important to validate the length of both email and password.
  Otherwise databases may truncate the email without warnings, which
  could lead to unpredictable or insecure behaviour. Long passwords may
  also be very expensive to hash for certain algorithms.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.

    * `:validate_email` - Validates the uniqueness of the email, in case
      you don't want to validate the uniqueness of the email (like when
      using this changeset for validations on a LiveView form before
      submitting the form), this option can be set to `false`.
      Defaults to `true`.
  """

  def changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :nickname,
      :email,
      :currency,
      :account_type,
      :roles_and_permission_id,
      :notifications,
      :password,
      :first_name,
      :last_name,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_number,
      :account_balance,
      :email_notifications,
      :sms_notifications,
      :push_notifications,
      :otp,
      :customer_no,
      :profile_picture
    ])
    |> validate_required([:first_name, :last_name, :roles_and_permission_id])
    |> validate_email(validate_email: true)
    |> validate_password(opts)
    |> validate_phone_number()
    #    |> validate_memorable_word()
    |> maybe_generate_customer_no()
  end

  def update_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :nickname,
      :email,
      :first_name,
      :roles_and_permission_id,
      :last_name,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_number,
      :account_balance,
      :email_notifications,
      :sms_notifications,
      :push_notifications,
      :otp,
      :profile_picture
    ])
    |> validate_required([:first_name, :last_name, :roles_and_permission_id])
    |> validate_email(opts)
    |> validate_phone_number()
  end

  def activate_update_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:password, :status, :activation_status, :updated_by, :approved])
    |> put_change(:approved, true)
    |> validate_password(opts)
  end

  def status_changeset(user, params \\ %{}) do
    user
    |> cast(params, [:updated_by, :status, :reason, :activation_status, :approved])
  end

  def registration_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :nickname,
      :email,
      :password,
      :first_name,
      :last_name,
      :roles_and_permission_id,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_number,
      :account_balance
    ])
    |> validate_email(opts)
    |> validate_password(opts)
  end

  def update_password_changeset(user, attrs) do
    user
    |> cast(attrs, [:password, :first_time_login])
    |> validate_required([:password])
    |> validate_length(:password, min: 12, max: 72)
    |> maybe_hash_password([])
  end

  def update_user(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :nickname,
      :account_number,
      :first_name,
      :last_name,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_balance,
      :approved,
      :first_time_login,
      :memorable_word,
      :otp
    ])
  end

  defp maybe_generate_customer_no(changeset) do
    case get_field(changeset, :customer_no) do
      nil -> put_change(changeset, :customer_no, generate_customer_no())
      _ -> changeset
    end
  end

  defp generate_customer_no do
    "CUS" <> (Enum.random(100_000..999_999) |> Integer.to_string())
  end

  defp validate_email(changeset, opts) do
    changeset
    |> validate_required([:email])
    |> validate_format(:email, ~r/^[^\s]+@[^\s]+$/, message: "must have the @ sign and no spaces")
    |> validate_length(:email, max: 160)
    |> maybe_validate_unique_email(opts)
  end

  defp validate_password(changeset, opts) do
    changeset
    |> validate_required([:password])
    |> validate_length(:password, min: 12, max: 72)
    # Examples of additional password validation:
    |> validate_format(:password, ~r/[a-z]/, message: "at least one lower case character")
    |> validate_format(:password, ~r/[A-Z]/, message: "at least one upper case character")
    |> validate_format(:password, ~r/[!?@#$%^&*_0-9]/,
      message: "at least one digit or punctuation character"
    )
    |> maybe_hash_password(opts)
  end

  defp validate_phone_number(changeset) do
    changeset
    |> validate_required([:phone_number])
    |> unsafe_validate_unique(:phone_number, ServiceManager.Repo)
    |> unique_constraint(:phone_number)
  end

  defp maybe_hash_password(changeset, opts) do
    hash_password? = Keyword.get(opts, :hash_password, true)
    password = get_change(changeset, :password)

    if hash_password? && password && changeset.valid? do
      changeset
      # If using Bcrypt, then further validate it is at most 72 bytes long
      |> validate_length(:password, max: 72, count: :bytes)
      # Hashing could be done with `Ecto.Changeset.prepare_changes/2`, but that
      # would keep the database transaction open longer and hurt performance.
      |> put_change(:hashed_password, Bcrypt.hash_pwd_salt(password))
      # |> put_change(:hashed_password, Pbkdf2.hash_pwd_salt(password))
      |> delete_change(:password)
    else
      changeset
    end
  end

  defp maybe_validate_unique_email(changeset, _opts) do
    changeset
    |> unsafe_validate_unique(:email, ServiceManager.Repo)
    |> unique_constraint(:email)
  end

  defp validate_memorable_word(changeset) do
    changeset
    |> validate_required([:memorable_word])
    |> validate_length(:memorable_word, min: 6, max: 50)
  end

  @doc """
  A user changeset for changing the email.

  It requires the email to change otherwise an error is added.
  """
  def email_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:email])
    |> validate_email(opts)
    |> case do
      %{changes: %{email: _}} = changeset -> changeset
      %{} = changeset -> add_error(changeset, :email, "did not change")
    end
  end

  @doc """
  A user changeset for changing the password.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.
  """
  def password_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:password])
    |> validate_confirmation(:password, message: "does not match password")
    |> validate_password(opts)
  end

  @doc """
  Confirms the account by setting `confirmed_at`.
  """
  def confirm_changeset(user) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)
    change(user, confirmed_at: now)
  end

  @doc """
  Verifies the password.

  If there is no user or the user doesn't have a password, we call
  `Bcrypt.no_user_verify/0` to avoid timing attacks.
  """
  def valid_password?(
        %ServiceManager.Schemas.AdminUsers{hashed_password: hashed_password},
        password
      )
      when is_binary(hashed_password) and byte_size(password) > 0 do
    # Pbkdf2.verify_pass(password, hashed_password)
    Bcrypt.verify_pass(password, hashed_password)
  end

  def valid_password?(_, _) do
    Bcrypt.no_user_verify()
    # Pbkdf2.no_user_verify()
    false
  end

  @doc """
  Validates the current password otherwise adds an error to the changeset.
  """
  def validate_current_password(changeset, password) do
    changeset = cast(changeset, %{current_password: password}, [:current_password])

    if valid_password?(changeset.data, password) do
      changeset
    else
      add_error(changeset, :current_password, "is not valid")
    end
  end

  def change_update_password_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:current_password, :password, :password_confirmation])
    |> validate_required([:current_password, :password, :password_confirmation])
    |> validate_current_password()
    |> validate_password_confirmation()
    |> validate_password_strength()
    |> maybe_hash_password(hash_password: true)
  end

  defp validate_password_strength(changeset) do
    changeset
    |> validate_required([:password])
    |> validate_length(:password, min: 12, max: 72)
    # Examples of additional password validation:
    |> validate_format(:password, ~r/[a-z]/, message: "at least one lower case character")
    |> validate_format(:password, ~r/[A-Z]/, message: "at least one upper case character")
    |> validate_format(:password, ~r/[!?@#$%^&*_0-9]/,
      message: "at least one digit or punctuation character"
    )
  end

  defp validate_current_password(changeset) do
    current_password = get_change(changeset, :current_password)

    if is_nil(current_password) do
      add_error(changeset, :current_password, "must be provided")
    else
      if valid_password?(changeset.data, current_password) do
        changeset
      else
        add_error(changeset, :current_password, "is not valid")
      end
    end
  end

  defp validate_password_confirmation(changeset) do
    password = get_change(changeset, :password)
    current_password = get_change(changeset, :current_password)
    confirmation = get_change(changeset, :password_confirmation)

    cond do
      password == current_password ->
        add_error(changeset, :password, "Please use a new password")

      password != confirmation ->
        add_error(changeset, :password_confirmation, "does not match password")

      true ->
        changeset
    end
  end
end
