defmodule ServiceManagerWeb.DynamicRouteController do
    use ServiceManagerWeb, :controller
    alias ServiceManager.Routing.DynamicRouteManager
    alias ServiceManager.Routing.DynamicRouter
    alias ServiceManager.Routing.DynamicRoute
    alias ServiceManager.Forms.DynamicForm
    alias ServiceManager.Forms.DynamicFormsManager
    alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager

    import Ecto.Query

    @base_path "/dynamic"

    # Process a route with dynamic processes if available
    defp process_route(conn, route, params) do
      # Check if the route has a linked process
      case ProcessManager.get_initial_process(route.id) do
        {:ok, _process} ->
          # Execute the process chain
          case ProcessManager.execute_chain(route.id, params) do
            {:ok, result} ->
              # Process chain executed successfully
              conn
              |> put_status(:ok)
              |> json(%{success: true, route: route.name, result: result})

            {:error, reason} ->
              # Process chain execution failed
              conn
              |> put_status(:internal_server_error)
              |> json(%{success: false, message: "Process execution failed", error: reason})
          end

        {:error, :not_found} ->
          # No process linked to this route, return default success
          conn
          |> put_status(:ok)
          |> json(%{success: true, route: route.name, params: params})
      end
    end

    def handle(conn, _params) do
      # Get the full request path and method
      full_path = conn.request_path
      method = conn.method

      # Remove the base path to get the dynamic part
      dynamic_path = String.replace_prefix(full_path, @base_path, "")

      # If the path is empty, set it to "/"
      dynamic_path = if dynamic_path == "", do: "/", else: dynamic_path

      # Find the route using the find_route function
      case DynamicRouteManager.find_route(method, dynamic_path) do
        {:ok, route} ->
          # First check if the route has any linked forms
          route_forms = DynamicFormsManager.get_route_forms(route.id)

          if Enum.empty?(route_forms) do
            # No forms attached to this route
            conn
            |> put_status(:bad_request)
            |> json(%{success: false, message: "No form attached to this route"})
          else
            # Check if there's a form for this route and method
            case DynamicFormsManager.get_form_for_route(route.id, method) do
              nil ->
                # Check if any forms are required for this route
                case check_if_form_required(route.id) do
                  true ->
                    conn
                    |> put_status(:bad_request)
                    |> json(%{success: false, message: "Incomplete API definition: No form defined for this route method"})

                  false ->
                    # Form not required, proceed with success
                    process_route(conn, route, %{})
                end

              form ->
                # Get the appropriate parameters based on HTTP method
                request_params = get_request_params(conn, method)

                # Validate the request against the form
                case DynamicFormsManager.validate_request(form, request_params) do
                  {:ok, validated_params} ->
                    # Form validation successful, continue with processing
                    process_route(conn, route, validated_params)

                  {:error, errors} ->
                    # Form validation failed
                    conn
                    |> put_status(:unprocessable_entity)
                    |> json(%{success: false, message: "Invalid request", errors: errors})
                end
            end
          end

        {:error, :not_found} ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, message: "Route not found"})
      end
    end

    # Check if any form is required for this route
    defp check_if_form_required(route_id) do
      route_forms = DynamicFormsManager.get_route_forms(route_id)
      Enum.any?(route_forms, fn form -> form.required end)
    end

    # Extract request parameters based on HTTP method
    defp get_request_params(conn, method) do
      case method do
        "GET" -> conn.query_params
        _ ->
          case conn.body_params do
            %Plug.Conn.Unfetched{} -> %{}
            params -> params
          end
      end
    end

    # List Forms
    def forms(conn, _params) do

      forms = DynamicForm.all()

      conn
      |> json(%{
        forms: forms
        })
    end

    # List registered routes
    def dynamic_routes(conn, _params) do
      routes = list_dynamic_routes()

      conn
      |> json(%{
        routes: routes
      })
    end

    defp list_dynamic_routes() do
      query = """
        SELECT
          routes.*,
          forms.id AS form_id,
          forms.name AS form_name,
          forms.description,
          forms.http_method,
          forms.form,
          forms.validation_schema,
          forms.required,
          forms.inserted_at AS form_inserted_at,
          forms.updated_at AS form_updated_at
        FROM dynamic_routes AS routes
        LEFT JOIN dynamic_route_forms AS route_forms ON routes.id = route_forms.route_id
        LEFT JOIN dynamic_forms AS forms ON route_forms.form_id = forms.id
        ORDER BY routes.id
      """

      result = Ecto.Adapters.SQL.query!(ServiceManager.Repo, query, [])

      # Process the raw results into structured format
      result.rows
      |> Enum.map(fn row ->
        # Convert row tuple to map with string keys
        Enum.zip(result.columns, row)
        |> Map.new(fn {k, v} -> {k, v} end)
      end)
      |> Enum.group_by(& &1["id"], & &1)
      |> Enum.map(fn {route_id, rows} ->
        base_route = %{
          "id" => route_id,
          "name" => hd(rows)["name"],
          "method" => hd(rows)["method"],
          "path" => hd(rows)["path"],
          "enabled" => hd(rows)["enabled"],
          "parts" => hd(rows)["parts"],
          "flat_parts" => hd(rows)["flat_parts"],
          "inserted_at" => hd(rows)["inserted_at"],
          "updated_at" => hd(rows)["updated_at"]
        }

        forms = Enum.filter_map(rows, & &1["form_id"], fn form ->
          %{
            name: form["form_name"],
            description: form["description"],
            http_method: form["http_method"],
            form: form["form"],
            validation_schema: form["validation_schema"],
            required: form["required"],
            inserted_at: form["form_inserted_at"],
            updated_at: form["form_updated_at"]
          }
        end)

        Map.put(base_route, :form, forms)
      end)
    end

  end
