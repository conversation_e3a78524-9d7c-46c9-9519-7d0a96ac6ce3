defmodule ServiceManager.Routing.DynamicRoute do
    use Ecto.Schema
    use Endon
    import Ecto.Changeset
    alias ServiceManager.Forms.DynamicForm
  
    @derive {Jason.Encoder, only: [:name, :method, :path]}
    schema "dynamic_routes" do
      field :name, :string
      field :method, :string
      field :path, :string
      field :enabled, :boolean, default: true
      field :parts, {:array, :string}
      field :flat_parts, {:array, :string}
      
      many_to_many :forms, DynamicForm, join_through: "dynamic_route_forms"
  
      timestamps()
    end
  
    def changeset(route, attrs) do
      route
      |> cast(attrs, [:name, :method, :path, :enabled, :parts, :flat_parts])
      |> validate_required([:name, :method, :path, :parts, :flat_parts])
      |> validate_inclusion(:method, ["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"])
      |> unique_constraint([:method, :path])
    end
  end
