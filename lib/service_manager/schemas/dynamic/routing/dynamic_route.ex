defmodule ServiceManager.Routing.DynamicRouter do
    @moduledoc """
    Handles dynamic routing functionality, including path parsing and route matching.
    """
  
    @doc """
    Extracts parts from a path string.
    Returns a tuple with {parts, flat_parts}.
    
    Example:
      extract_parts("/a/dynamic/route")
      => {["/a", "/dynamic", "/route"], ["a", "dynamic", "route"]}
    """
    def extract_parts(path) do
      # Split the path into segments with slashes
      parts = path
              |> String.split("/", trim: true)
              |> Enum.map(fn part -> "/" <> part end)
      
      # Create flat_parts without slashes
      flat_parts = path
                   |> String.split("/", trim: true)
      
      {parts, flat_parts}
    end
  
    @doc """
    Matches a request path against stored dynamic routes.
    Returns the matching route or nil if no match.
    """
    def match_route(method, path, routes) do
      Enum.find(routes, fn route -> 
        route.method == method && route.path == path && route.enabled
      end)
    end
  end
  