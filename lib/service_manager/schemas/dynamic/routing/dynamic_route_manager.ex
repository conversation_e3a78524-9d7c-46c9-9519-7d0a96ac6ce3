defmodule ServiceManager.Routing.DynamicRouteManager do
    alias ServiceManager.Repo
    alias ServiceManager.Routing.DynamicRoute
    alias ServiceManager.Routing.DynamicRouter

    import Ecto.Query
  
    @doc """
    Create a new dynamic route.
    """
    def create_route(name, method, path, enabled \\ true) do
      # Extract parts from the path
      {parts, flat_parts} = DynamicRouter.extract_parts(path)
      
      %DynamicRoute{}
      |> DynamicRoute.changeset(%{
        name: name,
        method: String.upcase(method),
        path: path,
        enabled: enabled,
        parts: parts,
        flat_parts: flat_parts
      })
      |> Repo.insert()
    end
  
    @doc """
    Get all dynamic routes.
    """
    def list_routes do
      Repo.all(DynamicRoute)
    end
  
    @doc """
    Get a dynamic route by ID.
    """
    def get_route(id) do
      Repo.get(DynamicRoute, id)
    end
  
    @doc """
    Update a dynamic route.
    """
    def update_route(route, attrs) do
      # If the path is being updated, recalculate parts and flat_parts
      attrs = if Map.has_key?(attrs, :path) do
        {parts, flat_parts} = DynamicRouter.extract_parts(attrs.path)
        Map.merge(attrs, %{parts: parts, flat_parts: flat_parts})
      else
        attrs
      end
  
      route
      |> DynamicRoute.changeset(attrs)
      |> Repo.update()
    end
  
  @doc """
  Delete a dynamic route.
  """
  def delete_route(route) do
    Repo.delete(route)
  end

  @doc """
  Find a route by ID.
  """
  def find_route_by_id(id) do
    case Repo.get(DynamicRoute, id) do
      nil -> {:error, :not_found}
      route -> {:ok, route}
    end
  end

  @doc """
  Find a route by method and path.
  """
  def find_route(method, path) do
    query = from r in DynamicRoute,
            where: r.method == ^method and r.path == ^path and r.enabled == true,
            limit: 1

    case Repo.one(query) do
      nil -> {:error, :not_found}
      route -> {:ok, route}
    end
  end
  end
