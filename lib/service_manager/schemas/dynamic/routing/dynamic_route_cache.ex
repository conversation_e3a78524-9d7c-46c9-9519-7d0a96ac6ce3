defmodule ServiceManager.Cache.RouteCache do
    use GenServer
    alias ServiceManager.Routing.DynamicRouteManager
  
    @refresh_interval :timer.minutes(5)
  
    # Client API
    def start_link(_opts) do
      GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
    end
  
    def get_routes do
      GenServer.call(__MODULE__, :get_routes)
    end
  
    def refresh do
      GenServer.cast(__MODULE__, :refresh)
    end
  
    # Server callbacks
    @impl true
    def init(_) do
      routes = load_routes()
      schedule_refresh()
      {:ok, %{routes: routes}}
    end
  
    @impl true
    def handle_call(:get_routes, _from, state) do
      {:reply, state.routes, state}
    end
  
    @impl true
    def handle_cast(:refresh, state) do
      routes = load_routes()
      schedule_refresh()
      {:noreply, %{state | routes: routes}}
    end
  
    @impl true
    def handle_info(:refresh, state) do
      routes = load_routes()
      schedule_refresh()
      {:noreply, %{state | routes: routes}}
    end
  
    defp load_routes do
      DynamicRouteManager.list_routes()
      |> Enum.filter(& &1.enabled)
    end
  
    defp schedule_refresh do
      Process.send_after(self(), :refresh, @refresh_interval)
    end
  end
  