defmodule ServiceManager.Forms.DynamicFormsManager do
  import Ecto.Query
  alias ServiceManager.Repo
  alias ServiceManager.Routing.DynamicRoute
  alias ServiceManager.Forms.DynamicForm
  alias ServiceManager.Forms.DynamicRouteForm

  @doc """
  Create a new form definition.
  """
  def create_form(attrs) do
    %DynamicForm{}
    |> DynamicForm.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Link a form to a route.
  """
  def link_form_to_route(route_id, form_id) do
    %DynamicRouteForm{}
    |> DynamicRouteForm.changeset(%{route_id: route_id, form_id: form_id})
    |> Repo.insert()
  end

  @doc """
  Unlink a form from a route.
  """
  def unlink_form_from_route(route_id, form_id) do
    query = from rf in DynamicRouteForm,
            where: rf.route_id == ^route_id and rf.form_id == ^form_id

    Repo.delete_all(query)
  end

  @doc """
  Get all forms linked to a route.
  """
  def get_route_forms(route_id) do
    query = from f in DynamicForm,
            join: rf in DynamicRouteForm, on: rf.form_id == f.id,
            where: rf.route_id == ^route_id,
            select: f
            
    Repo.all(query)
  end

  @doc """
  Get a form for a specific route and HTTP method.
  """
  def get_form_for_route(route_id, method) do
    query = from f in DynamicForm,
            join: rf in DynamicRouteForm, on: rf.form_id == f.id,
            where: rf.route_id == ^route_id and f.http_method == ^method,
            select: f,
            limit: 1
            
    Repo.one(query)
  end

  @doc """
  Validate request parameters against a form.
  """
  def validate_request(form, params) do
    # For GET requests, we validate query params
    # For other methods, we validate the request body
    
    case form.http_method do
      "GET" -> validate_get_params(form, params)
      _ -> validate_request_body(form, params)
    end
  end

  # Validate GET query parameters
  defp validate_get_params(form, params) do
    # Convert string keys to atoms for easier handling if needed
    params = if is_map(params) do
      for {key, val} <- params, into: %{}, do: {key, val}
    else
      params
    end
    
    # Use the validation schema for validation
    validate_against_schema(form.validation_schema, params)
  end

  # Validate request body (POST, PUT, etc.)
  defp validate_request_body(form, params) do
    # Use the validation schema for validation
    validate_against_schema(form.validation_schema, params)
  end

  # Perform the actual validation against the JSON schema
  defp validate_against_schema(schema, params) do
    # This is a simplified validation. In a real implementation,
    # you would use a proper JSON Schema validator like ex_json_schema
    
    # For now, we'll do a basic validation of required fields
    required_fields = schema["required"] || []
    
    missing_fields = Enum.filter(required_fields, fn field ->
      not Map.has_key?(params, field) and not Map.has_key?(params, String.to_atom(field))
    end)
    
    if Enum.empty?(missing_fields) do
      {:ok, params}
    else
      {:error, "Missing required fields: #{Enum.join(missing_fields, ", ")}"}
    end
  end

  # Format validation errors for better readability
  defp format_validation_errors(errors) do
    Enum.map(errors, fn {path, error} ->
      path_string = path |> Enum.join(".")
      "#{path_string}: #{error}"
    end)
  end

  @doc """
  Generate a JSON Schema from a form definition.
  This is a helper to create the validation_schema based on the form structure.
  """
  def generate_json_schema(form_def) do
    properties = Enum.reduce(form_def["form"]["fields"], %{}, fn field, acc ->
      # Start with basic properties
      field_schema = %{
        "type" => field["type"],
        "description" => Map.get(field, "description", "")
      }
      
      # Add constraints based on field type
      field_schema = case field["type"] do
        "string" ->
          field_schema
          |> add_constraint(field, "minLength")
          |> add_constraint(field, "maxLength")
          |> add_constraint(field, "format")
          
        "number" ->
          field_schema
          |> add_constraint(field, "minimum")
          |> add_constraint(field, "maximum")
          
        "integer" ->
          field_schema
          |> add_constraint(field, "minimum")
          |> add_constraint(field, "maximum")
          
        _ -> field_schema
      end
      
      Map.put(acc, field["name"], field_schema)
    end)
    
    required = form_def["form"]["fields"]
               |> Enum.filter(fn field -> Map.get(field, "required", false) end)
               |> Enum.map(fn field -> field["name"] end)
    
    %{
      "type" => "object",
      "properties" => properties,
      "required" => required
    }
  end
  
  # Helper to add constraints to field schema if they exist in the field definition
  defp add_constraint(field_schema, field, constraint_name) do
    case Map.get(field, constraint_name) do
      nil -> field_schema
      value -> Map.put(field_schema, constraint_name, value)
    end
  end

  @doc """
  Create a form with auto-generated JSON Schema.
  """
  def create_form_with_schema(attrs) do
    validation_schema = generate_json_schema(attrs.form)
    
    attrs = Map.put(attrs, :validation_schema, validation_schema)
    
    create_form(attrs)
  end

  @doc """
  List all available forms.
  """
  def list_forms do
    Repo.all(DynamicForm)
  end

  @doc """
  Get a form by ID.
  """
  def get_form(id) do
    Repo.get(DynamicForm, id)
  end

  @doc """
  Update a form.
  """
  def update_form(%DynamicForm{} = form, attrs) do
    form
    |> DynamicForm.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Delete a form.
  """
  def delete_form(%DynamicForm{} = form) do
    Repo.delete(form)
  end
end
