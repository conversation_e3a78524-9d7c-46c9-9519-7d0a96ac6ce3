defmodule ServiceManager.Forms.DynamicForm do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset
  alias ServiceManager.Routing.DynamicRoute

  @derive {Jason.Encoder, only: [:name, :description, :http_method, :form, :validation_schema]}
  schema "dynamic_forms" do
    field :name, :string
    field :description, :string
    field :http_method, :string
    field :form, :map  # Stored as JSON
    field :validation_schema, :map  # JSON Schema for validation
    field :required, :boolean, default: true
    
    many_to_many :routes, DynamicRoute, join_through: "dynamic_route_forms"

    timestamps()
  end

  @doc false
  def changeset(form, attrs) do
    form
    |> cast(attrs, [:name, :description, :http_method, :form, :validation_schema, :required])
    |> validate_required([:name, :http_method, :form, :validation_schema])
    |> validate_inclusion(:http_method, ["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"])
    |> validate_form_schema()
  end
  
  # Validate that the form schema is properly structured
  defp validate_form_schema(changeset) do
    case get_field(changeset, :form) do
      nil -> changeset
      form when is_map(form) -> 
        # Basic validation - could be more extensive
        if validate_form_structure(form) do
          changeset
        else
          add_error(changeset, :form, "has invalid structure")
        end
      _ -> add_error(changeset, :form, "must be a map")
    end
  end
  
  defp validate_form_structure(form) do
    # Simple validation - check that it has fields
    Map.has_key?(form, "fields") and is_list(form["fields"])
  end
end
