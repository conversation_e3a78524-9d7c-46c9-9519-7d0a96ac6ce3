defmodule ServiceManager.Forms.DynamicRouteForm do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset
  alias ServiceManager.Routing.DynamicRoute
  alias ServiceManager.Forms.DynamicForm

  @primary_key false
  schema "dynamic_route_forms" do
    belongs_to :route, DynamicRoute
    belongs_to :form, DynamicForm

    timestamps()
  end

  @doc false
  def changeset(route_form, attrs) do
    route_form
    |> cast(attrs, [:route_id, :form_id])
    |> validate_required([:route_id, :form_id])
    |> unique_constraint([:route_id, :form_id])
  end
end
