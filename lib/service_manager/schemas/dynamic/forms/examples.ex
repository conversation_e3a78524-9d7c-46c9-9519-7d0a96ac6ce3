defmodule ServiceManager.Forms.Examples do
  @moduledoc """
  Examples of how to use the dynamic forms system.
  This module provides sample code for creating and using dynamic forms.
  """
  
  alias ServiceManager.Routing.DynamicRouteManager
  alias ServiceManager.Forms.DynamicFormsManager

  @doc """
  Example of creating a user registration form and linking it to a route.
  """
  def create_user_registration_example do
    # 1. Create a dynamic route
    {:ok, route} = DynamicRouteManager.create_route(
      "User Registration API",
      "POST",
      "/users/register",
      true
    )

    # 2. Create a form for the route
    form_definition = %{
      name: "User Registration Form",
      description: "Form for user registration API",
      http_method: "POST",
      form: %{
        "fields" => [
          %{
            "name" => "username",
            "type" => "string",
            "description" => "User's username",
            "required" => true,
            "minLength" => 3,
            "maxLength" => 20
          },
          %{
            "name" => "email",
            "type" => "string",
            "description" => "User's email address",
            "required" => true,
            "format" => "email"
          },
          %{
            "name" => "password",
            "type" => "string",
            "description" => "User's password",
            "required" => true,
            "minLength" => 8
          },
          %{
            "name" => "age",
            "type" => "integer",
            "description" => "User's age",
            "required" => false,
            "minimum" => 18
          }
        ]
      },
      required: true
    }

    # Create the form with auto-generated JSON Schema
    {:ok, form} = DynamicFormsManager.create_form_with_schema(form_definition)

    # 3. Link the form to the route
    DynamicFormsManager.link_form_to_route(route.id, form.id)

    {:ok, %{route: route, form: form}}
  end

  @doc """
  Example of creating a user search form (GET method) and linking it to a route.
  """
  def create_user_search_example do
    # 1. Create a dynamic route
    {:ok, route} = DynamicRouteManager.create_route(
      "User Search API",
      "GET",
      "/users/search",
      true
    )

    # 2. Create a form for the route
    form_definition = %{
      name: "User Search Form",
      description: "Form for searching users",
      http_method: "GET",
      form: %{
        "fields" => [
          %{
            "name" => "query",
            "type" => "string",
            "description" => "Search query",
            "required" => true,
            "minLength" => 3
          },
          %{
            "name" => "limit",
            "type" => "integer",
            "description" => "Maximum number of results",
            "required" => false,
            "default" => 10,
            "minimum" => 1,
            "maximum" => 100
          },
          %{
            "name" => "offset",
            "type" => "integer",
            "description" => "Result offset for pagination",
            "required" => false,
            "default" => 0,
            "minimum" => 0
          }
        ]
      },
      required: false
    }

    # Create the form with auto-generated JSON Schema
    {:ok, form} = DynamicFormsManager.create_form_with_schema(form_definition)

    # 3. Link the form to the route
    DynamicFormsManager.link_form_to_route(route.id, form.id)

    {:ok, %{route: route, form: form}}
  end

  @doc """
  Example of creating a product update form (PUT method) and linking it to a route.
  """
  def create_product_update_example do
    # 1. Create a dynamic route
    {:ok, route} = DynamicRouteManager.create_route(
      "Product Update API",
      "PUT",
      "/products/:id",
      true
    )

    # 2. Create a form for the route
    form_definition = %{
      name: "Product Update Form",
      description: "Form for updating product information",
      http_method: "PUT",
      form: %{
        "fields" => [
          %{
            "name" => "name",
            "type" => "string",
            "description" => "Product name",
            "required" => false
          },
          %{
            "name" => "description",
            "type" => "string",
            "description" => "Product description",
            "required" => false
          },
          %{
            "name" => "price",
            "type" => "number",
            "description" => "Product price",
            "required" => false,
            "minimum" => 0
          },
          %{
            "name" => "category",
            "type" => "string",
            "description" => "Product category",
            "required" => false
          }
        ]
      },
      required: true
    }

    # Create the form with auto-generated JSON Schema
    {:ok, form} = DynamicFormsManager.create_form_with_schema(form_definition)

    # 3. Link the form to the route
    DynamicFormsManager.link_form_to_route(route.id, form.id)

    {:ok, %{route: route, form: form}}
  end
end
