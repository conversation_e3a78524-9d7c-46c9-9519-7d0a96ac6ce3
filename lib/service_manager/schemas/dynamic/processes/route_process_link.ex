defmodule ServiceManager.Schemas.Dynamic.Processes.RouteProcessLink do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Schemas.AdminUsers
  alias ServiceManager.Schemas.Dynamic.Processes.DynamicProcess
  alias ServiceManager.Routing.DynamicRoute

  schema "route_process_links" do
    belongs_to :route, <PERSON>ynamicRoute
    belongs_to :initial_process, DynamicProcess
    belongs_to :created_by, AdminUsers, foreign_key: :created_by_id

    timestamps()
  end

  @doc false
  def changeset(route_process_link, attrs) do
    route_process_link
    |> cast(attrs, [:route_id, :initial_process_id, :created_by_id])
    |> validate_required([:route_id, :initial_process_id])
    |> foreign_key_constraint(:route_id)
    |> foreign_key_constraint(:initial_process_id)
    |> unique_constraint(:route_id)
  end

  @doc """
  Find a route process link by route ID
  """
  def find_by_route_id(route_id) do
    ServiceManager.Repo.get_by(__MODULE__, route_id: route_id)
  end
end
