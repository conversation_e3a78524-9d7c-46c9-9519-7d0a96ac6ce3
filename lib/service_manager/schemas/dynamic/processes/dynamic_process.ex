defmodule ServiceManager.Schemas.Dynamic.Processes.DynamicProcess do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Schemas.AdminUsers

  schema "dynamic_processes" do
    field :name, :string
    field :description, :string
    field :code, :string
    field :expected_params, :map, default: %{}
    field :execution_mode, :string, default: "plugin_code"
    
    # Plugin library fields
    field :category, :string, default: "General"
    field :group, :string, default: "User Plugins"
    field :plugin_type, :string, default: "public"
    field :rating, :decimal, default: Decimal.new("0.0")
    field :downloads, :integer, default: 0
    field :tags, {:array, :string}, default: []
    field :version, :string, default: "1.0.0"
    field :author, :string
    field :license, :string, default: "MIT"
    field :repository_url, :string
    field :documentation_url, :string
    field :featured, :boolean, default: false
    field :verified, :boolean, default: false
    
    # Sync status fields
    field :sync_status, :string, default: "pending"
    field :last_synced_at, :utc_datetime
    
    belongs_to :created_by, AdminUsers, foreign_key: :created_by_id
    
    has_many :source_links, ServiceManager.Schemas.Dynamic.Processes.ProcessChainLink, foreign_key: :source_process_id
    has_many :target_links, ServiceManager.Schemas.Dynamic.Processes.ProcessChainLink, foreign_key: :target_process_id
    has_many :route_links, ServiceManager.Schemas.Dynamic.Processes.RouteProcessLink, foreign_key: :initial_process_id
    
    has_many :target_processes, through: [:source_links, :target_process]
    has_many :source_processes, through: [:target_links, :source_process]
    has_many :routes, through: [:route_links, :route]

    timestamps()
  end

  @doc false
  def changeset(dynamic_process, attrs) do
    dynamic_process
    |> cast(attrs, [
      :name, :description, :code, :expected_params, :execution_mode, :created_by_id,
      :category, :group, :plugin_type, :rating, :downloads, :tags,
      :version, :author, :license, :repository_url, :documentation_url,
      :featured, :verified, :sync_status, :last_synced_at
    ])
    |> validate_required([:name, :code])
    |> unique_constraint(:name)
    |> validate_code()
    |> validate_plugin_type()
    |> validate_execution_mode()
    |> validate_rating()
    |> validate_sync_status()
  end

  defp validate_code(changeset) do
    case get_change(changeset, :code) do
      nil -> changeset
      code ->
        # Basic validation to ensure the code has a process function
        if String.contains?(code, "def process") do
          changeset
        else
          add_error(changeset, :code, "must contain a process function")
        end
    end
  end

  defp validate_plugin_type(changeset) do
    valid_types = ["system", "public", "protected", "private", "enterprise"]
    validate_inclusion(changeset, :plugin_type, valid_types)
  end

  defp validate_execution_mode(changeset) do
    valid_modes = ["plugin_code", "steps"]
    validate_inclusion(changeset, :execution_mode, valid_modes)
  end

  defp validate_rating(changeset) do
    changeset
    |> validate_number(:rating, greater_than_or_equal_to: 0, less_than_or_equal_to: 5)
  end

  defp validate_sync_status(changeset) do
    valid_statuses = ["pending", "synced", "error"]
    validate_inclusion(changeset, :sync_status, valid_statuses)
  end

  @doc """
  Find a dynamic process by ID
  """
  def find(id) do
    ServiceManager.Repo.get(__MODULE__, id)
  end
end
