defmodule ServiceManager.Schemas.Dynamic.Processes.CodeStepManager do
  @moduledoc """
  Manager module for code steps with async support.
  Handles CRUD operations, step validation, and code generation.
  """

  import Ecto.Query
  import Ecto.Changeset
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Dynamic.Processes.{CodeStep, DynamicProcess}

  @doc """
  Get all steps for a process, ordered by hierarchy and position
  """
  def get_process_steps(process_id) do
    query = from s in CodeStep,
            where: s.process_id == ^process_id,
            order_by: [asc: s.order_position],
            preload: [:parent_step, :child_steps, :created_by]
    
    steps = Repo.all(query)
    {:ok, build_step_tree(steps)}
  end

  @doc """
  Get a single code step by ID
  """
  def get_step(step_id) do
    case Repo.get(CodeStep, step_id) do
      nil -> {:error, :not_found}
      step -> {:ok, Repo.preload(step, [:parent_step, :child_steps, :created_by])}
    end
  end

  @doc """
  Get root steps for a process (steps without parent)
  """
  def get_root_steps(process_id) do
    query = from s in CodeStep,
            where: s.process_id == ^process_id and s.is_root == true,
            order_by: [asc: s.order_position],
            preload: [:child_steps]
    
    Repo.all(query)
  end

  @doc """
  Create a new code step
  """
  def create_step(attrs) do
    %CodeStep{}
    |> CodeStep.changeset(attrs)
    |> maybe_calculate_indentation()
    |> maybe_set_order_position()
    |> Repo.insert()
  end

  @doc """
  Update an existing code step
  """
  def update_step(%CodeStep{} = step, attrs) do
    step
    |> CodeStep.changeset(attrs)
    |> maybe_calculate_indentation()
    |> Repo.update()
  end

  @doc """
  Delete a code step and all its children
  """
  def delete_step(%CodeStep{} = step) do
    # Delete all child steps recursively
    delete_child_steps(step.id)
    
    # Delete the step itself
    Repo.delete(step)
  end

  @doc """
  Batch update multiple steps (useful for reordering)
  """
  def batch_update_steps(steps_data) do
    Repo.transaction(fn ->
      Enum.map(steps_data, fn {step_id, attrs} ->
        step = Repo.get!(CodeStep, step_id)
        case update_step(step, attrs) do
          {:ok, updated_step} -> updated_step
          {:error, changeset} -> Repo.rollback(changeset)
        end
      end)
    end)
  end

  @doc """
  Generate Elixir code from process steps
  """
  def generate_code_from_steps(process_id) do
    case get_process_steps(process_id) do
      {:ok, step_tree} ->
        # Check if steps already contain a process function definition
        code_lines = step_tree
                    |> Enum.map(&step_to_code_line/1)
                    |> List.flatten()
                    |> Enum.join("\n")
        
        # If the generated code doesn't contain a process function, wrap it
        final_code = if String.contains?(code_lines, "def process") do
          code_lines
        else
          # Wrap the steps in a process function
          "def process(input) do\n  # Generated from code steps\n#{indent_code(code_lines, 2)}\n  \n  {:ok, %{status: \"success\", data: input}}\nend"
        end
        
        {:ok, final_code}
      
      error -> error
    end
  end

  @doc """
  Validate step hierarchy for a process
  """
  def validate_step_hierarchy(process_id) do
    case get_process_steps(process_id) do
      {:ok, steps} ->
        validation_results = %{
          balanced_blocks: validate_balanced_blocks(steps),
          valid_indentation: validate_indentation(steps),
          proper_structure: validate_structure(steps)
        }
        
        all_valid = Enum.all?(Map.values(validation_results))
        
        {:ok, %{valid: all_valid, details: validation_results}}
      
      error -> error
    end
  end

  @doc """
  Get next order position for a step in a given context
  """
  def get_next_order_position(process_id, parent_step_id \\ nil) do
    query = from s in CodeStep,
            where: s.process_id == ^process_id,
            select: max(s.order_position)
    
    query = if parent_step_id do
      from s in query, where: s.parent_step_id == ^parent_step_id
    else
      from s in query, where: is_nil(s.parent_step_id)
    end
    
    case Repo.one(query) do
      nil -> 0
      max_pos -> max_pos + 1
    end
  end

  @doc """
  Import code string into steps (reverse engineering)
  """
  def import_code_to_steps(process_id, code_string, user_id \\ nil) do
    # This is a complex function that would parse Elixir code
    # For now, we'll create a simple implementation
    lines = String.split(code_string, "\n")
    
    Repo.transaction(fn ->
      # Clear existing steps
      clear_process_steps(process_id)
      
      # Create steps from lines
      lines
      |> Enum.with_index()
      |> Enum.map(fn {line, index} ->
        step_type = determine_step_type(line)
        indentation = calculate_line_indentation(line)
        
        create_step(%{
          process_id: process_id,
          step_type: step_type,
          content: String.trim(line),
          order_position: index,
          indentation_level: indentation,
          is_root: indentation == 0,
          created_by_id: user_id
        })
      end)
    end)
  end

  # Private helper functions

  defp build_step_tree(steps) do
    # Build a hierarchical tree structure from flat list
    root_steps = Enum.filter(steps, &(&1.parent_step_id == nil))
    
    Enum.map(root_steps, fn root_step ->
      Map.put(root_step, :children, get_step_children(root_step.id, steps))
    end)
  end

  defp get_step_children(parent_id, all_steps) do
    children = Enum.filter(all_steps, &(&1.parent_step_id == parent_id))
    
    Enum.map(children, fn child ->
      Map.put(child, :children, get_step_children(child.id, all_steps))
    end)
  end

  defp delete_child_steps(parent_id) do
    child_steps = from(s in CodeStep, where: s.parent_step_id == ^parent_id)
    
    # Get all child step IDs to recursively delete their children
    child_ids = Repo.all(from s in child_steps, select: s.id)
    
    # Recursively delete children of children
    Enum.each(child_ids, &delete_child_steps/1)
    
    # Delete the direct children
    Repo.delete_all(child_steps)
  end

  defp maybe_calculate_indentation(%Ecto.Changeset{} = changeset) do
    parent_step_id = get_change(changeset, :parent_step_id)
    step_type = get_change(changeset, :step_type)
    
    if parent_step_id && step_type do
      parent_step = Repo.get(CodeStep, parent_step_id)
      indentation = CodeStep.calculate_indentation(parent_step, step_type)
      put_change(changeset, :indentation_level, indentation)
    else
      changeset
    end
  end

  defp maybe_set_order_position(%Ecto.Changeset{} = changeset) do
    case get_change(changeset, :order_position) do
      nil ->
        process_id = get_change(changeset, :process_id) || get_field(changeset, :process_id)
        parent_step_id = get_change(changeset, :parent_step_id)
        next_position = get_next_order_position(process_id, parent_step_id)
        put_change(changeset, :order_position, next_position)
      _ ->
        changeset
    end
  end

  defp step_to_code_line(%CodeStep{} = step) do
    indentation = String.duplicate(" ", step.indentation_level)
    line = indentation <> step.content
    
    case Map.get(step, :children, []) do
      [] -> [line]
      children -> [line | Enum.map(children, &step_to_code_line/1)]
    end
  end

  defp validate_balanced_blocks(steps) do
    # Check that every function_open has a function_close, etc.
    opens = count_steps_by_type(steps, ["function_open", "block_open"])
    closes = count_steps_by_type(steps, ["function_close", "block_close"])
    opens == closes
  end

  defp validate_indentation(steps) do
    # Check that indentation levels are consistent
    Enum.all?(steps, fn step ->
      expected_indent = case step.parent_step do
        nil -> 0
        parent -> parent.indentation_level + 2
      end
      step.indentation_level >= expected_indent
    end)
  end

  defp validate_structure(steps) do
    # Check that structure makes sense (functions contain valid content, etc.)
    # This is a placeholder for more complex validation
    length(steps) > 0
  end

  defp count_steps_by_type(steps, types) do
    steps
    |> List.flatten()
    |> Enum.count(&(&1.step_type in types))
  end

  defp clear_process_steps(process_id) do
    from(s in CodeStep, where: s.process_id == ^process_id)
    |> Repo.delete_all()
  end

  defp determine_step_type(line) do
    trimmed = String.trim(line)
    
    cond do
      String.starts_with?(trimmed, "def ") -> "function_open"
      String.starts_with?(trimmed, "if ") -> "block_open"
      String.starts_with?(trimmed, "case ") -> "block_open"
      String.starts_with?(trimmed, "cond ") -> "block_open"
      String.starts_with?(trimmed, "#") -> "comment"
      String.starts_with?(trimmed, "import ") -> "import"
      String.starts_with?(trimmed, "alias ") -> "import"
      String.trim(trimmed) == "end" -> determine_close_type(line)
      true -> "line"
    end
  end

  defp determine_close_type(_line) do
    # Could be more sophisticated by tracking context
    "function_close"
  end

  defp calculate_line_indentation(line) do
    line
    |> String.to_charlist()
    |> Enum.take_while(&(&1 == 32))  # ASCII 32 is space
    |> length()
  end

  defp indent_code(code, spaces) do
    indentation = String.duplicate(" ", spaces)
    code
    |> String.split("\n")
    |> Enum.map(fn line ->
      if String.trim(line) == "" do
        line  # Keep empty lines as-is
      else
        indentation <> line
      end
    end)
    |> Enum.join("\n")
  end
end