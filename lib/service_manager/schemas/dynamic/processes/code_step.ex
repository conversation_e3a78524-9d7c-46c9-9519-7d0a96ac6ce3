defmodule ServiceManager.Schemas.Dynamic.Processes.CodeStep do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Schemas.Dynamic.Processes.DynamicProcess
  alias ServiceManager.Schemas.AdminUsers

  @step_types ["line", "function_open", "function_close", "block_open", "block_close", "comment", "import", "custom"]

  schema "code_steps" do
    field :step_type, :string
    field :content, :string
    field :order_position, :integer, default: 0
    field :indentation_level, :integer, default: 0
    field :expected_inputs, :map, default: %{}
    field :input_count, :integer, default: 1
    field :is_root, :boolean, default: false
    field :step_category, :string
    
    # Library-specific fields
    field :is_template, :boolean, default: false
    field :step_group, :string
    field :step_class, :string
    field :usage_count, :integer, default: 0
    field :template_params, :map, default: %{}
    field :tags, {:array, :string}, default: []
    field :description, :string
    field :popularity_score, :float, default: 0.0
    field :is_featured, :boolean, default: false
    
    belongs_to :process, DynamicProcess, foreign_key: :process_id
    belongs_to :parent_step, __MODULE__, foreign_key: :parent_step_id
    belongs_to :created_by, AdminUsers, foreign_key: :created_by_id
    belongs_to :author, AdminUsers, foreign_key: :author_id
    
    has_many :child_steps, __MODULE__, foreign_key: :parent_step_id
    
    timestamps()
  end

  @doc false
  def changeset(code_step, attrs) do
    code_step
    |> cast(attrs, [
      :process_id, :step_type, :content, :parent_step_id, :order_position,
      :indentation_level, :expected_inputs, :input_count, :is_root, 
      :step_category, :created_by_id, :is_template, :step_group, :step_class,
      :usage_count, :template_params, :tags, :description, :popularity_score,
      :is_featured, :author_id
    ])
    |> validate_required([:step_type, :content])
    |> validate_required_for_process_step()
    |> validate_required_for_template()
    |> validate_inclusion(:step_type, @step_types)
    |> validate_number(:order_position, greater_than_or_equal_to: 0)
    |> validate_number(:indentation_level, greater_than_or_equal_to: 0)
    |> validate_number(:input_count, greater_than_or_equal_to: 0)
    |> validate_number(:usage_count, greater_than_or_equal_to: 0)
    |> validate_number(:popularity_score, greater_than_or_equal_to: 0.0)
    |> validate_step_pairing()
    |> foreign_key_constraint(:process_id)
    |> foreign_key_constraint(:parent_step_id)
    |> foreign_key_constraint(:created_by_id)
    |> foreign_key_constraint(:author_id)
  end

  @doc """
  Changeset specifically for template steps (library steps)
  """
  def template_changeset(code_step, attrs) do
    code_step
    |> cast(attrs, [
      :step_type, :content, :step_category, :step_group, :step_class,
      :template_params, :tags, :description, :is_featured, :author_id
    ])
    |> put_change(:is_template, true)
    |> validate_required([:step_type, :content, :description])
    |> validate_inclusion(:step_type, @step_types)
    |> foreign_key_constraint(:author_id)
  end

  # Validates required fields for process steps
  defp validate_required_for_process_step(changeset) do
    is_template = get_change(changeset, :is_template) || get_field(changeset, :is_template)
    
    if is_template do
      changeset
    else
      validate_required(changeset, [:process_id, :order_position, :indentation_level])
    end
  end

  # Validates required fields for template steps
  defp validate_required_for_template(changeset) do
    is_template = get_change(changeset, :is_template) || get_field(changeset, :is_template)
    
    if is_template do
      validate_required(changeset, [:description])
    else
      changeset
    end
  end

  # Validates that opening steps have corresponding closing steps
  defp validate_step_pairing(changeset) do
    step_type = get_change(changeset, :step_type)
    
    case step_type do
      "function_open" ->
        # Could add validation to ensure there's a corresponding function_close
        changeset
      "block_open" ->
        # Could add validation to ensure there's a corresponding block_close
        changeset
      _ ->
        changeset
    end
  end

  @doc """
  Get all step types
  """
  def step_types, do: @step_types

  @doc """
  Check if step type requires a closing pair
  """
  def requires_closing?(step_type) do
    step_type in ["function_open", "block_open"]
  end

  @doc """
  Get the closing step type for an opening step
  """
  def closing_type("function_open"), do: "function_close"
  def closing_type("block_open"), do: "block_close"
  def closing_type(_), do: nil

  @doc """
  Calculate indentation based on parent step and step type
  """
  def calculate_indentation(parent_step, step_type) do
    base_indent = if parent_step, do: parent_step.indentation_level, else: 0
    
    case step_type do
      "function_close" -> base_indent
      "block_close" -> base_indent
      _ when not is_nil(parent_step) -> base_indent + 2
      _ -> base_indent
    end
  end

  @doc """
  Get predefined step groups for organization
  """
  def step_groups do
    [
      "Control Flow",
      "Data Operations", 
      "Function Definitions",
      "Error Handling",
      "I/O Operations",
      "Pattern Matching",
      "Concurrency",
      "Database",
      "HTTP/API",
      "Utilities"
    ]
  end

  @doc """
  Get predefined step classes (complexity levels)
  """
  def step_classes do
    [
      "Basic",
      "Intermediate", 
      "Advanced",
      "Expert"
    ]
  end

  @doc """
  Increment usage count for a step
  """
  def increment_usage_count(step) do
    step
    |> change(%{usage_count: step.usage_count + 1})
    |> update_popularity_score()
  end

  @doc """
  Update popularity score based on usage
  """
  def update_popularity_score(changeset) do
    usage_count = get_field(changeset, :usage_count) || 0
    # Simple popularity algorithm: usage_count + featured bonus + recency factor
    featured_bonus = if get_field(changeset, :is_featured), do: 10.0, else: 0.0
    score = Float.round(usage_count * 1.0 + featured_bonus, 2)
    
    put_change(changeset, :popularity_score, score)
  end
end