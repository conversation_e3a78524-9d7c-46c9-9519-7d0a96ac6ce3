defmodule ServiceManager.Schemas.Dynamic.Processes.ProcessChainLink do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Schemas.AdminUsers
  alias ServiceManager.Schemas.Dynamic.Processes.DynamicProcess

  schema "process_chain_links" do
    field :position, :integer, default: 0
    field :is_root, :boolean, default: false
    field :chain_id, :binary_id
    field :order_position, :integer, default: 0
    
    belongs_to :source_process, DynamicProcess
    belongs_to :target_process, DynamicProcess
    belongs_to :created_by, AdminUsers, foreign_key: :created_by_id

    timestamps()
  end

  @doc false
  def changeset(process_chain_link, attrs) do
    process_chain_link
    |> cast(attrs, [:source_process_id, :target_process_id, :position, :is_root, :chain_id, :order_position, :created_by_id])
    |> validate_required([:source_process_id, :target_process_id])
    |> foreign_key_constraint(:source_process_id)
    |> foreign_key_constraint(:target_process_id)
    |> unique_constraint([:source_process_id, :target_process_id])
    |> validate_different_processes()
    |> validate_root_process()
  end

  defp validate_different_processes(changeset) do
    source_id = get_field(changeset, :source_process_id)
    target_id = get_field(changeset, :target_process_id)
    is_root = get_field(changeset, :is_root)

    # Only validate different processes if this is NOT a root process
    if !is_root && source_id && target_id && source_id == target_id do
      add_error(changeset, :target_process_id, "cannot be the same as source process")
    else
      changeset
    end
  end

  defp validate_root_process(changeset) do
    is_root = get_field(changeset, :is_root)
    source_id = get_field(changeset, :source_process_id)
    target_id = get_field(changeset, :target_process_id)

    # If this is marked as root, source and target should be the same
    if is_root && source_id && target_id && source_id != target_id do
      add_error(changeset, :is_root, "root process link must have same source and target process")
    else
      changeset
    end
  end

  @doc """
  Find all chain links for a source process
  """
  def find_by_source_process_id(source_process_id) do
    import Ecto.Query
    
    query = from pcl in __MODULE__,
            where: pcl.source_process_id == ^source_process_id,
            order_by: [asc: pcl.order_position, asc: pcl.position],
            preload: [:target_process]
            
    ServiceManager.Repo.all(query)
  end

  @doc """
  Find chain by chain_id
  """
  def find_by_chain_id(chain_id) do
    import Ecto.Query
    
    query = from pcl in __MODULE__,
            where: pcl.chain_id == ^chain_id,
            order_by: [asc: pcl.order_position],
            preload: [:source_process, :target_process]
            
    ServiceManager.Repo.all(query)
  end

  @doc """
  Find root process for a chain
  """
  def find_root_by_chain_id(chain_id) do
    import Ecto.Query
    
    query = from pcl in __MODULE__,
            where: pcl.chain_id == ^chain_id and pcl.is_root == true,
            preload: [:source_process]
            
    ServiceManager.Repo.one(query)
  end

  @doc """
  Find the chain for a specific process (either as root or in a chain)
  """
  def find_chain_for_process(process_id) do
    import Ecto.Query
    
    # First check if this process is a root
    root_query = from pcl in __MODULE__,
                where: pcl.source_process_id == ^process_id and pcl.is_root == true
    
    case ServiceManager.Repo.one(root_query) do
      %__MODULE__{chain_id: chain_id} -> find_by_chain_id(chain_id)
      nil -> 
        # Check if this process is part of any chain
        chain_query = from pcl in __MODULE__,
                     where: pcl.source_process_id == ^process_id or pcl.target_process_id == ^process_id,
                     limit: 1
        
        case ServiceManager.Repo.one(chain_query) do
          %__MODULE__{chain_id: chain_id} when not is_nil(chain_id) -> find_by_chain_id(chain_id)
          _ -> []
        end
    end
  end
end
