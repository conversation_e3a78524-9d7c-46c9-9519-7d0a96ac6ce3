defmodule ServiceManager.Schemas.RolesAndPermission do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Schema.Embedded.ModulesEmbedded, as: Rights

  schema "roles_and_permissions" do
    field :name, :string
    field :status, :string, default: "active"
    field :created_by, :integer
    field :updated_by, :integer
    embeds_one :rights, Rights

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(roles_and_permission, attrs) do
    roles_and_permission
    |> cast(attrs, [:name, :created_by, :updated_by, :status])
    |> cast_embed(:rights)
    |> validate_required([:name])
    |> unsafe_validate_unique(:name, ServiceManager.Repo)
    |> unique_constraint(:name)
  end
end
