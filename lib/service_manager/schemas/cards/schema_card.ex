defmodule ServiceManager.Cards.Card do
  use Ecto.Schema
  import Ecto.Changeset

  schema "cards" do
    field :token, :string
    field :last4, :string
    field :exp_month, :integer
    field :exp_year, :integer
    field :brand, :string
    belongs_to :accounts_users, ServiceManager.Accounts.User

    timestamps()
  end

  @doc false
  def changeset(card, attrs) do
    card
    |> cast(attrs, [:token, :last4, :exp_month, :exp_year, :brand])
    |> validate_required([:token, :last4, :exp_month, :exp_year, :brand])
    |> unique_constraint(:token)
  end
end
