defmodule ServiceManager.Schemas.Currency do
  use Ecto.Schema
  import Ecto.Changeset

  schema "currencies" do
    field :code, :string
    field :name, :string
    field :symbol, :string
    field :is_active, :boolean, default: true
    field :created_by, :integer
    field :updated_by, :integer

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(currency, attrs) do
    currency
    |> cast(attrs, [:code, :name, :symbol, :is_active, :created_by, :updated_by])
    |> validate_required([:code, :name])
    |> validate_length(:code, is: 3)
    |> unique_constraint(:code)
  end
end
