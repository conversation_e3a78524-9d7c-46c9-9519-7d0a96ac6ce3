defmodule ServiceManager.Schemas.Session.DeviceSessionManager do
  @moduledoc """
  Manager module for device session operations.
  Provides functions to create, retrieve, update, and manage device sessions.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Session.DeviceSession

  @doc """
  Creates a new device session.
  """
  def create_session(attrs \\ %{}) do
    %DeviceSession{}
    |> DeviceSession.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets a device session by ID.
  """
  def get_session(id) do
    Repo.get(DeviceSession, id)
  end

  @doc """
  Gets a device session by session token.
  """
  def get_session_by_token(session_token) do
    Repo.get_by(DeviceSession, session_token: session_token)
  end

  @doc """
  Gets a device session by access token.
  """
  def get_session_by_access_token(access_token) do
    Repo.get_by(DeviceSession, access_token: access_token)
  end

  @doc """
  Gets all active sessions for a device.
  """
  def get_active_sessions_by_device(device_id) do
    from(s in DeviceSession,
      where: s.device_id == ^device_id and s.is_active == true,
      order_by: [desc: s.last_activity_at]
    )
    |> Repo.all()
  end

  @doc """
  Gets all active sessions for a user.
  """
  def get_active_sessions_by_user(user_id) do
    from(s in DeviceSession,
      where: s.user_id == ^user_id and s.is_active == true,
      order_by: [desc: s.last_activity_at]
    )
    |> Repo.all()
  end

  @doc """
  Updates a device session.
  """
  def update_session(%DeviceSession{} = session, attrs) do
    session
    |> DeviceSession.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates the last activity timestamp for a session.
  """
  def update_last_activity(%DeviceSession{} = session) do
    update_session(session, %{last_activity_at: DateTime.utc_now()})
  end

  @doc """
  Revokes a device session.
  """
  def revoke_session(%DeviceSession{} = session, reason \\ "manual_revocation") do
    update_session(session, %{
      is_active: false,
      revoked_at: DateTime.utc_now(),
      revocation_reason: reason
    })
  end

  @doc """
  Revokes all sessions for a device.
  """
  def revoke_all_device_sessions(device_id, reason \\ "device_logout") do
    from(s in DeviceSession,
      where: s.device_id == ^device_id and s.is_active == true
    )
    |> Repo.update_all(
      set: [
        is_active: false,
        revoked_at: DateTime.utc_now(),
        revocation_reason: reason,
        updated_at: DateTime.utc_now()
      ]
    )
  end

  @doc """
  Revokes all sessions for a user.
  """
  def revoke_all_user_sessions(user_id, reason \\ "user_logout") do
    from(s in DeviceSession,
      where: s.user_id == ^user_id and s.is_active == true
    )
    |> Repo.update_all(
      set: [
        is_active: false,
        revoked_at: DateTime.utc_now(),
        revocation_reason: reason,
        updated_at: DateTime.utc_now()
      ]
    )
  end

  @doc """
  Checks if a session is valid and active.
  """
  def valid_session?(%DeviceSession{} = session) do
    session.is_active and
      (is_nil(session.expires_at) or DateTime.compare(session.expires_at, DateTime.utc_now()) == :gt)
  end

  @doc """
  Cleans up expired sessions.
  """
  def cleanup_expired_sessions do
    now = DateTime.utc_now()

    from(s in DeviceSession,
      where: s.is_active == true and s.expires_at < ^now
    )
    |> Repo.update_all(
      set: [
        is_active: false,
        revoked_at: now,
        revocation_reason: "expired",
        updated_at: now
      ]
    )
  end

  @doc """
  Deletes a device session permanently.
  """
  def delete_session(%DeviceSession{} = session) do
    Repo.delete(session)
  end
end
