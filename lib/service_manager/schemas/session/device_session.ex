defmodule ServiceManager.Schemas.Session.DeviceSession do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset
  

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "device_sessions" do
    field :device_id, :string
    field :user_id, :string 
    field :wallet_user_id, :string
    field :third_party_id, :string
    field :session_token, :string
    field :access_token, :string
    field :access_code, :string
    field :salt, :string
    field :secret, :string
    field :refresh_token, :string
    field :token_hash, :string
    field :expires_at, :utc_datetime
    field :last_activity_at, :utc_datetime
    field :ip_address, :string
    field :user_agent, :string
    field :is_active, :boolean, default: true
    field :revoked_at, :utc_datetime
    field :revocation_reason, :string

    timestamps()
  end

  @doc false
  def changeset(device_session, attrs) do
    device_session
    |> cast(attrs, [
      :device_id,
      :user_id,
      :wallet_user_id,
      :third_party_id,
      :session_token,
      :access_token,
      :access_code,
      :salt,
      :secret,
      :refresh_token,
      :token_hash,
      :expires_at,
      :last_activity_at,
      :ip_address,
      :user_agent,
      :is_active,
      :revoked_at,
      :revocation_reason
    ])
    |> validate_required([
      :device_id,
      :session_token,
      :access_token
    ])
    |> validate_length(:device_id, min: 1, max: 255)
    |> validate_length(:access_token, min: 1)
    |> unique_constraint(:session_token)
    |> unique_constraint(:access_token)
  end
end
