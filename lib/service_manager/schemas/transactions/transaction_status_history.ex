defmodule ServiceManager.Schemas.Transactions.TransactionStatusHistory do
  @moduledoc """
  Schema for tracking transaction status history.
  Records all status changes for transactions, including who made the change and when.
  """
  use Ecto.Schema
  import Ecto.Changeset
  use Endon

  @derive {Jason.Encoder,
           only: [
             :transaction_id,
             :from_status,
             :to_status,
             :changed_by,
             :notes,
             :metadata,
             :inserted_at
           ]}

  schema "transaction_status_history" do
    field :transaction_id, :integer
    field :from_status, :string
    field :to_status, :string
    field :changed_by, :integer  # User ID or system identifier
    field :notes, :string
    field :metadata, :map, default: %{}
    
    timestamps()
  end

  @doc false
  def changeset(history, attrs) do
    history
    |> cast(attrs, [:transaction_id, :from_status, :to_status, :changed_by, :notes, :metadata])
    |> validate_required([:transaction_id, :to_status])
  end
end
