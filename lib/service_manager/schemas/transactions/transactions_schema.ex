defmodule ServiceManager.Transactions.Transaction do
  # Use the Ecto.Schema module to define the schema for the Transaction model
  use Ecto.Schema
  # Import the Ecto.Changeset module to handle changesets
  import Ecto.Changeset
  use <PERSON><PERSON>

  alias ServiceManager.Accounts.FundAccounts
  alias ServiceManager.WalletAccounts.Wallet
  alias ServiceManager.Services.TransactionStatusHistoryService

  @derive {Jason.Encoder,
           only: [
             :type,
             :amount,
             :credit_amount,
             :debit_amount,
             :description,
             :status,
             :reference,
             :value_date,
             :opening_balance,
             :closing_balance,
             :transaction_details,
             :callback_status,
             :cbs_transaction_reference,
             :external_reference,
             :owner_id,
             :sender_account,
             :receiver_account,
             :is_reversal,
             :original_transaction_id
           ]}
  # Define the schema for the "transactions" table
  schema "transactions" do
    # Define a string field for the type of transaction (e.g., credit, debit, transfer)
    field :type, :string
    # Define a decimal field for the transaction amount
    field :amount, :decimal
    # Define a decimal field for the credit amount
    field :credit_amount, :decimal
    # Define a decimal field for the debit amount
    field :debit_amount, :decimal
    # Define a string field for the transaction description
    field :description, :string
    # Define a string field for the transaction status (e.g., pending, completed, failed)
    field :status, :string
    # Define a string field for the transaction reference
    field :reference, :string
    # Define a date field for the value date of the transaction
    field :value_date, :date
    # Define a decimal field for the opening balance before the transaction
    field :opening_balance, :decimal
    # Define a decimal field for the closing balance after the transaction
    field :closing_balance, :decimal
    # Store detailed transaction information including errors
    field :transaction_details, :map, default: %{}
    # Define a string field for callback status with default pending
    field :callback_status, :string, default: "pending"
    # Define a string field for CBS transaction reference
    field :cbs_transaction_reference, :string
    # Define a string field for external reference
    field :external_reference, :string
    # Define a string field for sender's account number
    field :sender_account, :string
    # Define a string field for receiver's account number
    field :receiver_account, :string
    # Define a belongs_to association with the UserAccount model for the from_account
    belongs_to :from_account, FundAccounts
    # Define a belongs_to association with the UserAccount model for the to_account
    belongs_to :to_account, FundAccounts
    # Define a belongs_to association with the User model for the owner
    belongs_to :owner, ServiceManager.Accounts.User
    # Define a belongs_to association with the Wallet model for the from_wallet
    belongs_to :from_wallet, Wallet
    # Define a belongs_to association with the Wallet model for the to_wallet
    belongs_to :to_wallet, Wallet
    # Define a self-referential association for the original transaction (for reversals)
    belongs_to :original_transaction, __MODULE__
    # Define a boolean field to indicate if this is a reversal transaction
    field :is_reversal, :boolean, default: false

    # Automatically manage inserted_at and updated_at timestamp fields
    timestamps()
  end

  # Indicate that this function is for internal use and should not be included in the generated documentation
  @doc false
  def changeset(transaction, attrs) do
    transaction
    # Cast the given attributes to the transaction struct
    |> cast(attrs, [
      :type,
      :amount,
      :credit_amount,
      :debit_amount,
      :description,
      :status,
      :reference,
      :from_account_id,
      :to_account_id,
      :from_wallet_id,
      :to_wallet_id,
      :owner_id,
      :sender_account,
      :receiver_account,
      :value_date,
      :opening_balance,
      :closing_balance,
      :transaction_details,
      :callback_status,
      :cbs_transaction_reference,
      :external_reference,
      :original_transaction_id,
      :is_reversal
    ])
    # Validate that the required fields are present
    |> validate_required([:type, :amount, :status, :value_date])
    # Track status changes if status is being updated
    |> track_status_changes(attrs)
    # Validate that the type field is one of the allowed values
    |> validate_inclusion(:type, [
      "credit",
      "debit",
      "transfer",
      "direct-transfer",
      "external-transfer",
      "reversal"
    ])
    # Validate that the status field is one of the allowed values
    |> validate_inclusion(:status, ["pending", "completed", "failed", "processing", "reversed"])
    # Validate that the callback_status field is one of the allowed values
    |> validate_inclusion(:callback_status, ["pending", "failed", "success"])
    # Validate that the amount is greater than 0
    # |> validate_number(:amount, greater_than: 0)
    # Validate that the credit amount is greater than or equal to 0
    |> validate_number(:credit_amount, greater_than_or_equal_to: 0)
    # Validate that the debit amount is greater than or equal to 0
    |> validate_number(:debit_amount, greater_than_or_equal_to: 0)
    # |> validate_number(:opening_balance, greater_than_or_equal_to: 0)  # Validate that the opening balance is greater than or equal to 0
    # |> validate_number(:closing_balance, greater_than_or_equal_to: 0)  # Validate that the closing balance is greater than or equal to 0
    # Ensure that the from_account_id field references a valid UserAccount
    |> foreign_key_constraint(:from_account_id)
    # Ensure that the to_account_id field references a valid UserAccount
    |> foreign_key_constraint(:to_account_id)
    # Ensure that the owner_id field references a valid User
    |> foreign_key_constraint(:owner_id)
    # Ensure that the from_wallet_id field references a valid Wallet
    |> foreign_key_constraint(:from_wallet_id)
    # Ensure that the to_wallet_id field references a valid Wallet
    |> foreign_key_constraint(:to_wallet_id)
  end

  @doc """
  Tracks status changes in a transaction changeset.
  If the status is being changed, it records the change in the transaction_status_history table.
  """
  defp track_status_changes(changeset, _attrs) do
    case get_change(changeset, :status) do
      nil ->
        # No status change, return the changeset as is
        changeset

      new_status ->
        # Get the current status from the transaction
        transaction = changeset.data
        old_status = transaction.status

        # Only record if this is an actual change (not a new transaction)
        if transaction.id && old_status != new_status do
          # Record the status change
          case TransactionStatusHistoryService.record_status_change(
            transaction.id,
            old_status,
            new_status,
            metadata: %{
              "changed_at" => DateTime.utc_now() |> DateTime.to_iso8601()
            }
          ) do
            {:ok, _history} ->
              # Status change recorded successfully
              changeset

            {:error, reason} ->
              # Failed to record status change, but don't fail the transaction update
              # Just log the error
              require Logger
              Logger.error("Failed to record status change for transaction #{transaction.id}: #{inspect(reason)}")
              changeset
          end
        else
          # New transaction or no actual change, return the changeset as is
          changeset
        end
    end
  end

  @doc """
  After a transaction is inserted, record its initial status in the history.
  This function should be called after a successful insert.
  """
  def record_initial_status(transaction) do
    if transaction.id do
      TransactionStatusHistoryService.record_status_change(
        transaction.id,
        nil,  # No previous status for a new transaction
        transaction.status,
        metadata: %{
          "initial_status" => true,
          "created_at" => DateTime.utc_now() |> DateTime.to_iso8601()
        }
      )
    end

    # Always return the transaction
    transaction
  end
end
