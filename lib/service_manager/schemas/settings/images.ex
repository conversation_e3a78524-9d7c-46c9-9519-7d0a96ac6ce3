defmodule ServiceManager.Schemas.Settings.Images do
  use Ecto.Schema
  import Ecto.Changeset

  schema "settings" do
    field :status, :string, default: "active"
    field :value, :string
    field :key, :string, default: "images"
    field :created_by, :integer
    field :updated_by, :integer
    embeds_one :config, ServiceManager.Schemas.Embedded.EmbeddedImages

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(config, attrs) do
    config
    |> cast(attrs, [:key, :value, :created_by, :updated_by, :status])
    |> cast_embed(:config)
    |> validate_required([:key])
    |> validate_length(:key, max: 20)
    |> unsafe_validate_unique(:key, ServiceManager.Repo)
    |> unique_constraint(:key)
  end
end
