defmodule ServiceManager.Schemas.Settings.Config do
  use Ecto.Schema
  import Ecto.Changeset

  schema "settings" do
    field :status, :string, default: "active"
    field :value, :string
    field :config, :map
    field :key, :string
    field :created_by, :integer
    field :updated_by, :integer

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(config, attrs) do
    config
    |> cast(attrs, [:key, :value, :config, :created_by, :updated_by, :status])
    |> validate_required([:key])
    |> validate_length(:key, min: 3)
    |> unsafe_validate_unique(:key, ServiceManager.Repo)
    |> unique_constraint(:key)
  end
end
