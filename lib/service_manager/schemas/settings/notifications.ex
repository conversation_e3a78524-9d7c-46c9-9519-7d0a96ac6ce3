defmodule ServiceManager.Schemas.Settings.Notifications do
  use Ecto.Schema
  import Ecto.Changeset

  schema "settings" do
    field :status, :string, default: "active"
    field :value, :string
    field :key, :string, default: "notifications"
    field :created_by, :integer
    field :updated_by, :integer
    embeds_one :config, ServiceManager.Schemas.Embedded.EmbeddedNotifications

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(config, attrs) do
    config
    |> cast(attrs, [:key, :value, :created_by, :updated_by, :status])
    |> cast_embed(:config)
    |> validate_required([:key])
    # Increased max length since "notifications" is 13 chars
    |> validate_length(:key, max: 20)
    |> unsafe_validate_unique(:key, ServiceManager.Repo)
    |> unique_constraint(:key)
  end
end
