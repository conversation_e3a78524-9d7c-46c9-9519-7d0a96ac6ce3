defmodule ServiceManager.Schemas.IpWhitelist do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset

  schema "ip_whitelists" do
    field :ip_address, :string
    field :description, :string
    field :status, Ecto.Enum, values: [:active, :inactive], default: :active
    field :last_accessed_at, :utc_datetime
    field :expiry_date, :utc_datetime
    field :access_count, :integer, default: 0
    field :risk_level, Ecto.Enum, values: [:low, :medium, :high], default: :medium

    field :environment, Ecto.Enum,
      values: [:development, :staging, :production],
      default: :production

    belongs_to :user, ServiceManager.Accounts.User, on_replace: :nilify
    belongs_to :wallet_user, ServiceManager.WalletAccounts.WalletUser, on_replace: :nilify

    belongs_to :third_party_api_key, ServiceManager.ThirdParty.ThirdPartyApiKey,
      on_replace: :nilify

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(ip_whitelist, attrs) do
    ip_whitelist
    |> cast(attrs, [
      :ip_address,
      :description,
      :status,
      :last_accessed_at,
      :expiry_date,
      :access_count,
      :risk_level,
      :environment,
      :user_id,
      :wallet_user_id,
      :third_party_api_key_id
    ])
    |> validate_required([:ip_address, :status, :risk_level, :environment])
    |> validate_ip_address(:ip_address)
    |> unique_constraint(:ip_address)
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:wallet_user_id)
    |> foreign_key_constraint(:third_party_api_key_id)
    |> validate_expiry_date()
  end

  defp validate_ip_address(changeset, field) do
    validate_change(changeset, field, fn _, ip_address ->
      case validate_ip_format(ip_address) do
        true -> []
        false -> [{field, "must be a valid IPv4 or IPv6 address"}]
      end
    end)
  end

  defp validate_ip_format(ip_address) do
    case :inet.parse_address(String.to_charlist(ip_address)) do
      {:ok, _} -> true
      {:error, _} -> false
    end
  end

  defp validate_expiry_date(changeset) do
    case get_change(changeset, :expiry_date) do
      nil ->
        changeset

      expiry_date ->
        if DateTime.compare(expiry_date, DateTime.utc_now()) == :gt do
          changeset
        else
          add_error(changeset, :expiry_date, "must be in the future")
        end
    end
  end

  @doc """
  Creates a migration version of the schema.
  """
  def migration_fields do
    quote do
      add :ip_address, :string, null: false
      add :description, :string
      add :status, :string, null: false, default: "active"
      add :last_accessed_at, :utc_datetime
      add :expiry_date, :utc_datetime
      add :access_count, :integer, default: 0, null: false
      add :risk_level, :string, null: false, default: "medium"
      add :environment, :string, null: false, default: "production"

      add :user_id, references(:accounts_users, on_delete: :nilify_all)
      add :wallet_user_id, references(:walletusers, on_delete: :nilify_all)
      add :third_party_api_key_id, references(:third_party_api_key, on_delete: :nilify_all)

      timestamps(type: :utc_datetime)
    end
  end

  @doc """
  Creates a migration version of the indices.
  """
  def migration_indices do
    quote do
      create unique_index(:ip_whitelists, [:ip_address])
      create index(:ip_whitelists, [:status])
      create index(:ip_whitelists, [:user_id])
      create index(:ip_whitelists, [:wallet_user_id])
      create index(:ip_whitelists, [:third_party_api_key_id])
      create index(:ip_whitelists, [:environment])
    end
  end
end
