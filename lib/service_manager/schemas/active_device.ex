defmodule ServiceManager.Schemas.ActiveDevice do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset

  @derive {Jason.Encoder, only: [:device_id, :device_name, :last_seen_at, :enabled, :blocked, :static_access_token, :device_screen_message, :on_hold]}
  schema "active_devices" do
    field :device_id, :string
    field :device_name, :string
    field :enabled, :boolean
    field :blocked, :boolean
    field :last_seen_at, :utc_datetime
    field :user_id, :integer
    field :wallet_user_id, :integer
    field :third_party_api_key_id, :integer
    field :static_access_token, :string
    field :device_screen_message, :string
    field :on_hold, :boolean
    #    field :api_key, :string
    #    field :description, :string
    #    field :user_type, :string

    timestamps()
  end

  def changeset(device, attrs) do
    device
    |> cast(attrs, [
      :device_id,
      :device_name,
      :enabled,
      :blocked,
      :last_seen_at,
      :user_id,
      :wallet_user_id,
      :third_party_api_key_id,
      :static_access_token,
      :device_screen_message,
      :on_hold
      #      :api_key,
      #      :description,
      #      :user_type
    ])
  end

  def update_device_token(device, attrs) do
    device
    |> cast(attrs, [
      :static_access_token,
      :device_screen_message,
      :on_hold
    ])
  end

  def new(attrs) do
    %__MODULE__{}
    |> cast(attrs, [
      :device_id,
      :last_seen_at,
      :user_id,
      #      :wallet_user_id,
      :third_party_api_key_id
      #      :api_key,
      #      :description,
      #      :user_type
    ])
  end
end

# ServiceManager.Repo.all(ServiceManager.Schemas.ActiveDevice)
