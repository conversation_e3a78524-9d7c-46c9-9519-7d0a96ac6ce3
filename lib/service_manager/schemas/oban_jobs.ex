defmodule ServiceManager.Schemas.ObanJobs do
  use Ecto.Schema
  import Ecto.Changeset

  @type t :: %__MODULE__{}
  @states ~w(available scheduled executing completed discarded cancelled retryable)a

  schema "oban_jobs" do
    field :state, Ecto.Enum, values: @states, default: :available
    field :queue, :string, default: "default"
    field :worker, :string
    field :args, :map, default: %{}
    field :errors, {:array, :map}, default: []
    field :attempt, :integer, default: 0
    field :max_attempts, :integer, default: 20
    field :attempted_by, {:array, :string}, default: []
    field :priority, :integer, default: 0
    field :tags, {:array, :string}, default: []
    field :meta, :map, default: %{}
    field :scheduled_at, :naive_datetime
    field :attempted_at, :naive_datetime
    field :completed_at, :naive_datetime
    field :discarded_at, :naive_datetime
    field :cancelled_at, :naive_datetime

    timestamps(type: :naive_datetime_usec, updated_at: false)
  end

  def changeset(job, attrs) do
    job
    |> cast(attrs, [
      :state,
      :queue,
      :worker,
      :args,
      :errors,
      :attempt,
      :max_attempts,
      :attempted_by,
      :priority,
      :tags,
      :meta,
      :scheduled_at,
      :attempted_at,
      :completed_at,
      :discarded_at,
      :cancelled_at
    ])
    |> validate_required([:worker])
    |> validate_length(:queue, min: 1, max: 127)
    |> validate_length(:worker, min: 1, max: 127)
    |> validate_number(:priority, greater_than_or_equal_to: 0, less_than_or_equal_to: 3)
    |> validate_number(:max_attempts, greater_than: 0)
    |> validate_number(:attempt, greater_than_or_equal_to: 0)
    |> validate_attempt_less_than_max()
  end

  defp validate_attempt_less_than_max(changeset) do
    attempt = get_field(changeset, :attempt)
    max_attempts = get_field(changeset, :max_attempts)

    if attempt && max_attempts && attempt > max_attempts do
      add_error(changeset, :attempt, "must be less than or equal to max_attempts")
    else
      changeset
    end
  end
end
