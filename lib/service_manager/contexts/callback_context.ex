defmodule ServiceManager.Contexts.CallbackContext do
  import Ecto.Query
  alias ServiceManager.Schemas.Callback
  alias ServiceManager.Repo

  def retrieve(params \\ %{}) do
    page = Map.get(params, "page", "1") |> String.to_integer()
    page_size = Map.get(params, "page_size", "10") |> String.to_integer()
    offset = (page - 1) * page_size

    base_query = from(c in Callback, order_by: [desc: c.inserted_at])

    base_query
    |> apply_filters(params)
    |> limit(^page_size)
    |> offset(^offset)
    |> Repo.all()
  end

  def get_data!(id), do: Repo.get!(Callback, id)

  defp apply_filters(query, params) do
    params
    |> Enum.reduce(query, fn
      {"status", value}, query when value in ["pending", "success", "failed"] ->
        from q in query, where: q.status == ^value

      {"type", value}, query when not is_nil(value) and value != "" ->
        from q in query, where: q.callback_type == ^value

      {"search", value}, query when not is_nil(value) and value != "" ->
        from q in query,
          where:
            ilike(q.callback_url, ^"%#{value}%") or
              ilike(q.callback_type, ^"%#{value}%")

      _, query ->
        query
    end)
  end
end
