defmodule ServiceManager.Contexts.CardlessWithdrawsContext do
  @moduledoc """
  Context for managing cardless withdrawals.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo

  alias ServiceManager.Schemas.Withdraws.CardlessWithdraw.{
    CardlessWithdrawSchema,
    CardlessWithdrawWalletSchema
  }

  @doc """
  Creates a cardless withdraw request.
  """
  def create_cardless_withdraw(attrs \\ %{}) do
    %CardlessWithdrawSchema{}
    |> CardlessWithdrawSchema.create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates a cardless wallet withdraw request.
  """
  def create_cardless_wallet_withdraw(attrs \\ %{}) do
    %CardlessWithdrawWalletSchema{}
    |> CardlessWithdrawWalletSchema.create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets a cardless withdraw by reference number.
  """
  def get_cardless_withdraw_by_reference(reference_number) do
    Repo.get_by(CardlessWithdrawSchema, reference_number: reference_number)
  end

  @doc """
  Gets a cardless wallet withdraw by reference number.
  """
  def get_cardless_wallet_withdraw_by_reference(reference_number) do
    Repo.get_by(CardlessWithdrawWalletSchema, reference_number: reference_number)
  end

  @doc """
  Updates a cardless withdraw.
  """
  def update_cardless_withdraw(%CardlessWithdrawSchema{} = withdraw, attrs) do
    withdraw
    |> CardlessWithdrawSchema.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates a cardless wallet withdraw.
  """
  def update_cardless_wallet_withdraw(%CardlessWithdrawWalletSchema{} = withdraw, attrs) do
    withdraw
    |> CardlessWithdrawWalletSchema.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Verifies OTP for a cardless withdraw.
  """
  def verify_withdraw_otp(%CardlessWithdrawSchema{} = withdraw, otp) do
    if withdraw.otp == otp do
      update_cardless_withdraw(withdraw, %{otp_verified: true})
    else
      {:error, "Invalid OTP"}
    end
  end


  @doc """
  Verifies OTP for a cardless wallet withdraw.
  """
  def verify_wallet_withdraw_otp(%CardlessWithdrawWalletSchema{} = withdraw, otp) do
    if withdraw.otp == otp do
      update_cardless_wallet_withdraw(withdraw, %{otp_verified: true})
    else
      {:error, "Invalid OTP"}
    end
  end

  @doc """
  Lists all cardless withdraws for a user.
  """
  def list_user_cardless_withdraws(user_id) do
    CardlessWithdrawSchema
    |> where([w], w.user_id == ^user_id)
    |> order_by([w], desc: w.inserted_at)
    |> Repo.all()
  end

  @doc """
  Lists all cardless wallet withdraws for a wallet user.
  """
  def list_wallet_user_cardless_withdraws(wallet_user_id) do
    CardlessWithdrawWalletSchema
    |> where([w], w.wallet_user_id == ^wallet_user_id)
    |> order_by([w], desc: w.inserted_at)
    |> Repo.all()
  end

  @doc """
  Gets a single cardless withdraw.
  """
  def get_cardless_withdraw!(id), do: Repo.get!(CardlessWithdrawSchema, id)

  @doc """
  Gets a single cardless wallet withdraw.
  """
  def get_cardless_wallet_withdraw!(id), do: Repo.get!(CardlessWithdrawWalletSchema, id)

  @doc """
  Gets data for LiveView show component.
  """
  def get_data!(id), do: get_cardless_withdraw!(id)

  @doc """
  Retrieves paginated cardless withdraws with optional filters.
  """
  def retrieve(params \\ %{}) do
    CardlessWithdrawSchema
    |> handle_filters(params)
    |> order_by([w], desc: w.inserted_at)
    |> preload(:user)
    |> Repo.paginate(params)
  end

  defp handle_filters(query, params) do
    params
    |> Enum.reduce(query, fn
      {"status", status}, query when status != "" ->
        where(query, [w], w.status == ^status)

      {"reference", reference}, query when reference != "" ->
        where(query, [w], ilike(w.reference_number, ^"%#{reference}%"))

      {"source_account", account}, query when account != "" ->
        where(query, [w], ilike(w.source_account, ^"%#{account}%"))

      {"beneficiary_phone", phone}, query when phone != "" ->
        where(query, [w], ilike(w.beneficiary_phone, ^"%#{phone}%"))

      {"beneficiary_name", name}, query when name != "" ->
        where(query, [w], ilike(w.beneficiary_name, ^"%#{name}%"))

      {"min_amount", amount}, query when amount != "" ->
        case Decimal.parse(amount) do
          {:ok, decimal} -> where(query, [w], w.amount >= ^decimal)
          _ -> query
        end

      {"max_amount", amount}, query when amount != "" ->
        case Decimal.parse(amount) do
          {:ok, decimal} -> where(query, [w], w.amount <= ^decimal)
          _ -> query
        end

      {"date_from", date}, query when date != "" ->
        where(query, [w], fragment("DATE(?)", w.inserted_at) >= ^date)

      {"date_to", date}, query when date != "" ->
        where(query, [w], fragment("DATE(?)", w.inserted_at) <= ^date)

      _, query ->
        query
    end)
  end

  @doc """
  Approves a cardless withdraw request.
  """
  def approve_request(%CardlessWithdrawSchema{} = withdraw, user) do
    if withdraw.status == "pending" do
      update_cardless_withdraw(withdraw, %{status: "approved"})
    else
      {:error, "Can only approve pending withdraws"}
    end
  end

  @doc """
  Rejects a cardless withdraw request.
  """
  def reject_request(%CardlessWithdrawSchema{} = withdraw, user) do
    if withdraw.status == "pending" do
      update_cardless_withdraw(withdraw, %{status: "rejected"})
    else
      {:error, "Can only reject pending withdraws"}
    end
  end
end
