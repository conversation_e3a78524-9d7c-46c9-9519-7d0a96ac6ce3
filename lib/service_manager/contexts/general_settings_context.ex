defmodule ServiceManager.Contexts.GeneralSettingsContext do
  @moduledoc """
  The GeneralSettingsContext handles interactions with general application settings.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Settings.GeneralSettings

  @doc """
  Gets the general settings schema, either returning the existing settings
  or creating a new empty schema if none exists.

  ## Returns
    - %GeneralSettings{} - Either existing settings or new empty schema
  """
  def get_setting_schema do
    case get_setting() do
      nil -> %GeneralSettings{}
      settings -> settings
    end
  end

  @doc """
  Gets the current general settings configuration.
  Returns the most recent general settings record.
  """
  def get_setting() do
    GeneralSettings
    |> where([g], g.key == "general_settings")
    |> Repo.all()
    |> List.last()
  end

  @doc """
  Creates new general settings.

  ## Parameters
    - attrs: Map of attributes for the new settings

  ## Returns
    - {:ok, %GeneralSettings{}} on success
    - {:error, changeset} on validation failure
  """
  def create_setting(attrs \\ %{}) do
    %GeneralSettings{}
    |> GeneralSettings.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates existing general settings.

  ## Parameters
    - general_settings: Existing general settings struct to update
    - attrs: Map of attributes to update

  ## Returns
    - {:ok, %GeneralSettings{}} on success
    - {:error, changeset} on validation failure
  """
  def update_setting(%GeneralSettings{} = general_settings, attrs) do
    general_settings
    |> GeneralSettings.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Returns a changeset for tracking general settings changes.
  """
  def change_setting(%GeneralSettings{} = general_settings, attrs \\ %{}) do
    GeneralSettings.changeset(general_settings, attrs)
  end

  @doc """
  Inserts new general settings with user tracking.

  ## Parameters
    - params: Settings parameters
    - user: User performing the action
  """
  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> create_setting()
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Updates existing general settings with user tracking.

  ## Parameters
    - general_settings: Existing general settings to update
    - params: Update parameters
    - user: User performing the update
  """
  def update_data(%GeneralSettings{} = general_settings, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      general_settings
      |> update_setting(Map.put(params, "updated_by", user.id))
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def multi_cache(multi) do
    multi
    |> Ecto.Multi.run(:general_settings_cache, fn _, %{data_struct: data_struct} ->
      if data_struct.status in ["active", "ACTIVE"] do
        data_struct.config
        |> put_cache()
      else
        delete_cache()
      end
    end)
  end

  def put_cache(data) do
    Cachex.put(:settings, :general_settings, data)
  end

  def delete_cache() do
    Cachex.del(:settings, :general_settings)
  end
end
