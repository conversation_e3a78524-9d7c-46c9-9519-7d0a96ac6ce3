defmodule ServiceManager.Contexts.UniversalContext do
  @moduledoc """
  The UniversalContext provides common database operations and business logic
  that can be reused across different contexts. It includes standard CRUD operations
  and search functionality.
  """

  import Ecto.Query, warn: false

  alias ServiceManager.{
    Queries.DefaultQueries,
    Queries.FilterQueries,
    Repo
  }

  @query_params Application.compile_env(:service_manager, :query_params)

  @doc """
  Retrieves a paginated list of records with sorting, searching and filtering applied.

  ## Parameters
    - schema: The Ecto schema module to query
    - params: Map of query parameters for filtering, sorting and pagination (optional)
    - fields: List of fields to search across (optional)

  Returns `%Scrivener.Page{}` of records
  """
  def retrieve(schema, params \\ @query_params, fields \\ []) do
    schema
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params, fields)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  @doc """
  No-op search filter when search parameter is empty or nil
  """
  def search_filter(query, %{"search" => search}, _fields) when is_nil(search) or search == "",
    do: query

  @doc """
  Filters records by searching across specified fields using case-insensitive LIKE queries.

  ## Parameters
    - query: The Ecto query to filter
    - params: Map containing the "search" key with search term
    - fields: List of fields to search (atoms or strings)

  ## Examples
      iex> search_filter(User, %{"search" => "john"}, [:first_name, :last_name])
      #Ecto.Query<...>
  """
  def search_filter(query, %{"search" => search}, fields)
      when is_list(fields) and length(fields) > 0 do
    conditions =
      Enum.map(fields, fn field ->
        field = to_string(field)

        dynamic(
          [a],
          fragment("lower(?) LIKE lower(?)", field(a, ^String.to_atom(field)), ^"%#{search}%")
        )
      end)

    where(query, [a], ^Enum.reduce(conditions, &dynamic([], ^&1 or ^&2)))
  end

  def search_filter(query, _, _), do: query

  @doc """
  Gets a single record by ID.

  ## Parameters
    - schema: The Ecto schema module to query
    - id: The ID of the record to fetch

  Raises `Ecto.NoResultsError` if the record does not exist.

  ## Examples
      iex> get_data!(User, 123)
      %User{}

      iex> get_data!(User, 456)
      ** (Ecto.NoResultsError)
  """
  def get_data!(schema, id), do: Repo.get!(schema, id)
end
