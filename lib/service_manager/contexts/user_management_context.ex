defmodule ServiceManager.Contexts.UserManagementContext do
  @moduledoc """
  The UserManagementContext context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  alias ServiceManager.Repo
  alias ServiceManager.Accounts.User

  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    User
    |> where([a], a.deletion_status == false)
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      # fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%") or
      # fragment("lower(?) LIKE lower(?)", a.name, ^"%#{search}%") or
      fragment("lower(?) LIKE lower(?)", a.first_name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.last_name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.phone_number, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.nickname, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.email, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  def user_restore(user_id) do
    user = get_data!(user_id)
    data = ServiceManager.Contexts.DeletedArchivesContext.get_deleted_archive_by_item_id(user_id)
    update_user(user, data.data)
  end

  @doc """
  Returns the list of users.

  ## Examples

      iex> list_users()
      [%User{}, ...]

  """
  def list_users do
    Repo.all(User)
  end

  @doc """
  Gets a single user.

  Raises `Ecto.NoResultsError` if the User does not exist.

  ## Examples

      iex> get_user!(123)
      %User{}

      iex> get_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_data!(id), do: Repo.get!(User, id)

  @doc """
  Creates a user.

  ## Examples

      iex> create_user(%{field: value})
      {:ok, %User{}}

      iex> create_user(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_user(attrs \\ %{}) do
    %User{}
    |> User.changeset(attrs)
    |> Repo.insert()
  end

  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> Map.put("password", ServiceManager.Utilities.password())
      |> create_user()
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: user.id,
    #     activity: "User created",
    #     description: "Inserted a new user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Updates a user.

  ## Examples

      iex> update_user(user, %{field: new_value})
      {:ok, %User{}}

      iex> update_user(user, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_user(%User{} = user, attrs) do
    user
    |> User.update_changeset_ui(attrs)
    |> Repo.update()
  end

  def activate_user(%User{} = user, attrs) do
    user
    |> User.activate_update_changeset(attrs)
    |> Repo.update()
  end

  def update_data(%User{} = user_record, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> update_user(Map.put(params, "updated_by", user.id))
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def partial_delete(%User{} = user_record, user, reason \\ "") do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> User.deletion_changeset(%{
        "updated_by" => user.id,
        "customer_no" => "#{user.customer_no}Archived",
        "phone_number" => "#{user.phone_number}Archived",
        "account_number" => "#{user.account_number}Archived",
        "email" => "#{user.email}Archived"
      })
      |> Repo.update()
    end)
    |> Ecto.Multi.run(:archive_deletion, fn _, _ ->
      ServiceManager.Contexts.DeletedArchivesContext.create_deleted_archive(
        "User",
        user_record,
        user,
        reason
      )
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def user_activation(user_record, params, user) do
    password = ServiceManager.Utilities.password()

    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      #
      user_record
      |> activate_user(
        Map.put(params, "updated_by", user.id)
        |> Map.put("password", password)
      )
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Deletes a user.

  ## Examples

      iex> delete_user(user)
      {:ok, %User{}}

      iex> delete_user(user)
      {:error, %Ecto.Changeset{}}

  """
  def delete_data(%User{} = user) do
    Repo.delete(user)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user changes.

  ## Examples

      iex> change_user(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_data(%User{} = user, attrs \\ %{}) do
    User.changeset(user, attrs)
  end
end
