defmodule ServiceManager.Contexts.SystemUserManagementContext do
  @moduledoc """
  The UserManagementContext context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.AdminUsers, as: User
  alias ServiceManager.ThirdParty.ThirdPartyApiKey, as: APIKey

  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    User
    |> DefaultQueries.status_query()
    #    |> preload([:maker, :checker])
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      # fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%") or
      # fragment("lower(?) LIKE lower(?)", a.name, ^"%#{search}%") or
      fragment("lower(?) LIKE lower(?)", a.first_name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.last_name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.phone_number, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.nickname, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.email, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  @doc """
  Returns the list of users.

  ## Examples

      iex> list_users()
      [%User{}, ...]

  """
  def list_users do
    Repo.all(User)
  end

  @doc """
  Gets a single user.

  Raises `Ecto.NoResultsError` if the User does not exist.

  ## Examples

      iex> get_user!(123)
      %User{}

      iex> get_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_data!(id), do: Repo.get!(User, id)

  @doc """
  Creates a user.

  ## Examples

      iex> create_user(%{field: value})
      {:ok, %User{}}

      iex> create_user(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_user(attrs \\ %{}) do
    %User{}
    |> User.changeset(attrs)
    |> Repo.insert()
  end

  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> Map.put("password", ServiceManager.Utilities.password())
      |> create_user()
    end)
    |> Repo.transaction()
  end

  @doc """
  Updates a user.

  ## Examples

      iex> update_user(user, %{field: new_value})
      {:ok, %User{}}

      iex> update_user(user, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_user(%User{} = user, attrs) do
    user
    |> User.update_changeset(attrs)
    |> Repo.update()
  end

  def activate_user(%User{} = user, attrs) do
    user
    |> User.activate_update_changeset(attrs)
    |> Repo.update()
  end

  def update_data(%User{} = user_record, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> update_user(Map.put(params, "updated_by", user.id))
    end)
    |> Repo.transaction()
  end

  def user_activation(user_record, user) do
    password = ServiceManager.Utilities.password()

    params = %{
      updated_by: user.id,
      password: password,
      status: "active",
      activation_status: "active"
    }

    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> activate_user(params)
    end)
    |> Ecto.Multi.run(:send_sms, fn _repo, %{data_struct: data_struct} ->
      ServiceManager.Contexts.SmsNotificationsContext.create_method(%{
        msisdn: data_struct.phone_number,
        user_id: data_struct.id,
        message:
          "Hello #{data_struct.first_name}, \n Please utilize the provided details to log in to the FDH mobile money backend portal. \nEmail: #{data_struct.email} \nPassword: #{password}"
      })
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def password_reset(user_record, user) do
    password = ServiceManager.Utilities.password()

    params = %{
      updated_by: user.id,
      password: password
    }

    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> activate_user(params)
    end)
    |> Ecto.Multi.run(:send_sms, fn _repo, %{data_struct: data_struct} ->
      ServiceManager.Contexts.SmsNotificationsContext.create_method(%{
        msisdn: data_struct.phone_number,
        user_id: data_struct.id,
        message:
          "Hello #{data_struct.first_name}, \n Please utilize the provided details to log in to the FDH mobile money backend portal. \nEmail: #{data_struct.email} \nPassword: #{password}"
      })
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def password_reset(user_record) do
    password = ServiceManager.Utilities.password()

    params = %{
      updated_by: user_record.id,
      password: password
    }

    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> activate_user(params)
    end)
    |> Ecto.Multi.run(:send_sms, fn _repo, %{data_struct: data_struct} ->
      ServiceManager.Contexts.SmsNotificationsContext.create_method(%{
        msisdn: data_struct.phone_number,
        user_id: data_struct.id,
        message:
          "Hello #{data_struct.first_name}, \n Please utilize the provided details to log in to the FDH mobile money backend portal. \nEmail: #{data_struct.email} \nPassword: #{password}"
      })
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def status_change(user_record, user, params \\ %{}) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:data_struct, fn _ ->
      User.status_changeset(user_record, params)
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def update_user_password(user_record, params) do
    new_params =
      params
      |> Map.put("updated_by", user_record.id)

    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      User.change_update_password_changeset(user_record, new_params)
      |> Repo.update()
    end)
    |> Ecto.Multi.run(:send_sms, fn _repo, %{data_struct: data_struct} ->
      ServiceManager.Contexts.SmsNotificationsContext.create_method(%{
        # Note: access phone_number through data_struct.data
        msisdn: data_struct.phone_number,
        user_id: data_struct.id,
        message:
          "Hello #{data_struct.first_name}, \n Please utilize the provided details to log in to the FDH mobile money backend portal. \nEmail: #{data_struct.email} \nPassword: #{params["password"]}"
      })
    end)
    |> Repo.transaction()
  end

  @doc """
  Deletes a user.

  ## Examples

      iex> delete_user(user)
      {:ok, %User{}}

      iex> delete_user(user)
      {:error, %Ecto.Changeset{}}

  """
  def delete_data(%User{} = user) do
    Repo.delete(user)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user changes.

  ## Examples

      iex> change_user(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_data(%User{} = user, attrs \\ %{}) do
    User.changeset(user, attrs)
  end

  def change_password_data(%User{} = user, attrs \\ %{}) do
    User.change_update_password_changeset(user, attrs)
  end

  def get_user_by_email_and_password(email, password)
      when is_binary(email) and is_binary(password) do
    user = Repo.get_by(User, email: email)
    if User.valid_password?(user, password), do: user
  end

  def verify_api_key(api_key) do
    case APIKey.find_by(api_key: api_key) do
      nil ->
        {:error, "Invalid API key"}

      api_key ->
        case User.find(api_key.user_id) do
          nil -> {:error, "API Not Linked"}
          user -> {:ok, user}
        end
    end
  end

  def stats() do
    total = Repo.aggregate(User, :count, :id)
    active = Repo.aggregate(from(c in User, where: c.status == "active"), :count, :id)
    inactive = Repo.aggregate(from(c in User, where: c.status == "inactive"), :count, :id)
    blocked = Repo.aggregate(from(c in User, where: c.status == "blocked"), :count, :id)
    deleted = Repo.aggregate(from(c in User, where: c.status == "deleted"), :count, :id)

    pending =
      Repo.aggregate(from(c in User, where: c.activation_status == "pending"), :count, :id)

    activated =
      Repo.aggregate(from(c in User, where: c.activation_status == "activated"), :count, :id)

    %{
      total: total,
      active: active,
      blocked: blocked,
      deleted: deleted,
      pending: pending,
      activated: activated,
      inactive: inactive
    }
  end

  # ServiceManager.Contexts.SystemUserManagementContext.get_user_by_email_and_password("<EMAIL>", "Qwerty123456")
end
