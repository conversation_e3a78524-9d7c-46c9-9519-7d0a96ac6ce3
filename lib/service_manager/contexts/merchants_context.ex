defmodule ServiceManager.Contexts.MerchantsContext do
  @moduledoc """
  The Merchants context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.{Merchant, MerchantTransaction}
  alias Ecto.Multi

  @doc """
  Returns the list of merchants.
  """
  def list_merchants do
    Repo.all(Merchant)
  end

  @doc """
  Gets a single merchant.
  Raises `Ecto.NoResultsError` if the Merchant does not exist.
  """
  def get_merchant!(id), do: Repo.get!(Merchant, id)

  @doc """
  Gets a single merchant by merchant_code.
  Returns nil if the Merchant does not exist.
  """
  def get_merchant_by_code(merchant_code) do
    Repo.get_by(Merchant, merchant_code: merchant_code)
  end

  @doc """
  Creates a merchant.
  """
  def create_merchant(attrs \\ %{}) do
    attrs = Map.put_new_lazy(attrs, "merchant_code", &Merchant.generate_merchant_code/0)

    %Merchant{}
    |> Merchant.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a merchant.
  """
  def update_merchant(%Merchant{} = merchant, attrs) do
    merchant
    |> Merchant.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a merchant.
  """
  def delete_merchant(%Merchant{} = merchant) do
    Repo.delete(merchant)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking merchant changes.
  """
  def change_merchant(%Merchant{} = merchant, attrs \\ %{}) do
    Merchant.changeset(merchant, attrs)
  end

  @doc """
  Process a payment from a user to a merchant.
  """
  def process_payment(user, params) do
    merchant = get_merchant_by_code(params["merchant_id"])

    # Convert amount to Decimal, handling both string and number inputs
    amount =
      case params["transaction_amount"] do
        amount when is_binary(amount) ->
          case Decimal.parse(amount) do
            {decimal, ""} -> decimal
            _ -> nil
          end

        amount when is_number(amount) ->
          Decimal.new("#{amount}")

        _ ->
          nil
      end

    # Convert user's float balance to Decimal for comparison
    user_balance = Decimal.new("#{user.account_balance || 0.0}")

    cond do
      is_nil(merchant) ->
        {:error, :merchant_not_found}

      is_nil(user) ->
        {:error, :user_not_found}

      is_nil(amount) ->
        {:error, :invalid_amount}

      Decimal.lt?(user_balance, amount) ->
        {:error, :insufficient_funds}

      true ->
        Multi.new()
        |> Multi.update(:debit_user, fn _ ->
          new_balance = user.account_balance - Decimal.to_float(amount)
          Ecto.Changeset.change(user, account_balance: new_balance)
        end)
        |> Multi.update(:credit_merchant, fn _ ->
          merchant_balance = Decimal.add(merchant.balance |> Decimal.from_float(), amount)
          Merchant.balance_changeset(merchant, merchant_balance)
        end)
        |> Multi.insert(:transaction, fn _ ->
          %MerchantTransaction{}
          |> MerchantTransaction.changeset(%{
            merchant_id: merchant.id,
            transaction_id: params["transaction_id"],
            amount: amount,
            currency: params["currency"],
            qr_code_data: params["qr_code_data"],
            description: params["payment_description"],
            callback_url: params["callback_url"],
            status: "completed"
          })
        end)
        |> Repo.transaction()
        |> case do
          {:ok, %{transaction: transaction, debit_user: user, credit_merchant: merchant}} ->
            {:ok, %{transaction: transaction, user: user, merchant: merchant}}

          {:error, failed_operation, failed_value, _changes} ->
            {:error, {failed_operation, failed_value}}
        end
    end
  end

  @doc """
  Get a transaction by ID
  """
  def get_transaction!(id), do: Repo.get!(MerchantTransaction, id)

  @doc """
  Get a transaction by transaction_id
  """
  def get_transaction_by_id(transaction_id) do
    Repo.get_by(MerchantTransaction, transaction_id: transaction_id)
  end

  @doc """
  Update transaction callback status
  """
  def update_transaction_callback(%MerchantTransaction{} = transaction, status, response) do
    transaction
    |> MerchantTransaction.changeset(%{
      callback_status: status,
      callback_response: response
    })
    |> Repo.update()
  end
end
