defmodule ServiceManager.Contexts.AccountingContext do
  @moduledoc """
  The AccountingContext handles all accounting-related operations.
  This includes managing the chart of accounts (ledgers) and creating double-entry accounting records.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounting.AccountingLedger
  alias ServiceManager.Schemas.Accounting.AccountingEntry
  alias ServiceManager.Transactions.Transaction
  alias ServiceManager.Transactions.WalletTransactions
  alias ServiceManager.Broadcasters.LoggerBroadcaster, as: <PERSON><PERSON>

  @doc """
  Gets a list of all accounting ledgers.
  """
  def list_ledgers do
    Repo.all(AccountingLedger)
  end

  @doc """
  Gets a list of active accounting ledgers.
  """
  def list_active_ledgers do
    AccountingLedger
    |> where([l], l.is_active == true)
    |> Repo.all()
  end

  @doc """
  Gets a single accounting ledger by ID.
  """
  def get_ledger(id), do: Repo.get(AccountingLedger, id)

  @doc """
  Gets a single accounting ledger by code.
  """
  def get_ledger_by_code(code) do
    AccountingLedger
    |> where([l], l.code == ^code)
    |> Repo.one()
  end

  @doc """
  Creates a new accounting ledger.
  """
  def create_ledger(attrs \\ %{}) do
    %AccountingLedger{}
    |> AccountingLedger.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates an accounting ledger.
  """
  def update_ledger(%AccountingLedger{} = ledger, attrs) do
    ledger
    |> AccountingLedger.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Gets a list of accounting entries.
  """
  def list_entries do
    Repo.all(AccountingEntry)
  end

  @doc """
  Gets a single accounting entry by ID.
  """
  def get_entry(id), do: Repo.get(AccountingEntry, id)

  @doc """
  Gets accounting entries by transaction reference.
  """
  def get_entries_by_reference(reference) do
    AccountingEntry
    |> where([e], e.reference == ^reference)
    |> Repo.all()
  end

  @doc """
  Creates a new accounting entry.
  """
  def create_entry(attrs \\ %{}) do
    %AccountingEntry{}
    |> AccountingEntry.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates double entry accounting records for a transaction.
  This function creates both debit and credit entries for a transaction.
  """
  def create_double_entry_for_transaction(%Transaction{} = transaction, user_id \\ nil) do
    Logger.info("Creating double entry accounting records for transaction #{transaction.id}")

    # Determine the appropriate ledger accounts based on transaction type
    {debit_ledger_code, credit_ledger_code} = determine_ledger_accounts_for_transaction(transaction)

    # Get the ledger accounts
    with {:ok, debit_ledger} <- get_or_create_ledger(debit_ledger_code),
         {:ok, credit_ledger} <- get_or_create_ledger(credit_ledger_code) do
      
      # Create the transaction in a database transaction to ensure both entries are created or none
      Repo.transaction(fn ->
        # Create debit entry
        debit_attrs = %{
          entry_type: "debit",
          amount: transaction.amount,
          description: transaction.description,
          transaction_date: DateTime.utc_now(),
          value_date: transaction.value_date,
          reference: transaction.reference,
          external_reference: transaction.external_reference,
          status: "posted",
          currency: "MWK", # Default currency, could be made dynamic
          ledger_id: debit_ledger.id,
          transaction_id: transaction.id,
          created_by: user_id
        }

        # Create credit entry
        credit_attrs = %{
          entry_type: "credit",
          amount: transaction.amount,
          description: transaction.description,
          transaction_date: DateTime.utc_now(),
          value_date: transaction.value_date,
          reference: transaction.reference,
          external_reference: transaction.external_reference,
          status: "posted",
          currency: "MWK", # Default currency, could be made dynamic
          ledger_id: credit_ledger.id,
          transaction_id: transaction.id,
          created_by: user_id
        }

        with {:ok, debit_entry} <- create_entry(debit_attrs),
             {:ok, credit_entry} <- create_entry(credit_attrs) do
          {debit_entry, credit_entry}
        else
          {:error, changeset} -> Repo.rollback(changeset)
        end
      end)
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  Creates double entry accounting records for a wallet transaction.
  This function creates both debit and credit entries for a wallet transaction.
  """
  def create_double_entry_for_wallet_transaction(%WalletTransactions{} = transaction, user_id \\ nil) do
    Logger.info("Creating double entry accounting records for wallet transaction #{transaction.id}")

    # Determine the appropriate ledger accounts based on transaction type
    {debit_ledger_code, credit_ledger_code} = determine_ledger_accounts_for_wallet_transaction(transaction)

    # Get the ledger accounts
    with {:ok, debit_ledger} <- get_or_create_ledger(debit_ledger_code),
         {:ok, credit_ledger} <- get_or_create_ledger(credit_ledger_code) do
      
      # Create the transaction in a database transaction to ensure both entries are created or none
      Repo.transaction(fn ->
        # Create debit entry
        debit_attrs = %{
          entry_type: "debit",
          amount: transaction.amount,
          description: transaction.description,
          transaction_date: DateTime.utc_now(),
          value_date: transaction.value_date,
          reference: transaction.reference,
          external_reference: transaction.external_reference,
          status: "posted",
          currency: "MWK", # Default currency, could be made dynamic
          ledger_id: debit_ledger.id,
          wallet_transaction_id: transaction.id,
          created_by: user_id
        }

        # Create credit entry
        credit_attrs = %{
          entry_type: "credit",
          amount: transaction.amount,
          description: transaction.description,
          transaction_date: DateTime.utc_now(),
          value_date: transaction.value_date,
          reference: transaction.reference,
          external_reference: transaction.external_reference,
          status: "posted",
          currency: "MWK", # Default currency, could be made dynamic
          ledger_id: credit_ledger.id,
          wallet_transaction_id: transaction.id,
          created_by: user_id
        }

        with {:ok, debit_entry} <- create_entry(debit_attrs),
             {:ok, credit_entry} <- create_entry(credit_attrs) do
          {debit_entry, credit_entry}
        else
          {:error, changeset} -> Repo.rollback(changeset)
        end
      end)
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # Helper function to determine ledger accounts for a transaction
  defp determine_ledger_accounts_for_transaction(transaction) do
    case transaction.type do
      "credit" -> 
        {"1000", "4000"} # Debit Cash, Credit Revenue
      "debit" -> 
        {"5000", "1000"} # Debit Expense, Credit Cash
      "transfer" -> 
        {"1000", "1000"} # Debit Cash, Credit Cash (different accounts in practice)
      "direct-transfer" -> 
        {"1000", "1000"} # Debit Cash, Credit Cash (different accounts in practice)
      "external-transfer" -> 
        {"1000", "2000"} # Debit Cash, Credit Liability
      _ -> 
        {"1000", "1000"} # Default case
    end
  end

  # Helper function to determine ledger accounts for a wallet transaction
  defp determine_ledger_accounts_for_wallet_transaction(transaction) do
    case transaction.type do
      "credit" -> 
        {"1100", "4100"} # Debit Wallet Cash, Credit Wallet Revenue
      "debit" -> 
        {"5100", "1100"} # Debit Wallet Expense, Credit Wallet Cash
      "transfer" -> 
        {"1100", "1100"} # Debit Wallet Cash, Credit Wallet Cash (different accounts in practice)
      _ -> 
        {"1100", "1100"} # Default case
    end
  end

  # Helper function to get or create a ledger account
  defp get_or_create_ledger(code) do
    case get_ledger_by_code(code) do
      nil -> 
        # Create a default ledger if it doesn't exist
        create_default_ledger(code)
      ledger -> 
        {:ok, ledger}
    end
  end

  # Helper function to create a default ledger account
  defp create_default_ledger(code) do
    {name, type, category} = get_default_ledger_info(code)
    
    attrs = %{
      code: code,
      name: name,
      account_type: type,
      account_category: category,
      is_system: true
    }
    
    case create_ledger(attrs) do
      {:ok, ledger} -> {:ok, ledger}
      {:error, changeset} -> {:error, "Failed to create ledger: #{inspect(changeset.errors)}"}
    end
  end

  # Helper function to get default ledger information based on code
  defp get_default_ledger_info(code) do
    case code do
      "1000" -> {"Cash", "asset", "current_asset"}
      "1100" -> {"Wallet Cash", "asset", "current_asset"}
      "2000" -> {"Accounts Payable", "liability", "current_liability"}
      "3000" -> {"Capital", "equity", "capital"}
      "4000" -> {"Revenue", "revenue", "operating_revenue"}
      "4100" -> {"Wallet Revenue", "revenue", "operating_revenue"}
      "5000" -> {"Expenses", "expense", "operating_expense"}
      "5100" -> {"Wallet Expenses", "expense", "operating_expense"}
      _ -> {"Miscellaneous", "asset", "current_asset"}
    end
  end
end
