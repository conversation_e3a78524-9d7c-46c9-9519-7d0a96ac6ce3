defmodule ServiceManager.Contexts.SystemConfiguarationsContext do
  @moduledoc """
  The NotificationsConfigContext handles interactions with notification system settings,
  including email, SMS and push notification configurations.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Settings.Config, as: Notifications

  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    Notifications
    |> DefaultQueries.status_query()
    # |> preload([:from_account, :to_account])
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.key, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.value, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  @doc """
  Gets the notification settings schema, either returning the existing settings
  or creating a new empty schema if none exists.

  ## Returns
    - %Notifications{} - Either existing settings or new empty schema
  """
  def get_setting_schema(key) do
    get_setting(key)
  end

  @doc """
  Gets the current active notification settings configuration.
  Returns the most recent notification settings record.

  ## Returns
    - %Notifications{} | nil
  """
  def get_setting(key) do
    Notifications
    |> where([n], n.key == ^key)
    |> order_by([n], desc: n.inserted_at)
    |> limit(1)
    |> Repo.one()
  end

  def get_setting_by_id(id) do
    Notifications
    |> where([n], n.id == ^id)
    |> Repo.one()
  end

  def data_schema() do
    %Notifications{}
  end

  @doc """
  Creates new notification settings.

  ## Parameters
    - attrs: Map of attributes for the new settings
      - :email_notifications_enabled - Boolean to enable/disable email notifications
      - :sms_notifications_enabled - Boolean to enable/disable SMS notifications 
      - :push_notifications_enabled - Boolean to enable/disable push notifications
      - Other email, SMS and push notification specific configs

  ## Returns
    - {:ok, %Notifications{}} on success
    - {:error, changeset} on validation failure
  """
  def create_setting(attrs \\ %{}) do
    %Notifications{}
    |> Notifications.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates existing notification settings.

  ## Parameters
    - notifications: Existing notification settings struct to update
    - attrs: Map of attributes to update with the same structure as create_setting/1

  ## Returns
    - {:ok, %Notifications{}} on success
    - {:error, changeset} on validation failure
  """
  def update_setting(%Notifications{} = notifications, attrs) do
    notifications
    |> Notifications.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Returns a changeset for tracking notification settings changes.

  ## Parameters
    - notifications: The notifications struct to create a changeset for
    - attrs: Optional map of changes to apply
  """
  def change_setting(%Notifications{} = notifications, attrs \\ %{}) do
    Notifications.changeset(notifications, attrs)
  end

  @doc """
  Inserts new notification settings with user tracking in a transaction.

  ## Parameters
    - params: Settings parameters map
    - user: User struct of the user performing the action

  ## Returns
    - {:ok, %{data_struct: %Notifications{}, notifications_cache: term()}} on success
    - {:error, failed_operation, failed_value, changes_so_far} on failure
  """
  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> create_setting()
    end)
    |> multi_cache()
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Updates existing notification settings with user tracking in a transaction.

  ## Parameters
    - notifications: Existing notification settings struct to update
    - params: Update parameters map
    - user: User struct of the user performing the update

  ## Returns
    - {:ok, %{data_struct: %Notifications{}, notifications_cache: term()}} on success
    - {:error, failed_operation, failed_value, changes_so_far} on failure
  """
  def update_data(%Notifications{} = notifications, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      notifications
      |> update_setting(Map.put(params, "updated_by", user.id))
    end)
    |> multi_cache()
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  # Private Functions

  def delete_settings(%Notifications{} = data) do
    Repo.delete(data)
  end

  defp multi_cache(multi) do
    multi
    |> Ecto.Multi.run(:notifications_cache, fn _, %{data_struct: data_struct} ->
      {:ok, put_cache(data_struct)}
    end)
  end

  defp put_cache(data) do
    Cachex.put(:settings, data.key, data)
  end
end
