defmodule ServiceManager.Contexts.TransactionHistoryContext do
  @moduledoc """
  Context module for transaction history and balance snapshots.
  Provides functions to query transaction status history and balance snapshots.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Transactions.TransactionStatusHistory
  alias ServiceManager.Accounts.BalanceSnapshot
  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries

  @query_params Application.compile_env(:service_manager, :query_params)

  @doc """
  Gets the status history for a transaction.
  
  ## Parameters
    - transaction_id: The ID of the transaction
  
  ## Returns
    - List of status history entries, ordered by insertion date (newest first)
  """
  def get_transaction_status_history(transaction_id) do
    TransactionStatusHistory
    |> where([h], h.transaction_id == ^transaction_id)
    |> order_by([h], desc: h.inserted_at)
    |> Repo.all()
  end

  @doc """
  Gets the latest status change for a transaction.
  
  ## Parameters
    - transaction_id: The ID of the transaction
  
  ## Returns
    - The latest status history entry, or nil if none exists
  """
  def get_latest_status_change(transaction_id) do
    TransactionStatusHistory
    |> where([h], h.transaction_id == ^transaction_id)
    |> order_by([h], desc: h.inserted_at)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Gets all status history entries with pagination.
  
  ## Parameters
    - params: Query parameters for filtering and pagination
  
  ## Returns
    - Paginated list of status history entries
  """
  def list_status_history(params \\ @query_params) do
    TransactionStatusHistory
    |> order_by([h], desc: h.inserted_at)
    |> DefaultQueries.pagination_query(params)
  end

  @doc """
  Gets balance snapshots for a transaction.
  
  ## Parameters
    - transaction_id: The ID of the transaction
  
  ## Returns
    - List of balance snapshots, ordered by insertion date (newest first)
  """
  def get_transaction_balance_snapshots(transaction_id) do
    BalanceSnapshot
    |> where([s], s.transaction_id == ^transaction_id)
    |> order_by([s], desc: s.inserted_at)
    |> Repo.all()
  end

  @doc """
  Gets balance snapshots for an account.
  
  ## Parameters
    - account_id: The ID of the account
    - params: Query parameters for filtering and pagination
  
  ## Returns
    - Paginated list of balance snapshots
  """
  def get_account_balance_snapshots(account_id, params \\ @query_params) do
    BalanceSnapshot
    |> where([s], s.account_id == ^account_id)
    |> order_by([s], desc: s.inserted_at)
    |> DefaultQueries.pagination_query(params)
  end

  @doc """
  Gets pre-transaction balance snapshots for a transaction.
  
  ## Parameters
    - transaction_id: The ID of the transaction
  
  ## Returns
    - List of pre-transaction balance snapshots
  """
  def get_pre_transaction_snapshots(transaction_id) do
    BalanceSnapshot
    |> where([s], s.transaction_id == ^transaction_id and s.balance_type == "pre")
    |> order_by([s], desc: s.inserted_at)
    |> Repo.all()
  end

  @doc """
  Gets post-transaction balance snapshots for a transaction.
  
  ## Parameters
    - transaction_id: The ID of the transaction
  
  ## Returns
    - List of post-transaction balance snapshots
  """
  def get_post_transaction_snapshots(transaction_id) do
    BalanceSnapshot
    |> where([s], s.transaction_id == ^transaction_id and s.balance_type == "post")
    |> order_by([s], desc: s.inserted_at)
    |> Repo.all()
  end

  @doc """
  Gets all balance snapshots with pagination.
  
  ## Parameters
    - params: Query parameters for filtering and pagination
  
  ## Returns
    - Paginated list of balance snapshots
  """
  def list_balance_snapshots(params \\ @query_params) do
    BalanceSnapshot
    |> order_by([s], desc: s.inserted_at)
    |> DefaultQueries.pagination_query(params)
  end
end
