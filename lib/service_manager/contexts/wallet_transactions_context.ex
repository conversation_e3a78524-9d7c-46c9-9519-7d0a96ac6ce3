defmodule ServiceManager.Contexts.WalletTransactionsContext do
  @moduledoc """
  Context module for handling wallet transactions operations.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Transactions.WalletTransactions, as: MainSchema
  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries

  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    MainSchema
    |> DefaultQueries.status_query(params)
    |> preload([:from_account, :to_account])
    |> DefaultQueries.sorting_query(params)
    |> DefaultQueries.date_range_query(params)
    |> DefaultQueries.amount_range_query(params)
    |> DefaultQueries.type_query(params)
    |> DefaultQueries.reference_query(params)
    |> DefaultQueries.from_account_query(params)
    |> DefaultQueries.to_account_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def retrieve_report_data(params \\ @query_params) do
    MainSchema
    |> DefaultQueries.status_query(params)
    |> preload([:from_account, :to_account])
    |> DefaultQueries.sorting_query(params)
    |> DefaultQueries.date_range_query(params)
    |> DefaultQueries.amount_range_query(params)
    |> DefaultQueries.type_query(params)
    |> DefaultQueries.reference_query(params)
    |> DefaultQueries.from_account_query(params)
    |> DefaultQueries.to_account_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> Repo.all()
  end

  def search_filter(query, %{"mobile_number" => mobile_number})
      when is_nil(mobile_number) or mobile_number == "",
      do: query

  def search_filter(query, %{"mobile_number" => mobile_number}) do
    query
    |> join(:left, [a], fa in assoc(a, :from_account))
    |> join(:left, [a], ta in assoc(a, :to_account))
    |> where(
      [a, fa, ta],
      fragment("lower(?) LIKE lower(?)", fa.mobile_number, ^"%#{mobile_number}%") or
        fragment("lower(?) LIKE lower(?)", ta.mobile_number, ^"%#{mobile_number}%")
    )
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> join(:left, [a], fa in assoc(a, :from_account))
    |> join(:left, [a], ta in assoc(a, :to_account))
    |> where(
      [a, fa, ta],
      fragment("lower(?) LIKE lower(?)", a.type, ^"%#{search}%") or
        fragment("CAST(? AS TEXT) LIKE ?", a.amount, ^"%#{search}%") or
        fragment("CAST(? AS TEXT) LIKE ?", a.credit_amount, ^"%#{search}%") or
        fragment("CAST(? AS TEXT) LIKE ?", a.debit_amount, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.description, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.reference, ^"%#{search}%") or
        fragment("TO_CHAR(?, 'YYYY-MM-DD') LIKE ?", a.value_date, ^"%#{search}%") or
        fragment("CAST(? AS TEXT) LIKE ?", a.opening_balance, ^"%#{search}%") or
        fragment("CAST(? AS TEXT) LIKE ?", a.closing_balance, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", fa.name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", ta.name, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  def retrieve_by_mobile(params \\ @query_params) do
    MainSchema
    |> mobile_number_filter(params)
    |> DefaultQueries.status_query(params)
    |> preload([:from_account, :to_account])
    |> DefaultQueries.sorting_query(params)
    |> DefaultQueries.date_range_query(params)
    |> DefaultQueries.amount_range_query(params)
    |> DefaultQueries.type_query(params)
    |> DefaultQueries.reference_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> Repo.all()
  end

  defp mobile_number_filter(query, %{"mobile_number" => mobile_number})
       when is_nil(mobile_number) or mobile_number == "",
       do: query

  defp mobile_number_filter(query, %{"mobile_number" => mobile_number}) do
    query
    |> join(:left, [a], fa in assoc(a, :from_account))
    |> join(:left, [a], ta in assoc(a, :to_account))
    |> where(
      [a, fa, ta],
      fragment("lower(?) LIKE lower(?)", fa.mobile_number, ^"%#{mobile_number}%") or
        fragment("lower(?) LIKE lower(?)", ta.mobile_number, ^"%#{mobile_number}%")
    )
  end

  defp mobile_number_filter(query, _), do: query

  @doc """
  Gets a single wallet transaction.

  Returns nil if the wallet transaction does not exist.

  ## Examples

      iex> get_wallet_transaction(123)
      %WalletTransactions{}

      iex> get_wallet_transaction(456)
      nil

  """
  def get_data(id), do: Repo.get(MainSchema, id)

  @doc """
  Gets a single wallet transaction.

  Raises `Ecto.NoResultsError` if the wallet transaction does not exist.

  ## Examples

      iex> get_wallet_transaction!(123)
      %WalletTransactions{}

      iex> get_wallet_transaction!(456)
      ** (Ecto.NoResultsError)

  """
  def get_data!(id), do: Repo.get!(MainSchema, id)

  @doc """
  Creates a wallet transaction.

  ## Examples

      iex> create_wallet_transaction(%{field: value})
      {:ok, %WalletTransactions{}}

      iex> create_wallet_transaction(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create(attrs \\ %{}) do
    %MainSchema{}
    |> MainSchema.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a wallet transaction.

  ## Examples

      iex> update_wallet_transaction(wallet_transaction, %{field: new_value})
      {:ok, %WalletTransactions{}}

      iex> update_wallet_transaction(wallet_transaction, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update(%MainSchema{} = wallet_transaction, attrs) do
    wallet_transaction
    |> MainSchema.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a wallet transaction.

  ## Examples

      iex> delete_wallet_transaction(wallet_transaction)
      {:ok, %WalletTransactions{}}

      iex> delete_wallet_transaction(wallet_transaction)
      {:error, %Ecto.Changeset{}}

  """
  def delete(%MainSchema{} = wallet_transaction) do
    Repo.delete(wallet_transaction)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking wallet transaction changes.

  ## Examples

      iex> change_wallet_transaction(wallet_transaction)
      %Ecto.Changeset{data: %WalletTransactions{}}

  """
  def change(%MainSchema{} = wallet_transaction, attrs \\ %{}) do
    MainSchema.changeset(wallet_transaction, attrs)
  end

  @doc """
  Gets all pending wallet transactions.
  Returns a list of transactions with status "pending" that are wallet transactions.
  """
  def get_pending_wallet_transactions do
    query =
      from(t in MainSchema,
        where: t.status == "pending",
        where: t.type in ["credit", "debit", "transfer"],
        order_by: [asc: t.inserted_at]
      )

    query
    |> Repo.all()
    |> Repo.preload(
      from_wallet:
        from(w in ServiceManager.WalletAccounts.WalletUser,
          select: [:id, :mobile_number, :balance]
        ),
      to_wallet:
        from(w in ServiceManager.WalletAccounts.WalletUser,
          select: [:id, :mobile_number, :balance]
        )
    )
  end

  @doc """
  Gets all wallet transactions.
  Returns a list of all wallet transactions.
  """
  def get_all_transactions do
    MainSchema
    |> order_by([t], asc: t.inserted_at)
    |> Repo.all()
    |> Repo.preload(
      from_wallet:
        from(w in ServiceManager.WalletAccounts.WalletUser,
          select: [:id, :mobile_number, :balance]
        ),
      to_wallet:
        from(w in ServiceManager.WalletAccounts.WalletUser,
          select: [:id, :mobile_number, :balance]
        )
    )
  end

  @doc """
  Updates a transaction with the given attributes.
  """
  def update_transaction(%MainSchema{} = transaction, attrs) do
    transaction
    |> MainSchema.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Gets a wallet by ID with a lock for update.
  """
  def get_wallet_with_lock(wallet_id) do
    Repo.one(
      from w in ServiceManager.WalletAccounts.WalletUser,
        where: w.id == ^wallet_id,
        lock: "FOR UPDATE"
    )
  end
end
