defmodule ServiceManager.Contexts.ExchangeRateContext do
  @moduledoc """
  The ExchangeRateContext context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo

  alias ServiceManager.Schemas.ExchangeRate

  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    ExchangeRate
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.from_currency_code, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.to_currency_code, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  @doc """
  Gets the exchange rate between two currencies.
  Returns {:ok, rate} if found, {:error, reason} otherwise.
  """
  def get_exchange_rate(from_currency, to_currency) do
    # If currencies are the same, return 1.0
    if from_currency == to_currency do
      {:ok, Decimal.new(1)}
    else
      # Try direct conversion
      case get_direct_rate(from_currency, to_currency) do
        {:ok, rate} ->
          {:ok, rate}

        {:error, _} ->
          # Try conversion through MWK as base currency
          with {:ok, to_mwk} <- get_direct_rate(from_currency, "MWK"),
               {:ok, from_mwk} <- get_direct_rate("MWK", to_currency) do
            {:ok, Decimal.mult(to_mwk, from_mwk)}
          else
            _ -> {:error, :rate_not_found}
          end
      end
    end
  end

  defp get_direct_rate(from_currency, to_currency) do
    ExchangeRate
    |> where([e], e.from_currency_code == ^from_currency and e.to_currency_code == ^to_currency)
    |> select([e], e.rate)
    |> Repo.one()
    |> case do
      nil -> {:error, :rate_not_found}
      rate -> {:ok, Decimal.new(rate)}
    end
  end

  @doc """
  Returns the list of exchange_rates.

  ## Examples

      iex> list_exchange_rates()
      [%ExchangeRate{}, ...]

  """
  def list_exchange_rates do
    Repo.all(ExchangeRate)
  end

  @doc """
  Gets a single exchange_rate.

  Raises `Ecto.NoResultsError` if the Exchange rate does not exist.

  ## Examples

      iex> get_exchange_rate!(123)
      %ExchangeRate{}

      iex> get_exchange_rate!(456)
      ** (Ecto.NoResultsError)

  """
  def get_exchange_rate!(id), do: Repo.get!(ExchangeRate, id)

  @doc """
  Creates a exchange_rate.

  ## Examples

      iex> create_exchange_rate(%{field: value})
      {:ok, %ExchangeRate{}}

      iex> create_exchange_rate(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_exchange_rate(attrs \\ %{}) do
    %ExchangeRate{}
    |> ExchangeRate.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a exchange_rate.

  ## Examples

      iex> update_exchange_rate(exchange_rate, %{field: new_value})
      {:ok, %ExchangeRate{}}

      iex> update_exchange_rate(exchange_rate, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_exchange_rate(%ExchangeRate{} = exchange_rate, attrs) do
    exchange_rate
    |> ExchangeRate.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a exchange_rate.

  ## Examples

      iex> delete_exchange_rate(exchange_rate)
      {:ok, %ExchangeRate{}}

      iex> delete_exchange_rate(exchange_rate)
      {:error, %Ecto.Changeset{}}

  """
  def delete_exchange_rate(%ExchangeRate{} = exchange_rate) do
    Repo.delete(exchange_rate)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking exchange_rate changes.

  ## Examples

      iex> change_exchange_rate(exchange_rate)
      %Ecto.Changeset{data: %ExchangeRate{}}

  """
  def change_exchange_rate(%ExchangeRate{} = exchange_rate, attrs \\ %{}) do
    ExchangeRate.changeset(exchange_rate, attrs)
  end

  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> create_exchange_rate()
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: user.id,
    #     activity: "User created",
    #     description: "Inserted a new user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def update_data(%ExchangeRate{} = user_record, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> update_exchange_rate(Map.put(params, "updated_by", user.id))
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: user.id,
    #     activity: "User updated",
    #     description: "#{user.email} }updated user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end
end
