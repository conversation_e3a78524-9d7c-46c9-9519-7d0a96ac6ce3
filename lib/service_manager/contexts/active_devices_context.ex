defmodule ServiceManager.Contexts.ActiveDevicesContext do
  @moduledoc """
  The ActiveDeviceContext context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo

  alias ServiceManager.Schemas.ActiveDevice

  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  @query_params Application.compile_env(:service_manager, :query_params)

  def list(params \\ @query_params) do
    ActiveDevice
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.device_id, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", type(a.user_id, :string), ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", type(a.wallet_user_id, :string), ^"%#{search}%") or
        fragment(
          "lower(?) LIKE lower(?)",
          type(a.third_party_api_key_id, :string),
          ^"%#{search}%"
        )
    )
  end

  def search_filter(query, _), do: query

  @doc """
  Gets a single ActiveDevice.

  Raises `Ecto.NoResultsError` if the ActiveDevice does not exist.

  ## Examples

      iex> get_active_device!(123)
      %ActiveDevice{}

      iex> get_active_device!(456)
      ** (Ecto.NoResultsError)

  """
  def get_data!(id), do: Repo.get!(ActiveDevice, id)

  @doc """
  Creates a ActiveDevice.

  ## Examples

      iex> create_active_device(%{field: value})
      {:ok, %ActiveDevice{}}

      iex> create_active_device(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_active_device(attrs \\ %{}) do
    %ActiveDevice{}
    |> ActiveDevice.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a ActiveDevice.

  ## Examples

      iex> update_active_device(ActiveDevice, %{field: new_value})
      {:ok, %ActiveDevice{}}

      iex> update_active_device(ActiveDevice, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_active_device(%ActiveDevice{} = ActiveDevice, attrs) do
    ActiveDevice
    |> ActiveDevice.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a ActiveDevice.

  ## Examples

      iex> delete_active_device(ActiveDevice)
      {:ok, %ActiveDevice{}}

      iex> delete_active_device(ActiveDevice)
      {:error, %Ecto.Changeset{}}

  """
  def delete_active_device(%ActiveDevice{} = ActiveDevice) do
    Repo.delete(ActiveDevice)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking ActiveDevice changes.

  ## Examples

      iex> change_active_device(ActiveDevice)
      %Ecto.Changeset{data: %ActiveDevice{}}

  """
  def change_active_device(%ActiveDevice{} = activeDevice, attrs \\ %{}) do
    ActiveDevice.changeset(activeDevice, attrs)
  end

  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> create_active_device()
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def update_data(%ActiveDevice{} = user_record, params, user \\ %{id: 1}) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> update_active_device(Map.put(params, "updated_by", user.id))
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end
end
