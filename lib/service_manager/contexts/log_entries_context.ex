defmodule ServiceManager.Contexts.LogEntriesContext do
  import Ecto.Query
  alias ServiceManager.{Repo, Logging.LogEntry}

  def retrieve(params \\ %{}) do
    {query, total_entries} =
      LogEntry
      |> apply_filters(params)
      |> apply_sorting(params)
      |> get_total_entries()

    entries =
      query
      |> apply_pagination(params)
      |> Repo.all()

    %{
      entries: entries,
      total_entries: total_entries,
      page_size: get_page_size(params),
      page_number: get_page_number(params)
    }
  end

  def get_data!(id), do: Repo.get!(LogEntry, id)

  def get_distinct_modules do
    LogEntry
    |> distinct(true)
    |> select([l], l.module_name)
    |> order_by(asc: :module_name)
    |> Repo.all()
  end

  def get_distinct_chain_ids do
    LogEntry
    |> distinct(true)
    |> select([l], l.chain_id)
    |> order_by(asc: :chain_id)
    |> Repo.all()
  end

  def get_distinct_functions do
    LogEntry
    |> distinct(true)
    |> select([l], l.function_name)
    |> order_by(asc: :function_name)
    |> Repo.all()
  end

  defp get_total_entries(query) do
    total = Repo.aggregate(query, :count, :id)
    {query, total}
  end

  @default_page_size 10

  defp get_page_size(%{"page_size" => size}) when is_binary(size) do
    case Integer.parse(size) do
      {num, _} when num > 0 -> num
      _ -> @default_page_size
    end
  end

  defp get_page_size(%{"page_size" => size}) when is_integer(size) and size > 0, do: size
  defp get_page_size(_), do: @default_page_size

  defp get_page_number(%{"page" => page}) when is_binary(page) do
    case Integer.parse(page) do
      {num, _} when num > 0 -> num
      _ -> 1
    end
  end

  defp get_page_number(%{"page" => page}) when is_integer(page) and page > 0, do: page
  defp get_page_number(_), do: 1

  defp apply_filters(query, params) do
    query
    |> maybe_filter_by_chain_id(params["chain_id"])
    |> maybe_filter_by_function_name(params["function_name"])
    |> maybe_filter_by_module_name(params["module_name"])
    |> maybe_filter_by_process_id(params["process_id"])
    |> maybe_filter_by_date_range(params["start_date"], params["end_date"])
    |> maybe_filter_by_search(params["search"])
  end

  defp apply_sorting(query, %{"sort_field" => field, "sort_direction" => direction})
       when field in ~w(id chain_id function_name module_name process_id inserted_at) do
    direction = String.to_existing_atom(direction)
    field = String.to_existing_atom(field)

    case field do
      :id -> order_by(query, [{^direction, :id}])
      :chain_id -> order_by(query, [{^direction, :chain_id}])
      :function_name -> order_by(query, [{^direction, :function_name}])
      :module_name -> order_by(query, [{^direction, :module_name}])
      :process_id -> order_by(query, [{^direction, :process_id}])
      :inserted_at -> order_by(query, [{^direction, :inserted_at}])
    end
  end

  defp apply_sorting(query, _), do: order_by(query, [l], desc: l.inserted_at)

  defp apply_pagination(query, params) do
    page_number = get_page_number(params)
    page_size = get_page_size(params)
    offset = max(page_number - 1, 0) * page_size

    query
    |> limit(^page_size)
    |> offset(^offset)
  end

  defp maybe_filter_by_chain_id(query, nil), do: query

  defp maybe_filter_by_chain_id(query, chain_id) when is_binary(chain_id) and chain_id != "" do
    where(query, [l], l.chain_id == ^chain_id)
  end

  defp maybe_filter_by_chain_id(query, _), do: query

  defp maybe_filter_by_function_name(query, nil), do: query

  defp maybe_filter_by_function_name(query, name) when is_binary(name) and name != "" do
    where(query, [l], ilike(l.function_name, ^"%#{name}%"))
  end

  defp maybe_filter_by_function_name(query, _), do: query

  defp maybe_filter_by_module_name(query, nil), do: query

  defp maybe_filter_by_module_name(query, name) when is_binary(name) and name != "" do
    where(query, [l], ilike(l.module_name, ^"%#{name}%"))
  end

  defp maybe_filter_by_module_name(query, _), do: query

  defp maybe_filter_by_process_id(query, nil), do: query

  defp maybe_filter_by_process_id(query, process_id)
       when is_binary(process_id) and process_id != "" do
    where(query, [l], l.process_id == ^process_id)
  end

  defp maybe_filter_by_process_id(query, _), do: query

  defp maybe_filter_by_date_range(query, nil, nil), do: query

  defp maybe_filter_by_date_range(query, start_date, nil)
       when is_binary(start_date) and start_date != "" do
    {:ok, date} = Date.from_iso8601(start_date)
    where(query, [l], fragment("DATE(?)", l.inserted_at) >= ^date)
  end

  defp maybe_filter_by_date_range(query, nil, end_date)
       when is_binary(end_date) and end_date != "" do
    {:ok, date} = Date.from_iso8601(end_date)
    where(query, [l], fragment("DATE(?)", l.inserted_at) <= ^date)
  end

  defp maybe_filter_by_date_range(query, start_date, end_date)
       when is_binary(start_date) and start_date != "" and is_binary(end_date) and end_date != "" do
    {:ok, start_date} = Date.from_iso8601(start_date)
    {:ok, end_date} = Date.from_iso8601(end_date)

    where(
      query,
      [l],
      fragment("DATE(?)", l.inserted_at) >= ^start_date and
        fragment("DATE(?)", l.inserted_at) <= ^end_date
    )
  end

  defp maybe_filter_by_date_range(query, _, _), do: query

  defp maybe_filter_by_search(query, nil), do: query

  defp maybe_filter_by_search(query, search) when is_binary(search) and search != "" do
    search = "%#{search}%"

    where(
      query,
      [l],
      ilike(l.chain_id, ^search) or
        ilike(l.function_name, ^search) or
        ilike(l.module_name, ^search) or
        ilike(l.process_id, ^search)
    )
  end

  defp maybe_filter_by_search(query, _), do: query

  def parse_json_field(nil), do: nil

  def parse_json_field(json_string) when is_binary(json_string) do
    case Jason.decode(json_string) do
      {:ok, parsed} -> parsed
      _ -> json_string
    end
  end

  def parse_json_field(value), do: value
end
