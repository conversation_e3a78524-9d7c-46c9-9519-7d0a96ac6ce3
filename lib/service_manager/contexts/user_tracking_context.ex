defmodule ServiceManager.Contexts.UserTrackingContext do
  @moduledoc """
  Context module for handling user tracking operations.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Tracking.UserTracking

  @doc """
  Returns the list of user tracking records.

  ## Examples

      iex> list_user_tracking()
      [%UserTracking{}, ...]

  """
  def list_user_tracking do
    Repo.all(UserTracking)
  end

  @doc """
  Gets a single user tracking record.

  Raises `Ecto.NoResultsError` if the record does not exist.

  ## Examples

      iex> get_user_tracking!(123)
      %UserTracking{}

      iex> get_user_tracking!(456)
      ** (Ecto.NoResultsError)

  """
  def get_user_tracking!(id), do: Repo.get!(UserTracking, id)

  @doc """
  Creates a user tracking record.

  ## Examples

      iex> create_user_tracking(%{field: value})
      {:ok, %UserTracking{}}

      iex> create_user_tracking(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_user_tracking(attrs \\ %{}) do
    %UserTracking{}
    |> UserTracking.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates a user tracking entry record (when a user enters a page).

  ## Examples

      iex> create_entry_tracking(%{page_path: "/dashboard"})
      {:ok, %UserTracking{}}

  """
  def create_entry_tracking(attrs \\ %{}) do
    %UserTracking{}
    |> UserTracking.entry_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a user tracking record with exit information.

  ## Examples

      iex> update_with_exit_tracking(tracking, %{next_page_path: "/logout"})
      {:ok, %UserTracking{}}

  """
  def update_with_exit_tracking(%UserTracking{} = tracking, attrs) do
    tracking
    |> UserTracking.exit_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates a user tracking record.

  ## Examples

      iex> update_user_tracking(tracking, %{field: new_value})
      {:ok, %UserTracking{}}

      iex> update_user_tracking(tracking, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_user_tracking(%UserTracking{} = tracking, attrs) do
    tracking
    |> UserTracking.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a user tracking record.

  ## Examples

      iex> delete_user_tracking(tracking)
      {:ok, %UserTracking{}}

      iex> delete_user_tracking(tracking)
      {:error, %Ecto.Changeset{}}

  """
  def delete_user_tracking(%UserTracking{} = tracking) do
    Repo.delete(tracking)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user tracking changes.

  ## Examples

      iex> change_user_tracking(tracking)
      %Ecto.Changeset{data: %UserTracking{}}

  """
  def change_user_tracking(%UserTracking{} = tracking, attrs \\ %{}) do
    UserTracking.changeset(tracking, attrs)
  end

  @doc """
  Gets the most recent tracking record for a user.

  ## Examples

      iex> get_latest_user_tracking(user_id: 123)
      %UserTracking{}

      iex> get_latest_user_tracking(session_id: "abc123")
      %UserTracking{}

  """
  def get_latest_user_tracking(filters) do
    query = from t in UserTracking, order_by: [desc: t.entry_timestamp], limit: 1

    query =
      Enum.reduce(filters, query, fn
        {:user_id, user_id}, query ->
          from t in query, where: t.user_id == ^user_id

        {:wallet_user_id, wallet_user_id}, query ->
          from t in query, where: t.wallet_user_id == ^wallet_user_id

        {:session_id, session_id}, query ->
          from t in query, where: t.session_id == ^session_id

        {:device_id, device_id}, query ->
          from t in query, where: t.device_id == ^device_id

        _, query ->
          query
      end)

    Repo.one(query)
  end

  @doc """
  Gets user tracking records filtered by various criteria.

  ## Examples

      iex> filter_user_tracking(user_id: 123, page_path: "/dashboard")
      [%UserTracking{}, ...]

  """
  def filter_user_tracking(filters) do
    query = from t in UserTracking, order_by: [desc: t.entry_timestamp]

    query =
      Enum.reduce(filters, query, fn
        {:user_id, user_id}, query ->
          from t in query, where: t.user_id == ^user_id

        {:wallet_user_id, wallet_user_id}, query ->
          from t in query, where: t.wallet_user_id == ^wallet_user_id

        {:session_id, session_id}, query ->
          from t in query, where: t.session_id == ^session_id

        {:page_path, page_path}, query ->
          from t in query, where: t.page_path == ^page_path

        {:section, section}, query ->
          from t in query, where: t.section == ^section

        {:device_id, device_id}, query ->
          from t in query, where: t.device_id == ^device_id

        {:status, status}, query ->
          from t in query, where: t.status == ^status

        {:start_date, start_date}, query ->
          from t in query, where: t.entry_timestamp >= ^start_date

        {:end_date, end_date}, query ->
          from t in query, where: t.entry_timestamp <= ^end_date

        _, query ->
          query
      end)

    Repo.all(query)
  end
end
