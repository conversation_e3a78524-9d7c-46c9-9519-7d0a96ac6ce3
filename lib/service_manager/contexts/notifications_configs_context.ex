defmodule ServiceManager.Contexts.NotificationsConfigContext do
  @moduledoc """
  The NotificationsConfigContext handles interactions with notification system settings,
  including email, SMS and push notification configurations.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Settings.Notifications

  @doc """
  Gets the notification settings schema, either returning the existing settings
  or creating a new empty schema if none exists.

  ## Returns
    - %Notifications{} - Either existing settings or new empty schema
  """
  def get_setting_schema do
    case get_setting() do
      nil -> %ServiceManager.Schemas.Embedded.EmbeddedNotifications{}
      settings -> settings
    end
  end

  @doc """
  Gets the current active notification settings configuration.
  Returns the most recent notification settings record.

  ## Returns
    - %Notifications{} | nil
  """
  def get_setting do
    Notifications
    |> where([n], n.key == "notifications")
    |> order_by([n], desc: n.inserted_at)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Creates new notification settings.

  ## Parameters
    - attrs: Map of attributes for the new settings
      - :email_notifications_enabled - <PERSON><PERSON><PERSON> to enable/disable email notifications
      - :sms_notifications_enabled - <PERSON><PERSON><PERSON> to enable/disable SMS notifications 
      - :push_notifications_enabled - <PERSON>olean to enable/disable push notifications
      - Other email, SMS and push notification specific configs

  ## Returns
    - {:ok, %Notifications{}} on success
    - {:error, changeset} on validation failure
  """
  def create_setting(attrs \\ %{}) do
    %Notifications{}
    |> Notifications.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates existing notification settings.

  ## Parameters
    - notifications: Existing notification settings struct to update
    - attrs: Map of attributes to update with the same structure as create_setting/1

  ## Returns
    - {:ok, %Notifications{}} on success
    - {:error, changeset} on validation failure
  """
  def update_setting(%Notifications{} = notifications, attrs) do
    notifications
    |> Notifications.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Returns a changeset for tracking notification settings changes.

  ## Parameters
    - notifications: The notifications struct to create a changeset for
    - attrs: Optional map of changes to apply
  """
  def change_setting(%Notifications{} = notifications, attrs \\ %{}) do
    Notifications.changeset(notifications, attrs)
  end

  @doc """
  Inserts new notification settings with user tracking in a transaction.

  ## Parameters
    - params: Settings parameters map
    - user: User struct of the user performing the action

  ## Returns
    - {:ok, %{data_struct: %Notifications{}, notifications_cache: term()}} on success
    - {:error, failed_operation, failed_value, changes_so_far} on failure
  """
  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> create_setting()
    end)
    |> multi_cache()
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Updates existing notification settings with user tracking in a transaction.

  ## Parameters
    - notifications: Existing notification settings struct to update
    - params: Update parameters map
    - user: User struct of the user performing the update

  ## Returns
    - {:ok, %{data_struct: %Notifications{}, notifications_cache: term()}} on success
    - {:error, failed_operation, failed_value, changes_so_far} on failure
  """
  def update_data(%Notifications{} = notifications, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      notifications
      |> update_setting(Map.put(params, "updated_by", user.id))
    end)
    |> multi_cache()
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  # Private Functions

  defp multi_cache(multi) do
    multi
    |> Ecto.Multi.run(:notifications_cache, fn _, %{data_struct: data_struct} ->
      {:ok, put_cache(data_struct.config)}
    end)
  end

  defp put_cache(data) do
    Cachex.put(:settings, :notifications, data)
  end

  defp delete_cache do
    Cachex.del(:settings, :notifications)
  end
end
