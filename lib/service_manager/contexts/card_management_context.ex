defmodule ServiceManager.Contexts.CardManagementContext do
  @moduledoc """
  The Card Management context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounts.SchemaCard

  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    SchemaCard
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> preload([:user, :wallet_user, :beneficiary])
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.card_number, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.card_type, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.cvv, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.currency, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  @doc """
  Returns the list of active cards.
  """
  def list_active_cards do
    SchemaCard
    |> where([c], c.status == "active")
    |> order_by([c], c.card_number)
    |> Repo.all()
  end

  @doc """
  Returns a list of cards codes for dropdowns.
  """
  def get_cards_options do
    list_active_cards()
    |> Enum.map(fn cards -> {cards.card_number, cards.card_number} end)
  end

  @doc """
  Gets a single cards by code.
  """
  def get_cards_by_card_number(card_number) do
    SchemaCard
    |> where([c], c.card_number == ^card_number)
    |> Repo.one()
  end

  @doc """
  Creates a cards.
  """
  def create_cards(attrs \\ %{}) do
    %SchemaCard{}
    |> SchemaCard.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a cards.
  """
  def update_cards(%SchemaCard{} = cards, attrs) do
    cards
    |> SchemaCard.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a cards.
  """
  def delete_cards(%SchemaCard{} = cards) do
    Repo.delete(cards)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking cards changes.
  """
  def change_cards(%SchemaCard{} = cards, attrs \\ %{}) do
    SchemaCard.changeset(cards, attrs)
  end
end
