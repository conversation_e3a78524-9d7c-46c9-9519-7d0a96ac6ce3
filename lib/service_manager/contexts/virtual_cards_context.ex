defmodule ServiceManager.Contexts.VirtualCardsAContexts do
  @moduledoc """
  The Configuration context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo

  alias ServiceManager.Schemas.VirtualCardsApiConfig, as: ApiConfig

  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    ApiConfig
    |> DefaultQueries.status_query()
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.version, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.api_key, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.provider_name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.notes, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> create_api_config()
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: user.id,
    #     activity: "User created",
    #     description: "Inserted a new user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def update_data(%ApiConfig{} = user_record, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> update_api_config(Map.put(params, "updated_by", user.id))
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: user.id,
    #     activity: "User updated",
    #     description: "#{user.email} }updated user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Returns the list of api_configs.

  ## Examples

      iex> list_api_configs()
      [%ApiConfig{}, ...]

  """
  def list_api_configs do
    Repo.all(ApiConfig)
  end

  @doc """
  Gets a single api_config.

  Raises `Ecto.NoResultsError` if the Api config does not exist.

  ## Examples

      iex> get_api_config!(123)
      %ApiConfig{}

      iex> get_api_config!(456)
      ** (Ecto.NoResultsError)

  """
  def get_api_config!(id), do: Repo.get!(ApiConfig, id)

  @doc """
  Creates a api_config.

  ## Examples

      iex> create_api_config(%{field: value})
      {:ok, %ApiConfig{}}

      iex> create_api_config(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_api_config(attrs \\ %{}) do
    %ApiConfig{}
    |> ApiConfig.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a api_config.

  ## Examples

      iex> update_api_config(api_config, %{field: new_value})
      {:ok, %ApiConfig{}}

      iex> update_api_config(api_config, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_api_config(%ApiConfig{} = api_config, attrs) do
    api_config
    |> ApiConfig.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a api_config.

  ## Examples

      iex> delete_api_config(api_config)
      {:ok, %ApiConfig{}}

      iex> delete_api_config(api_config)
      {:error, %Ecto.Changeset{}}

  """
  def delete_api_config(%ApiConfig{} = api_config) do
    Repo.delete(api_config)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking api_config changes.

  ## Examples

      iex> change_api_config(api_config)
      %Ecto.Changeset{data: %ApiConfig{}}

  """
  def change_api_config(%ApiConfig{} = api_config, attrs \\ %{}) do
    ApiConfig.changeset(api_config, attrs)
  end
end
