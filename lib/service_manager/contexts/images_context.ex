defmodule ServiceManager.Contexts.ImagesContext do
  @moduledoc """
  The ImagesContext handles interactions with image and file storage settings,
  including logo, favicon, storage providers and image processing configurations.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Settings.Images

  @doc """
  Gets the image settings schema, either returning the existing settings
  or creating a new empty schema if none exists.

  ## Returns
    - %Images{} - Either existing settings or new empty schema
  """
  def get_setting_schema do
    case get_setting() do
      nil -> %Images{}
      settings -> settings
    end
  end

  @doc """
  Gets the current active image settings configuration.
  Returns the most recent image settings record.

  ## Returns
    - %Images{} | nil
  """
  def get_setting do
    Images
    |> where([i], i.key == "images")
    |> order_by([i], desc: i.inserted_at)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Creates new image settings.

  ## Parameters
    - attrs: Map of attributes for the new settings
      - :logo_url - URL for company logo
      - :favicon_url - URL for site favicon
      - :max_file_size - Maximum allowed file size in bytes
      - :allowed_formats - List of allowed image formats
      - Other storage provider and image processing configs

  ## Returns
    - {:ok, %Images{}} on success
    - {:error, changeset} on validation failure
  """
  def create_setting(attrs \\ %{}) do
    %Images{}
    |> Images.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates existing image settings.

  ## Parameters
    - images: Existing image settings struct to update
    - attrs: Map of attributes to update with the same structure as create_setting/1

  ## Returns
    - {:ok, %Images{}} on success
    - {:error, changeset} on validation failure
  """
  def update_setting(%Images{} = images, attrs) do
    images
    |> Images.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Returns a changeset for tracking image settings changes.

  ## Parameters
    - images: The images struct to create a changeset for
    - attrs: Optional map of changes to apply
  """
  def change_setting(%Images{} = images, attrs \\ %{}) do
    Images.changeset(images, attrs)
  end

  @doc """
  Inserts new image settings with user tracking in a transaction.

  ## Parameters
    - params: Settings parameters map
    - user: User struct of the user performing the action

  ## Returns
    - {:ok, %{data_struct: %Images{}, images_cache: term()}} on success
    - {:error, failed_operation, failed_value, changes_so_far} on failure
  """
  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> create_setting()
    end)
    |> multi_cache()
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Updates existing image settings with user tracking in a transaction.

  ## Parameters
    - images: Existing image settings struct to update
    - params: Update parameters map
    - user: User struct of the user performing the update

  ## Returns
    - {:ok, %{data_struct: %Images{}, images_cache: term()}} on success
    - {:error, failed_operation, failed_value, changes_so_far} on failure
  """
  def update_data(%Images{} = images, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      images
      |> update_setting(Map.put(params, "updated_by", user.id))
    end)
    |> multi_cache()
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  # Private Functions

  defp multi_cache(multi) do
    multi
    |> Ecto.Multi.run(:images_cache, fn _, %{data_struct: data_struct} ->
      if String.downcase(data_struct.status) == "active" do
        {:ok, put_cache(data_struct.config)}
      else
        {:ok, delete_cache()}
      end
    end)
  end

  defp put_cache(data) do
    Cachex.put(:settings, :images, data)
  end

  defp delete_cache do
    Cachex.del(:settings, :images)
  end
end
