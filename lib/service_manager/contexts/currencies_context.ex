defmodule ServiceManager.Contexts.CurrenciesContext do
  @moduledoc """
  The Currencies context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Currency

  @doc """
  Returns the list of active currencies.
  """
  def list_active_currencies do
    Currency
    |> where([c], c.is_active == true)
    |> order_by([c], c.code)
    |> Repo.all()
  end

  @doc """
  Returns a list of currency codes for dropdowns.
  """
  def get_currency_options do
    list_active_currencies()
    |> Enum.map(fn currency -> {currency.code, currency.code} end)
  end

  @doc """
  Gets a single currency by code.
  """
  def get_currency_by_code(code) do
    Currency
    |> where([c], c.code == ^code)
    |> Repo.one()
  end

  @doc """
  Creates a currency.
  """
  def create_currency(attrs \\ %{}) do
    %Currency{}
    |> Currency.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a currency.
  """
  def update_currency(%Currency{} = currency, attrs) do
    currency
    |> Currency.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a currency.
  """
  def delete_currency(%Currency{} = currency) do
    Repo.delete(currency)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking currency changes.
  """
  def change_currency(%Currency{} = currency, attrs \\ %{}) do
    Currency.changeset(currency, attrs)
  end
end
