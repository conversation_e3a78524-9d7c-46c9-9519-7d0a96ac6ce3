defmodule ServiceManager.Contexts.PaymentMethodsContext do
  @moduledoc """
  The PaymentMethodsContext context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo

  alias ServiceManager.Schemas.PaymentMethod

  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    PaymentMethod
    |> DefaultQueries.status_query()
    # |> preload([:from_account, :to_account])
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.type, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.description, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> create_payment_method()
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: user.id,
    #     activity: "User created",
    #     description: "Inserted a new user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def update_data(%PaymentMethod{} = user_record, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> update_payment_method(Map.put(params, "updated_by", user.id))
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: user.id,
    #     activity: "User updated",
    #     description: "#{user.email} }updated user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Returns the list of payment_methods.

  ## Examples

      iex> list_payment_methods()
      [%PaymentMethod{}, ...]

  """
  def list_payment_methods do
    Repo.all(PaymentMethod)
  end

  @doc """
  Gets a single payment_method.

  Raises `Ecto.NoResultsError` if the Payment method does not exist.

  ## Examples

      iex> get_payment_method!(123)
      %PaymentMethod{}

      iex> get_payment_method!(456)
      ** (Ecto.NoResultsError)

  """
  def get_payment_method!(id), do: Repo.get!(PaymentMethod, id)

  @doc """
  Creates a payment_method.

  ## Examples

      iex> create_payment_method(%{field: value})
      {:ok, %PaymentMethod{}}

      iex> create_payment_method(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_payment_method(attrs \\ %{}) do
    %PaymentMethod{}
    |> PaymentMethod.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a payment_method.

  ## Examples

      iex> update_payment_method(payment_method, %{field: new_value})
      {:ok, %PaymentMethod{}}

      iex> update_payment_method(payment_method, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_payment_method(%PaymentMethod{} = payment_method, attrs) do
    payment_method
    |> PaymentMethod.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a payment_method.

  ## Examples

      iex> delete_payment_method(payment_method)
      {:ok, %PaymentMethod{}}

      iex> delete_payment_method(payment_method)
      {:error, %Ecto.Changeset{}}

  """
  def delete_payment_method(%PaymentMethod{} = payment_method) do
    Repo.delete(payment_method)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking payment_method changes.

  ## Examples

      iex> change_payment_method(payment_method)
      %Ecto.Changeset{data: %PaymentMethod{}}

  """
  def change_payment_method(%PaymentMethod{} = payment_method, attrs \\ %{}) do
    PaymentMethod.changeset(payment_method, attrs)
  end
end
