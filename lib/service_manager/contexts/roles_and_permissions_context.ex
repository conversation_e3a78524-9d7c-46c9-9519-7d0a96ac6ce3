defmodule ServiceManager.Contexts.RolesAndPermissionsContext do
  @moduledoc """
  The RolesAndPermissionsContext context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo

  alias ServiceManager.Schemas.RolesAndPermission

  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    RolesAndPermission
    #   |> DefaultQueries.status_query()
    # |> preload([:from_account, :to_account])
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  @doc """
  Returns the list of roles_and_permissions.

  ## Examples

      iex> list_roles_and_permissions()
      [%RolesAndPermission{}, ...]

  """
  def list_roles_and_permissions do
    Repo.all(RolesAndPermission)
  end

  def get_roles() do
    RolesAndPermission
    |> where([a], a.status in ~w(active ACTIVE)s)
    |> select([a], {a.name, a.id})
    |> Repo.all()
  end

  @doc """
  Gets a single roles_and_permission.

  Raises `Ecto.NoResultsError` if the Roles and permission does not exist.

  ## Examples

      iex> get_roles_and_permission!(123)
      %RolesAndPermission{}

      iex> get_roles_and_permission!(456)
      ** (Ecto.NoResultsError)

  """
  def get_roles_and_permission!(id), do: Repo.get!(RolesAndPermission, id)

  @doc """
  Creates a roles_and_permission.

  ## Examples

      iex> create_roles_and_permission(%{field: value})
      {:ok, %RolesAndPermission{}}

      iex> create_roles_and_permission(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_roles_and_permission(attrs \\ %{}) do
    %RolesAndPermission{}
    |> RolesAndPermission.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a roles_and_permission.

  ## Examples

      iex> update_roles_and_permission(roles_and_permission, %{field: new_value})
      {:ok, %RolesAndPermission{}}

      iex> update_roles_and_permission(roles_and_permission, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_roles_and_permission(%RolesAndPermission{} = roles_and_permission, attrs) do
    roles_and_permission
    |> RolesAndPermission.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a roles_and_permission.

  ## Examples

      iex> delete_roles_and_permission(roles_and_permission)
      {:ok, %RolesAndPermission{}}

      iex> delete_roles_and_permission(roles_and_permission)
      {:error, %Ecto.Changeset{}}

  """
  def delete_roles_and_permission(%RolesAndPermission{} = roles_and_permission) do
    Repo.delete(roles_and_permission)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking roles_and_permission changes.

  ## Examples

      iex> change_roles_and_permission(roles_and_permission)
      %Ecto.Changeset{data: %RolesAndPermission{}}

  """
  def change_roles_and_permission(%RolesAndPermission{} = roles_and_permission, attrs \\ %{}) do
    RolesAndPermission.changeset(roles_and_permission, attrs)
  end
end
