defmodule ServiceManager.Contexts.FundRequestsContext do
  import Ecto.Query
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Withdraws.FundRequests.{FundRequestsSchema, FundRequestsConfig}
  alias ServiceManager.Notifications.SMSNotification
  alias ServiceManager.Accounts
  alias ServiceManagerWeb.Api.Services.Local.CrossTransfersService

  def create_fund_request(attrs) do
    with {:ok, _receiver} <- validate_receiver_account(attrs.receiver_account) do
      %FundRequestsSchema{}
      |> FundRequestsSchema.create_changeset(attrs)
      |> Repo.insert()
      |> case do
        {:ok, request} ->
          # Send SMS notification to receiver
          send_request_notification(request)
          {:ok, request}

        error ->
          error
      end
    end
  end

  defp validate_receiver_account(account_number) do
    config = get_config()

    if config.validate_receiver_account do
      case Accounts.user_by_account_number(account_number) do
        nil -> {:error, "Receiver account not found"}
        user -> {:ok, user}
      end
    else
      {:ok, nil}
    end
  end

  defp get_config do
    Repo.one(FundRequestsConfig) || %FundRequestsConfig{validate_receiver_account: true}
  end

  def get_fund_request!(id) do
    FundRequestsSchema
    |> Repo.get!(id)
    |> Repo.preload([:sender, :receiver])
  end

  def get_data!(id), do: get_fund_request!(id)

  def retrieve(params \\ %{}) do
    FundRequestsSchema
    |> handle_filters(params)
    |> order_by([r], desc: r.inserted_at)
    |> preload([:sender, :receiver])
    |> Repo.paginate(params)
  end

  defp handle_filters(query, params) do
    params
    |> Enum.reduce(query, fn
      {"status", status}, query when status != "" ->
        where(query, [r], r.status == ^status)

      {"reference", reference}, query when reference != "" ->
        where(query, [r], ilike(r.transaction_reference, ^"%#{reference}%"))

      {"sender_account", account}, query when account != "" ->
        where(query, [r], ilike(r.sender_account, ^"%#{account}%"))

      {"receiver_account", account}, query when account != "" ->
        where(query, [r], ilike(r.receiver_account, ^"%#{account}%"))

      {"min_amount", amount}, query when amount != "" ->
        case Decimal.parse(amount) do
          {:ok, decimal} -> where(query, [r], r.amount >= ^decimal)
          _ -> query
        end

      {"max_amount", amount}, query when amount != "" ->
        case Decimal.parse(amount) do
          {:ok, decimal} -> where(query, [r], r.amount <= ^decimal)
          _ -> query
        end

      {"date_from", date}, query when date != "" ->
        where(query, [r], fragment("DATE(?)", r.inserted_at) >= ^date)

      {"date_to", date}, query when date != "" ->
        where(query, [r], fragment("DATE(?)", r.inserted_at) <= ^date)

      _, query ->
        query
    end)
  end

  def get_fund_request_by_reference(reference) do
    Repo.get_by(FundRequestsSchema, transaction_reference: reference)
  end

  def list_user_requests(user_id) do
    FundRequestsSchema
    |> where([r], r.sender_id == ^user_id or r.receiver_id == ^user_id)
    |> order_by([r], desc: r.inserted_at)
    |> Repo.all()
  end

  def list_pending_requests(account) do
    FundRequestsSchema
    |> where(
      [r],
      (r.receiver_account == ^account or r.sender_account == ^account) and r.status == "pending"
    )
    |> order_by([r], desc: r.inserted_at)
    |> Repo.all()
  end

  def approve_request(%FundRequestsSchema{} = request, user_id) do
    with {:ok, receiver} <- get_user_by_account(request.receiver_account) do
      if receiver.id == user_id do
        # First try the transfer
        case process_fund_transfer(request) do
          {:ok, {:ok, _result}} ->
            # If transfer succeeds, update status and send notifications
            request
            |> FundRequestsSchema.approve_changeset(%{receiver_id: user_id})
            |> Repo.update()
            |> case do
              {:ok, updated_request} ->
                send_approval_notification(updated_request)
                {:ok, updated_request}

              error ->
                error
            end

          {:error, reason} ->
            # If transfer fails, don't update status
            {:error, reason}
        end
      else
        {:error, "Only the receiver can approve fund requests"}
      end
    end
  end

  def reject_request(%FundRequestsSchema{} = request, user_id) do
    with {:ok, receiver} <- get_user_by_account(request.receiver_account) do
      if receiver.id == user_id do
        request
        |> FundRequestsSchema.reject_changeset()
        |> Repo.update()
        |> case do
          {:ok, updated_request} ->
            send_rejection_notification(updated_request)
            {:ok, updated_request}

          error ->
            error
        end
      else
        {:error, "Only the receiver can reject fund requests"}
      end
    end
  end

  defp send_request_notification(request) do
    with {:ok, receiver} <- get_user_by_account(request.receiver_account),
         {:ok, sender} <- get_user_by_account(request.sender_account) do
      # Notify receiver
      receiver_message = """
      You have received a fund request of #{request.amount} from #{sender.first_name} #{sender.last_name} (Account: #{request.sender_account}).
      Reference: #{request.transaction_reference}
      """

      Repo.insert(%SMSNotification{
        msisdn: receiver.phone_number,
        message: receiver_message
      })

      # Notify sender
      sender_message = """
      You have sent a fund request of #{request.amount} to #{receiver.first_name} #{receiver.last_name} (Account: #{request.receiver_account}).
      Reference: #{request.transaction_reference}
      """

      Repo.insert(%SMSNotification{
        msisdn: sender.phone_number,
        message: sender_message
      })
    end
  end

  defp send_approval_notification(request) do
    with {:ok, sender} <- get_user_by_account(request.sender_account),
         {:ok, receiver} <- get_user_by_account(request.receiver_account) do
      # Notify sender
      sender_message =
        "Your fund request #{request.transaction_reference} has been approved and processed by #{receiver.first_name} #{receiver.last_name}."

      Repo.insert(%SMSNotification{
        msisdn: sender.phone_number,
        message: sender_message
      })

      # Notify receiver
      receiver_message =
        "You have approved and processed fund request #{request.transaction_reference} for #{sender.first_name} #{sender.last_name}."

      Repo.insert(%SMSNotification{
        msisdn: receiver.phone_number,
        message: receiver_message
      })
    end
  end

  defp send_rejection_notification(request) do
    with {:ok, sender} <- get_user_by_account(request.sender_account),
         {:ok, receiver} <- get_user_by_account(request.receiver_account) do
      # Notify sender
      sender_message =
        "Your fund request #{request.transaction_reference} has been rejected by #{receiver.first_name} #{receiver.last_name}."

      Repo.insert(%SMSNotification{
        msisdn: sender.phone_number,
        message: sender_message
      })

      # Notify receiver
      receiver_message =
        "You have rejected fund request #{request.transaction_reference} from #{sender.first_name} #{sender.last_name}."

      Repo.insert(%SMSNotification{
        msisdn: receiver.phone_number,
        message: receiver_message
      })
    end
  end

  defp process_fund_transfer(request) do
    # Process the fund transfer using CrossTransfersService
    params = %{
      "amount" => Decimal.to_string(request.amount),
      "from_account" => request.sender_account,
      "to_account" => request.receiver_account,
      "reference" => request.transaction_reference,
      "description" => "Fund request transfer: #{request.transaction_reference}"
    }

    result = CrossTransfersService.bank_to_bank(params)

    result
  end

  defp get_user_by_account(account_number) do
    case Accounts.user_by_account_number(account_number) do
      nil -> {:error, :user_not_found}
      user -> {:ok, user}
    end
  end
end
