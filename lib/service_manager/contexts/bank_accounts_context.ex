defmodule ServiceManager.Contexts.BankAccountsContext do
  @moduledoc """
  The BankAccountsContext handles all bank account related database operations and business logic.
  This includes creating, reading, updating and deleting bank account records.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounts.BankAccount
  alias ServiceManager.Queries.{DefaultQueries, FilterQueries}

  @query_params Application.compile_env(:service_manager, :query_params)

  @doc """
  Retrieves a paginated list of bank accounts with sorting and filtering applied.

  ## Parameters
    - params: Map of query parameters for filtering, sorting and pagination (optional)

  Returns `%Scrivener.Page{}` of bank accounts
  """
  def retrieve(params \\ @query_params) do
    BankAccount
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  @doc """
  No-op search filter when search parameter is empty or nil
  """
  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  @doc """
  Filters bank accounts by searching across multiple fields using case-insensitive LIKE queries.
  Searches name, number, type and tag fields.
  """
  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.number, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.type, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.tag, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  @doc """
  Returns the list of bank accounts.

  ## Examples

      iex> list_bank_accounts()
      [%BankAccount{}, ...]

  """
  def list_bank_accounts do
    Repo.all(BankAccount)
  end

  @doc """
  Gets a single bank account.

  Raises `Ecto.NoResultsError` if the Bank account does not exist.

  ## Examples

      iex> get_bank_account!(123)
      %BankAccount{}

      iex> get_bank_account!(456)
      ** (Ecto.NoResultsError)

  """
  def get_bank_account!(id), do: Repo.get!(BankAccount, id)

  @doc """
  Creates a bank account.

  ## Examples

      iex> create_bank_account(%{field: value})
      {:ok, %BankAccount{}}

      iex> create_bank_account(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_bank_account(attrs \\ %{}) do
    %BankAccount{}
    |> BankAccount.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a bank account.

  ## Examples

      iex> update_bank_account(bank_account, %{field: new_value})
      {:ok, %BankAccount{}}

      iex> update_bank_account(bank_account, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_bank_account(%BankAccount{} = bank_account, attrs) do
    bank_account
    |> BankAccount.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a bank account.

  ## Examples

      iex> delete_bank_account(bank_account)
      {:ok, %BankAccount{}}

      iex> delete_bank_account(bank_account)
      {:error, %Ecto.Changeset{}}

  """
  def delete_bank_account(%BankAccount{} = bank_account) do
    Repo.delete(bank_account)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking bank account changes.

  ## Examples

      iex> change_bank_account(bank_account)
      %Ecto.Changeset{data: %BankAccount{}}

  """
  def change_bank_account(%BankAccount{} = bank_account, attrs \\ %{}) do
    BankAccount.changeset(bank_account, attrs)
  end
end
