defmodule ServiceManager.Contexts.DeletedArchivesContext do
  @moduledoc """
  The Archives context for handling soft-deleted records.
  """
  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.DeletedArchive

  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries

  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    DeletedArchive
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.item, ^"%#{search}%")
    )
  end

  @doc """
  Returns the list of deleted_archives.

  ## Examples

      iex> list_deleted_archives()
      [%DeletedArchive{}, ...]

  """
  def list_deleted_archives do
    Repo.all(DeletedArchive)
  end

  @doc """
  Gets a single deleted_archive.

  Raises `Ecto.NoResultsError` if the Deleted archive does not exist.

  ## Examples

      iex> get_deleted_archive!(123)
      %DeletedArchive{}

      iex> get_deleted_archive!(456)
      ** (Ecto.NoResultsError)

  """
  def get_deleted_archive!(id), do: Repo.get!(DeletedArchive, id)

  def get_deleted_archive_by_item_id(id) do
    DeletedArchive
    |> where([a], a.item_id == type(^id, :integer))
    |> Repo.all()
    |> List.first()
  end

  @doc """
  Creates a deleted_archive.

  ## Examples

      iex> create_deleted_archive(%{field: value})
      {:ok, %DeletedArchive{}}

      iex> create_deleted_archive(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_deleted_archive(item, struct, user, description \\ "") do
    %{
      item: item,
      item_id: struct.id,
      description: description,
      created_by: user.id,
      data: ServiceManager.Utilities.StructConverter.to_total_map(struct)
    }
    |> create_deleted_archive()
  end

  def create_deleted_archive(attrs \\ %{}) do
    %DeletedArchive{}
    |> DeletedArchive.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Archives an item by moving it to the deleted archive.

  ## Examples

      iex> archive_item("User", 123, %{description: "Inactive account"}, current_user)
      {:ok, %DeletedArchive{}}

      iex> archive_item("Post", 456, %{}, current_user)
      {:ok, %DeletedArchive{}}

  """
  def archive_item(item_type, item_id, metadata \\ %{}, user) do
    attrs = %{
      item: item_type,
      item_id: item_id,
      description: metadata[:description],
      data: metadata[:data] || %{},
      created_by: user.id,
      updated_by: user.id
    }

    create_deleted_archive(attrs)
  end

  @doc """
  Updates a deleted_archive.

  ## Examples

      iex> update_deleted_archive(deleted_archive, %{field: new_value})
      {:ok, %DeletedArchive{}}

      iex> update_deleted_archive(deleted_archive, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_deleted_archive(%DeletedArchive{} = deleted_archive, attrs, user) do
    attrs = Map.put(attrs, :updated_by, user.id)

    deleted_archive
    |> DeletedArchive.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a deleted_archive (permanent deletion).

  ## Examples

      iex> delete_deleted_archive(deleted_archive)
      {:ok, %DeletedArchive{}}

      iex> delete_deleted_archive(deleted_archive)
      {:error, %Ecto.Changeset{}}

  """
  def delete_deleted_archive(%DeletedArchive{} = deleted_archive) do
    Repo.delete(deleted_archive)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking deleted_archive changes.

  ## Examples

      iex> change_deleted_archive(deleted_archive)
      %Ecto.Changeset{data: %DeletedArchive{}}

  """
  def change_deleted_archive(%DeletedArchive{} = deleted_archive, attrs \\ %{}) do
    DeletedArchive.changeset(deleted_archive, attrs)
  end

  @doc """
  Searches the deleted archive by item type and/or ID.

  ## Examples

      iex> search_deleted_archives(item_type: "User")
      [%DeletedArchive{}, ...]

      iex> search_deleted_archives(item_id: 123)
      [%DeletedArchive{}]

      iex> search_deleted_archives(item_type: "Post", item_id: 456)
      [%DeletedArchive{}]
  """
  def search_deleted_archives(opts) do
    query = from(d in DeletedArchive)

    Enum.reduce(opts, query, fn
      {:item_type, item_type}, query ->
        where(query, [d], d.item == ^item_type)

      {:item_id, item_id}, query ->
        where(query, [d], d.item_id == ^item_id)

      {_, _}, query ->
        query
    end)
    |> Repo.all()
  end
end
