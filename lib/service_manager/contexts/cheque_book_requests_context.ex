defmodule ServiceManager.Contexts.ChequeBookRequestsContext do
  import Ecto.Query
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounts.Cheques.ChequeBookRequest

  def get_data!(id), do: Repo.get!(ChequeBookRequest, id) |> Repo.preload(:user)

  def list_data(params \\ %{}) do
    query = from(c in ChequeBookRequest, preload: [:user])

    query
    |> filter_by_status(params["status"])
    |> filter_by_date_range(params["from_date"], params["end_date"])
    |> filter_by_search(params["search"])
    |> order_by([c], desc: c.inserted_at)
    |> Repo.all()
  end

  def update_data(cheque_request, attrs, current_user) do
    cheque_request
    |> ChequeBookRequest.changeset(attrs)
    |> Repo.update()
  end

  def change_data(%ChequeBookRequest{} = cheque_request, attrs \\ %{}) do
    ChequeBookRequest.changeset(cheque_request, attrs)
  end

  def delete_data(%ChequeBookRequest{} = cheque_request) do
    Repo.delete(cheque_request)
  end

  def insert_data(attrs, current_user) do
    %ChequeBookRequest{}
    |> ChequeBookRequest.changeset(attrs)
    |> Repo.insert()
  end

  defp filter_by_status(query, status) when is_nil(status) or status == "", do: query

  defp filter_by_status(query, status) do
    from(c in query, where: c.request_status == ^status)
  end

  defp filter_by_date_range(query, nil, nil), do: query
  defp filter_by_date_range(query, "", ""), do: query

  defp filter_by_date_range(query, from_date, end_date) do
    case {Date.from_iso8601(from_date || ""), Date.from_iso8601(end_date || "")} do
      {{:ok, from}, {:ok, to}} ->
        from_datetime = DateTime.new!(from, ~T[00:00:00], "Etc/UTC")
        to_datetime = DateTime.new!(to, ~T[23:59:59], "Etc/UTC")
        where(query, [c], c.inserted_at >= ^from_datetime and c.inserted_at <= ^to_datetime)

      _ ->
        query
    end
  end

  defp filter_by_search(query, nil), do: query
  defp filter_by_search(query, ""), do: query

  defp filter_by_search(query, search) do
    search = "%#{search}%"

    query
    |> join(:left, [c], u in assoc(c, :user))
    |> where(
      [c, u],
      ilike(c.request_reference, ^search) or
        ilike(c.account_number, ^search) or
        ilike(fragment("CONCAT(?, ' ', ?)", u.first_name, u.last_name), ^search)
    )
  end
end
