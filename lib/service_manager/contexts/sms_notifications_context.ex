defmodule ServiceManager.Contexts.SmsNotificationsContext do
  @moduledoc """
  The UserManagementContext context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  alias ServiceManager.Repo
  alias ServiceManager.Notifications.SMSNotification, as: MainSchema

  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    MainSchema
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.msisdn, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  @doc """
  Returns the list of users.

  ## Examples

      iex> list_users()
      [%MainSchema}, ...]

  """
  def list_users do
    Repo.all(MainSchema)
  end

  @doc """
  Gets a single MainSchema

  Raises `Ecto.NoResultsError` if the User does not exist.

  ## Examples

      iex> get_user!(123)
      %MainSchema}

      iex> get_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_data!(id), do: Repo.get!(MainSchema, id)

  @doc """
  Creates a MainSchema

  ## Examples

      iex> create_method(%{field: value})
      {:ok, %MainSchema}}

      iex> create_method(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_method(attrs \\ %{}) do
    %MainSchema{}
    |> MainSchema.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a MainSchema

  ## Examples

      iex> update_method(user, %{field: new_value})
      {:ok, %MainSchema}}

      iex> update_method(user, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_method(%MainSchema{} = user, attrs) do
    user
    |> MainSchema.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a MainSchema

  ## Examples

      iex> delete_method(user)
      {:ok, %MainSchema}}

      iex> delete_method(user)
      {:error, %Ecto.Changeset{}}

  """
  def delete_data(%MainSchema{} = user) do
    Repo.delete(user)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user changes.

  ## Examples

      iex> change_method(user)
      %Ecto.Changeset{data: %MainSchema}}

  """
  def change_data(%MainSchema{} = user, attrs \\ %{}) do
    MainSchema.changeset(user, attrs)
  end
end
