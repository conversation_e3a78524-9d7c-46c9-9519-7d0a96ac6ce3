defmodule ServiceManager.Contexts.BeneficieriesContext do
  @moduledoc """
  The UserManagementContext context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounts.SchemaBeneficiary, as: MainSchema

  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    MainSchema
    #   |> DefaultQueries.status_query()
    |> preload([:user])
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.account_number, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.bank_code, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.currency, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.beneficiary_type, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.description, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  @doc """
  Returns the list of users.

  ## Examples

      iex> list_users()
      [%MainSchema}, ...]

  """
  def list_users do
    Repo.all(MainSchema)
  end

  @doc """
  Gets a single MainSchema

  Raises `Ecto.NoResultsError` if the User does not exist.

  ## Examples

      iex> get_user!(123)
      %MainSchema}

      iex> get_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_data!(id), do: Repo.get!(MainSchema, id)

  @doc """
  Creates a MainSchema

  ## Examples

      iex> create_method(%{field: value})
      {:ok, %MainSchema}}

      iex> create_method(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_method(attrs \\ %{}) do
    %MainSchema{}
    |> MainSchema.changeset(attrs)
    |> Repo.insert()
  end

  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> Map.put("password", ServiceManager.Utilities.password())
      |> create_method()
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: MainSchemaid,
    #     activity: "User created",
    #     description: "Inserted a new user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Updates a MainSchema

  ## Examples

      iex> update_method(user, %{field: new_value})
      {:ok, %MainSchema}}

      iex> update_method(user, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_method(%MainSchema{} = user, attrs) do
    user
    |> MainSchema.changeset(attrs)
    |> Repo.update()
  end

  def activate_method(%MainSchema{} = user, attrs) do
    user
    |> MainSchema.activate_update_changeset(attrs)
    |> Repo.update()
  end

  def update_data(%MainSchema{} = user_record, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> update_method(Map.put(params, "updated_by", user.id))
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: MainSchemaid,
    #     activity: "User updated",
    #     description: "#{MainSchemaemail} }updated user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def user_activation(user_record, params, user) do
    password = ServiceManager.Utilities.password()

    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      #
      user_record
      |> activate_method(
        Map.put(params, "updated_by", user.id)
        |> Map.put("password", password)
      )
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: data_struct} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: MainSchemaid,
    #     activity: "User activated",
    #     description: "#{MainSchemaemail} activated user - ID: #{data_struct.id}"
    #   })
    # end)
    # |> Ecto.Multi.run(:send_sms, fn _repo, %{data_struct: data_struct} ->
    #   App.ContextSmsLogs.create_sms_message(%{
    #     msisdn: data_struct.msisdn,
    #     body:
    #       "Hello #{data_struct.first_name}, \n Please utilize the provided password to log in to the Smartlink portal. #{password}"
    #   })
    # end)
    # |> Ecto.Multi.run(:send_email, fn _repo, %{data_struct: data_struct} ->
    #   App.ContextEmails.create_email(%{
    #     subject: "Password reset",
    #     name: "#{data_struct.first_name} #{data_struct.last_name}",
    #     to: data_struct.email,
    #     body:
    #       "Hello #{data_struct.first_name}, \n Please utilize the provided password to log in to the Smartlink portal. #{password}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Deletes a MainSchema

  ## Examples

      iex> delete_method(user)
      {:ok, %MainSchema}}

      iex> delete_method(user)
      {:error, %Ecto.Changeset{}}

  """
  def delete_data(%MainSchema{} = user) do
    Repo.delete(user)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user changes.

  ## Examples

      iex> change_method(user)
      %Ecto.Changeset{data: %MainSchema}}

  """
  def change_data(%MainSchema{} = user, attrs \\ %{}) do
    MainSchema.changeset(user, attrs)
  end

  def update_all_to_mobile_banking do
    from(b in MainSchema, update: [set: [beneficiary_type: "mobile_banking"]])
    |> Repo.update_all([])
  end
end
