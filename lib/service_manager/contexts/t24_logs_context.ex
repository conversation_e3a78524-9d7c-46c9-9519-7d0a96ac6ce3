defmodule ServiceManager.Contexts.T24LogsContext do
  @moduledoc """
  The T24LogsContext context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo

  alias ServiceManager.Schemas.T24Log

  @doc """
  Returns the list of t_24_logs.

  ## Examples

      iex> list_t_24_logs()
      [%T24Log{}, ...]

  """
  def list_t_24_logs do
    Repo.all(T24Log)
  end

  @doc """
  Gets a single t24_log.

  Raises `Ecto.NoResultsError` if the T24 log does not exist.

  ## Examples

      iex> get_t24_log!(123)
      %T24Log{}

      iex> get_t24_log!(456)
      ** (Ecto.NoResultsError)

  """
  def get_t24_log!(id), do: Repo.get!(T24Log, id)

  @doc """
  Creates a t24_log.

  ## Examples

      iex> create_t24_log(%{field: value})
      {:ok, %T24Log{}}

      iex> create_t24_log(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_t24_log(attrs \\ %{}) do
    %T24Log{}
    |> T24Log.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a t24_log.

  ## Examples

      iex> update_t24_log(t24_log, %{field: new_value})
      {:ok, %T24Log{}}

      iex> update_t24_log(t24_log, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_t24_log(%T24Log{} = t24_log, attrs) do
    t24_log
    |> T24Log.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a t24_log.

  ## Examples

      iex> delete_t24_log(t24_log)
      {:ok, %T24Log{}}

      iex> delete_t24_log(t24_log)
      {:error, %Ecto.Changeset{}}

  """
  def delete_t24_log(%T24Log{} = t24_log) do
    Repo.delete(t24_log)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking t24_log changes.

  ## Examples

      iex> change_t24_log(t24_log)
      %Ecto.Changeset{data: %T24Log{}}

  """
  def change_t24_log(%T24Log{} = t24_log, attrs \\ %{}) do
    T24Log.changeset(t24_log, attrs)
  end
end
