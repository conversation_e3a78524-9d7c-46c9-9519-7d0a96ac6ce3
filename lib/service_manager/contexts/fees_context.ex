defmodule ServiceManager.Contexts.FeesContext do
  @moduledoc """
  The FeesContext context.
  Handles fee management, calculations, and notifications.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Fee
  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  alias ServiceManager.Contexts.ExchangeRateContext

  @query_params Application.compile_env(:service_manager, :query_params)

  @fee_categories [
    "account_maintenance",
    "transaction",
    "loan",
    "card",
    "atm",
    "international",
    "service"
  ]

  @calculation_methods ["percentage", "flat_rate"]
  @frequencies ["one_time", "daily", "weekly", "monthly", "yearly"]
  @application_times ["instant", "end_of_day", "end_of_month"]

  def get_fee_categories, do: @fee_categories
  def get_calculation_methods, do: @calculation_methods
  def get_frequencies, do: @frequencies
  def get_application_times, do: @application_times

  def retrieve(params \\ @query_params) do
    Fee
    |> DefaultQueries.status_query()
    # |> preload([:from_account, :to_account])
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [a],
      fragment("lower(?) LIKE lower(?)", a.name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.code, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.description, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.currency_code, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", a.status, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  @doc """
  Calculates the fee amount for a given transaction.
  Handles both percentage and flat rate calculations.
  """
  def calculate_fee_amount(%Fee{} = fee, transaction_amount) do
    try do
      amount =
        case fee.calculation_method do
          "percentage" when not is_nil(fee.percentage_rate) ->
            Decimal.mult(transaction_amount, Decimal.div(fee.percentage_rate, Decimal.new(100)))

          "flat_rate" when not is_nil(fee.amount) ->
            fee.amount

          _ ->
            {:error, :invalid_calculation_method}
        end

      case amount do
        {:error, _} = error -> error
        amount -> {:ok, apply_fee_limits(amount, fee)}
      end
    rescue
      e ->
        IO.inspect(e, label: "Fee Calculation Error")
        {:error, :calculation_failed}
    end
  end

  defp apply_fee_limits(amount, %Fee{} = fee) do
    amount
    |> maybe_apply_min(fee.min_amount)
    |> maybe_apply_max(fee.max_amount)
  end

  defp maybe_apply_min(amount, nil), do: amount

  defp maybe_apply_min(amount, min) do
    case Decimal.compare(amount, min) do
      :lt -> min
      _ -> amount
    end
  end

  defp maybe_apply_max(amount, nil), do: amount

  defp maybe_apply_max(amount, max) do
    case Decimal.compare(amount, max) do
      :gt -> max
      _ -> amount
    end
  end

  @doc """
  Converts an amount from one currency to another using exchange rates
  """
  def convert_amount(amount, source_currency, target_currency)
      when is_binary(source_currency) and is_binary(target_currency) do
    case ExchangeRateContext.get_exchange_rate(source_currency, target_currency) do
      {:ok, rate} ->
        converted = Decimal.mult(amount, Decimal.new(rate))
        {:ok, Decimal.round(converted, 2)}

      {:error, reason} ->
        {:error, reason}

      _ ->
        {:error, "Unable to get exchange rate"}
    end
  end

  @doc """
  Gets all fees that need notifications sent based on their effective dates
  """
  def get_fees_needing_notification do
    today = Date.utc_today()

    Fee
    |> where([f], f.notification_enabled == true)
    |> where([f], f.status == "active")
    |> where([f], fragment("? - ? = ?", f.effective_date, ^today, f.notification_days_before))
    |> Repo.all()
  end

  @doc """
  Compares fees across different account types
  Returns a map of account types and their associated fees
  """
  def compare_fees(fee_category) do
    Fee
    |> where([f], f.category == ^fee_category)
    |> where([f], f.status == "active")
    |> group_by([f], f.account_type)
    |> select([f], {f.account_type, fragment("json_agg(row_to_json(?))", f)})
    |> Repo.all()
    |> Enum.into(%{})
  end

  @doc """
  Gets all fees applicable to a specific account type
  """
  def get_fees_by_account_type(account_type) do
    Fee
    |> where([f], f.account_type == ^account_type)
    |> where([f], f.status == "active")
    |> Repo.all()
  end

  @doc """
  Gets all fees for a specific transaction type
  """
  def get_fees_by_transaction_type(transaction_type) do
    Fee
    |> where([f], f.transaction_type == ^transaction_type)
    |> where([f], f.status == "active")
    |> Repo.all()
  end

  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> create_fee()
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: user.id,
    #     activity: "User created",
    #     description: "Inserted a new user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def update_data(%Fee{} = user_record, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> update_fee(Map.put(params, "updated_by", user.id))
    end)
    # |> Ecto.Multi.run(:user_logs, fn _repo, %{data_struct: user_role} ->
    #   App.ContextUserLogs.create_user_log(%{
    #     user_id: user.id,
    #     activity: "User updated",
    #     description: "#{user.email} }updated user - ID: #{user_role.id}"
    #   })
    # end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Returns the list of fees.

  ## Examples

      iex> list_fees()
      [%Fee{}, ...]

  """
  def list_fees do
    Repo.all(Fee)
  end

  @doc """
  Gets a single fee.

  Raises `Ecto.NoResultsError` if the Fee does not exist.

  ## Examples

      iex> get_fee!(123)
      %Fee{}

      iex> get_fee!(456)
      ** (Ecto.NoResultsError)

  """
  def get_fee!(id), do: Repo.get!(Fee, id)

  @doc """
  Creates a fee.

  ## Examples

      iex> create_fee(%{field: value})
      {:ok, %Fee{}}

      iex> create_fee(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_fee(attrs \\ %{}) do
    %Fee{}
    |> Fee.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a fee.

  ## Examples

      iex> update_fee(fee, %{field: new_value})
      {:ok, %Fee{}}

      iex> update_fee(fee, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_fee(%Fee{} = fee, attrs) do
    fee
    |> Fee.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a fee.

  ## Examples

      iex> delete_fee(fee)
      {:ok, %Fee{}}

      iex> delete_fee(fee)
      {:error, %Ecto.Changeset{}}

  """
  def delete_fee(%Fee{} = fee) do
    Repo.delete(fee)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking fee changes.

  ## Examples

      iex> change_fee(fee)
      %Ecto.Changeset{data: %Fee{}}

  """
  def change_fee(%Fee{} = fee, attrs \\ %{}) do
    Fee.changeset(fee, attrs)
  end
end
