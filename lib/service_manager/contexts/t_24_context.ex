defmodule ServiceManager.Contexts.T24Context do
  @moduledoc """
  The T24Context handles interactions with T24 banking system settings.
  """

  import Ecto.Query, warn: false
  require Logger
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Settings.T24

  @doc """
  Gets the T24 settings schema, either returning the existing settings
  or creating a new empty schema if none exists.

  ## Returns
    - %T24{} - Either existing settings or new empty schema
  """
  def get_setting_schema do
    case get_setting() do
      nil -> %T24{}
      settings -> settings
    end
  end

  @doc """
  Gets the current T24 settings configuration.
  Returns the most recent T24 settings record.
  """
  def get_setting() do
    T24
    |> where([t], t.key == "t24")
    |> Repo.all()
    |> List.last()
  end

  @doc """
  Creates new T24 settings.

  ## Parameters
    - attrs: Map of attributes for the new settings

  ## Returns
    - {:ok, %T24{}} on success
    - {:error, changeset} on validation failure
  """
  def create_setting(attrs \\ %{}) do
    %T24{}
    |> T24.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates existing T24 settings.

  ## Parameters
    - t24_settings: Existing T24 settings struct to update
    - attrs: Map of attributes to update

  ## Returns
    - {:ok, %T24{}} on success
    - {:error, changeset} on validation failure
  """
  def update_setting(%T24{} = t24_settings, attrs) do
    t24_settings
    |> T24.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Returns a changeset for tracking T24 settings changes.
  """
  def change_setting(%T24{} = t24_settings, attrs \\ %{}) do
    T24.changeset(t24_settings, attrs)
  end

  @doc """
  Inserts new T24 settings with user tracking.

  ## Parameters
    - params: Settings parameters
    - user: User performing the action
  """
  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> create_setting()
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  @doc """
  Updates existing T24 settings with user tracking.

  ## Parameters
    - t24_settings: Existing T24 settings to update
    - params: Update parameters
    - user: User performing the update
  """
  def update_data(%T24{} = t24_settings, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      t24_settings
      |> update_setting(Map.put(params, "updated_by", user.id))
    end)
    |> Repo.transaction()
    |> ServiceManager.Utilities.complete_transaction_handler()
  end

  def multi_cache(multi) do
    multi
    |> Ecto.Multi.run(:t24_cache, fn _, %{data_struct: data_struct} ->
      data_struct.config

      if data_struct.status in ["active", "ACTIVE"] do
        data_struct.config
        |> put_cache()
      else
        delete_cache()
      end
    end)
  end

  def put_cache(data) do
    Cachex.put(:settings, :t24, data)
  end

  def delete_cache() do
    Cachex.del(:settings, :t24)
  end

  @doc """
  Gets account balance from T24 via ESB.

  ## Parameters
    - account_number: The account number to fetch balance for

  ## Returns
    - {:ok, balance} on success
    - {:error, reason} on failure
  """
  def get_account_balance(account_number) do
    Logger.info("GetAccountDetails: Retrieving account details for account #{account_number}")

    case ServiceManager.Services.T24.Messages.GetAccountBalance.get_account_balance_parsed(
           account_number
         ) do
      %{"working_balance" => balance} when not is_nil(balance) ->
        {:ok, balance}

      response when is_map(response) ->
        Logger.error("Missing working balance in response: #{inspect(response)}")
        {:error, "Missing working balance"}

      {:error, error} ->
        Logger.error("T24 balance fetch error: #{inspect(error)}")
        {:error, error}
    end
  end

  def get_account_balance_extended(account_number) do
    Logger.info("GetAccountDetails: Retrieving account details for account #{account_number}")

    case ServiceManager.Services.T24.Messages.GetAccountBalance.get_account_balance_parsed(
           account_number
         ) do
      %{
        "working_balance" => working_balance,
        "available_balance" => available_balance,
        "cleared_balance" => cleared_balance,
        "online_actual_balance" => online_actual_balance
      }
      when not is_nil(working_balance) ->
        {:ok, working_balance, available_balance, cleared_balance, online_actual_balance}

      response when is_map(response) ->
        Logger.error("Missing working balance in response: #{inspect(response)}")
        {:error, "Missing working balance"}

      {:error, error} ->
        Logger.error("T24 balance fetch error: #{inspect(error)}")
        {:error, error}
    end
  end
end

# ServiceManager.Services.T24.Messages.GetAccountBalance.get_account_balance_parsed("*************")
