defmodule ServiceManager.Contexts.CustomersContext do
  @moduledoc """
  The CustomersContext handles all customer/user related database operations and business logic.
  This includes creating, reading, updating and deleting customer records, as well as handling
  customer activation and search functionality.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Queries.{DefaultQueries, FilterQueries}
  alias ServiceManager.{Repo, Utilities}
  alias ServiceManager.Accounts.User

  # Default query parameters loaded from config
  @query_params Application.compile_env(:service_manager, :query_params)

  @doc """
  Retrieves a paginated list of users with sorting and filtering applied.

  ## Parameters
    - params: Map of query parameters for filtering, sorting and pagination (optional)

  Returns `%Scrivener.Page{}` of users
  """
  def retrieve(params \\ @query_params) do
    User
    |> DefaultQueries.email_query(params)
    # |> DefaultQueries.name_query(params)
    |> DefaultQueries.nickname_query(params)
    |> DefaultQueries.first_name_query(params)
    |> DefaultQueries.last_name_query(params)
    |> DefaultQueries.phone_number_query(params)
    |> DefaultQueries.approved_query(params)
    |> DefaultQueries.tag_query(params)
    |> DefaultQueries.account_name_query(params)
    |> DefaultQueries.account_type_query(params)
    |> DefaultQueries.currency_query(params)
    |> DefaultQueries.balance_range_query(params)
    |> DefaultQueries.sorting_query(params)
    |> DefaultQueries.date_range_query(params)
    |> DefaultQueries.pagination_query(params)
  end

  @doc """
  Retrieves customer data for reports based on filter parameters.

  Applies filters from the form parameters to query customers and their bank accounts.
  Returns list of customers matching the filter criteria.

  ## Parameters
    - params: Map of filter parameters from the report form
  """
  def retrieve_report_data(params) do
    User
    |> join(:left, [u], b in ServiceManager.Schemas.Accounts.BankAccount, on: b.user_id == u.id)
    |> DefaultQueries.email_query(params)
    # |> DefaultQueries.name_query(params)
    |> DefaultQueries.nickname_query(params)
    |> DefaultQueries.first_name_query(params)
    |> DefaultQueries.last_name_query(params)
    |> DefaultQueries.phone_number_query(params)
    |> DefaultQueries.approved_query(params)
    |> DefaultQueries.tag_query(params)
    |> DefaultQueries.account_name_query(params)
    |> DefaultQueries.account_type_query(params)
    |> DefaultQueries.currency_query(params)
    |> DefaultQueries.balance_range_query(params)
    |> DefaultQueries.sorting_query(params)
    |> DefaultQueries.date_range_query(params)
    |> search_filter(params)
    |> Repo.all()
  end

  @doc """
  No-op search filter when search parameter is empty or nil
  """
  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  @doc """
  Filters users and their bank accounts by searching across multiple fields using case-insensitive LIKE queries.
  Searches user fields (first_name, last_name, phone_number, nickname, email) and bank account fields
  (name, number, type, tag).

  ## Parameters
    - query: The base Ecto query
    - params: Map containing "search" parameter with search term
  """
  def search_filter(query, %{"search" => search}) do
    query
    |> join(:left, [u], b in ServiceManager.Schemas.Accounts.BankAccount, on: b.user_id == u.id)
    |> where(
      [u, b],
      # fragment("lower(?) LIKE lower(?)", b.name, ^"%#{search}%") or
      fragment("lower(?) LIKE lower(?)", u.first_name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", u.last_name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", u.phone_number, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", u.nickname, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", u.email, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", b.number, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", b.type, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", b.tag, ^"%#{search}%")
    )
    |> distinct(true)
  end

  @doc """
  Default case for search_filter when no search params provided
  """
  def search_filter(query, _), do: query

  @doc """
  Returns the list of all users without any filtering or pagination.

  ## Examples

      iex> list_users()
      [%User{}, ...]

  """
  def list_users, do: Repo.all(User)

  @doc """
  Gets a single user by ID.

  Raises `Ecto.NoResultsError` if the User does not exist.

  ## Parameters
    - id: The ID of the user to fetch

  ## Examples

      iex> get_data!(123)
      %User{}

      iex> get_data!(456)
      ** (Ecto.NoResultsError)

  """
  def get_data!(id), do: Repo.get!(User, id)

  @doc """
  Creates a new user.

  ## Parameters
    - attrs: Map of attributes for the new user

  ## Examples

      iex> create_user(%{field: value})
      {:ok, %User{}}

      iex> create_user(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_user(attrs \\ %{}) do
    %User{}
    |> User.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Inserts a new user record with additional metadata.
  Handles the transaction and generates a random password.

  ## Parameters
    - params: Map of user attributes
    - user: The user performing the action (for audit)
  """
  def insert_data(params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      params
      |> Map.put("created_by", user.id)
      |> Map.put("password", Utilities.password())
      |> create_user()
    end)
    |> Repo.transaction()
    |> Utilities.complete_transaction_handler()
  end

  @doc """
  Updates a user's information.

  ## Parameters
    - user: The user struct to update
    - attrs: Map of attributes to update

  ## Examples

      iex> update_user(user, %{field: new_value})
      {:ok, %User{}}

      iex> update_user(user, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_user(%User{} = user, attrs) do
    user
    |> User.update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Activates a user account with the provided attributes.
  """
  def activate_user(%User{} = user, attrs) do
    user
    |> User.activate_update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates user data within a transaction, including audit information.

  ## Parameters
    - user_record: The user record to update
    - params: Map of update attributes
    - user: The user performing the update (for audit)
  """
  def update_data(%User{} = user_record, params, user) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> update_user(Map.put(params, "updated_by", user.id))
    end)
    |> Repo.transaction()
    |> Utilities.complete_transaction_handler()
  end

  @doc """
  Activates a user account and generates a new password.
  Handles the activation within a transaction.

  ## Parameters
    - user_record: The user record to activate
    - params: Additional activation parameters
    - user: The user performing the activation (for audit)
  """
  def user_activation(user_record, params, user) do
    password = Utilities.password()

    Ecto.Multi.new()
    |> Ecto.Multi.run(:data_struct, fn _, _ ->
      user_record
      |> activate_user(
        params
        |> Map.put("updated_by", user.id)
        |> Map.put("password", password)
      )
    end)
    |> Repo.transaction()
    |> Utilities.complete_transaction_handler()
  end

  @doc """
  Deletes a user record.

  ## Parameters
    - user: The user struct to delete

  ## Examples

      iex> delete_data(user)
      {:ok, %User{}}

      iex> delete_data(user)
      {:error, %Ecto.Changeset{}}

  """
  def delete_data(%User{} = user), do: Repo.delete(user)

  @doc """
  Returns a changeset for tracking user changes.

  ## Parameters
    - user: The user struct to create a changeset for
    - attrs: Optional map of attributes to apply

  ## Examples

      iex> change_data(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_data(%User{} = user, attrs \\ %{}) do
    User.changeset(user, attrs)
  end
end
