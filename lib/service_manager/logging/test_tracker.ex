defmodule ServiceManager.Logging.TestTracker do
  use ServiceManager.Logging.FunctionTracker

  track do
    def process_transaction(chain_id, amount) do
      # Add small delay to simulate work
      Process.sleep(100)
      {:ok, amount}
    end
  end

  track do
    def update_balance(amount) do
      # Add small delay to simulate work
      Process.sleep(50)
      {:ok, amount}
    end
  end

  track do
    def add(a, b) do
      # Add small delay to simulate work
      Process.sleep(100)
      a + b
    end
  end

  track do
    def greet(name) do
      # Add small delay to simulate work
      Process.sleep(50)
      "Hello, #{name}!"
    end
  end

  track do
    def process_data(data) when is_list(data) do
      # Add small delay to simulate work
      Process.sleep(150)
      Enum.map(data, &(&1 * 2))
    end
  end
end
