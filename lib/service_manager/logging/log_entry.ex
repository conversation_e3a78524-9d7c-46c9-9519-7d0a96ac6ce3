defmodule ServiceManager.Logging.LogEntry do
  use Ecto.Schema
  import Ecto.Changeset

  schema "log_entries" do
    field :chain_id, :string
    field :function_name, :string
    field :module_name, :string
    field :process_id, :string
    field :duration_ms, :integer
    field :params, :string
    field :result, :string
    field :process_state, :string
    field :request_info, :string
    field :response_info, :string
    field :timestamp, :utc_datetime
    field :file_path, :string
    field :raw_log, :string

    timestamps()
  end

  @required_fields ~w(chain_id function_name module_name process_id timestamp)a
  @optional_fields ~w(duration_ms params result process_state request_info response_info file_path raw_log)a

  def changeset(log_entry, attrs) do
    log_entry
    |> cast(attrs, @required_fields ++ @optional_fields)
    |> validate_required(@required_fields)
  end
end
