defmodule ServiceManager.Transfers.TransferResult do
  @moduledoc """
  Standardized transfer result structure returned by the transfer pipeline.
  Provides consistent response format across all transfer types.
  """
  
  defstruct [
    :transfer_id,
    :status,
    :transaction_reference,
    :external_reference,
    :amount,
    :source_account,
    :destination_account,
    :processed_at,
    :metadata,
    :errors,
    :warnings
  ]
  
  @type status :: :pending | :processing | :completed | :failed | :cancelled
  
  @type t :: %__MODULE__{
    transfer_id: String.t(),
    status: status(),
    transaction_reference: String.t(),
    external_reference: String.t() | nil,
    amount: Decimal.t(),
    source_account: String.t(),
    destination_account: String.t(),
    processed_at: DateTime.t(),
    metadata: map(),
    errors: [String.t()],
    warnings: [String.t()]
  }
end