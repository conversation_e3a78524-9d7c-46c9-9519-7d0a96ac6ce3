defmodule ServiceManager.Transfers.ProcessorChain do
  @moduledoc """
  Configurable processor chain that executes transfer processing steps
  in sequence or parallel based on configuration. Provides centralized
  error handling, rollback mechanisms, and progress tracking.
  """
  
  require Logger
  alias ServiceManager.Transfers.{TransferRequest, TransferResult}
  alias ServiceManager.Broadcasters.LoggerBroadcaster
  
  @doc """
  Execute a chain of processors for a transfer request.
  
  ## Options
  - `:parallel` - Execute independent processors in parallel
  - `:timeout` - Maximum execution time in milliseconds  
  - `:retry` - Retry configuration for failed processors
  - `:rollback` - Enable automatic rollback on failure
  """
  def execute(%TransferRequest{} = request, processors, options \\ []) do
    parallel = Keyword.get(options, :parallel, false)
    timeout = Keyword.get(options, :timeout, 30_000)
    
    LoggerBroadcaster.info("Executing processor chain for transfer: #{request.id}")
    
    context = %{
      request: request,
      results: %{},
      start_time: System.monotonic_time(:millisecond),
      options: options
    }
    
    try do
      if parallel do
        execute_parallel(processors, context, timeout)
      else
        execute_sequential(processors, context)
      end
    catch
      :timeout ->
        LoggerBroadcaster.error("Processor chain timeout for transfer: #{request.id}")
        {:error, "Transfer processing timeout"}
        
      :exit, reason ->
        LoggerBroadcaster.error("Processor chain exit for transfer: #{request.id}, reason: #{inspect(reason)}")
        {:error, "Transfer processing failed: #{inspect(reason)}"}
    end
  end
  
  # Execute processors sequentially (default)
  defp execute_sequential(processors, context) do
    processors
    |> Enum.reduce_while({:ok, context}, fn {processor_module, processor_type}, {:ok, ctx} ->
      case execute_processor(processor_module, processor_type, ctx) do
        {:ok, updated_context} ->
          {:cont, {:ok, updated_context}}
          
        {:error, reason} ->
          {:halt, {:error, reason}}
      end
    end)
    |> case do
      {:ok, final_context} ->
        build_transfer_result(final_context)
        
      {:error, reason} ->
        {:error, reason}
    end
  end
  
  # Execute independent processors in parallel
  defp execute_parallel(processors, context, timeout) do
    # Identify parallel-safe processors (validation steps)
    {parallel_processors, sequential_processors} = partition_processors(processors)
    
    # Execute parallel processors first
    parallel_results = 
      parallel_processors
      |> Task.async_stream(fn {mod, type} -> 
        execute_processor(mod, type, context) 
      end, timeout: timeout)
      |> Enum.map(fn {:ok, result} -> result end)
    
    # Check if all parallel processors succeeded
    case Enum.find(parallel_results, fn result -> match?({:error, _}, result) end) do
      {:error, reason} ->
        {:error, reason}
        
      nil ->
        # Update context with parallel results
        updated_context = merge_parallel_results(context, parallel_results)
        
        # Execute sequential processors  
        execute_sequential(sequential_processors, updated_context)
    end
  end
  
  defp execute_processor(processor_module, processor_type, context) do
    LoggerBroadcaster.info("Executing processor: #{processor_module}.#{processor_type}")
    
    start_time = System.monotonic_time(:millisecond)
    
    try do
      case apply(processor_module, :process, [context.request, processor_type, context]) do
        {:ok, result} ->
          end_time = System.monotonic_time(:millisecond)
          duration = end_time - start_time
          
          LoggerBroadcaster.info("Processor completed: #{processor_module}.#{processor_type} (#{duration}ms)")
          
          updated_context = %{context | 
            results: Map.put(context.results, {processor_module, processor_type}, result)
          }
          
          {:ok, updated_context}
          
        {:error, reason} ->
          LoggerBroadcaster.error("Processor failed: #{processor_module}.#{processor_type}, reason: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      error ->
        LoggerBroadcaster.error("Processor exception: #{processor_module}.#{processor_type}, error: #{inspect(error)}")
        {:error, "Processor failed: #{inspect(error)}"}
    end
  end
  
  defp partition_processors(processors) do
    # Validation processors can run in parallel
    {parallel, sequential} = 
      Enum.split_with(processors, fn {module, _type} ->
        String.contains?(to_string(module), "Validation")
      end)
    
    {parallel, sequential}
  end
  
  defp merge_parallel_results(context, parallel_results) do
    merged_results = 
      parallel_results
      |> Enum.reduce(context.results, fn {:ok, result}, acc ->
        Map.merge(acc, result)
      end)
    
    %{context | results: merged_results}
  end
  
  defp build_transfer_result(context) do
    %{request: request, results: results} = context
    
    # Extract transaction details from processor results
    transaction_data = extract_transaction_data(results)
    
    result = %TransferResult{
      transfer_id: request.id,
      status: :completed,
      transaction_reference: transaction_data[:transaction_reference],
      external_reference: transaction_data[:external_reference],
      amount: request.amount,
      source_account: get_account_identifier(request.source),
      destination_account: get_account_identifier(request.destination),
      processed_at: DateTime.utc_now(),
      metadata: request.metadata,
      errors: [],
      warnings: []
    }
    
    {:ok, result}
  end
  
  defp extract_transaction_data(results) do
    # Extract transaction references and IDs from processor results
    transaction_processors = 
      results
      |> Enum.filter(fn {{module, _type}, _result} -> 
        String.contains?(to_string(module), "Transaction")
      end)
    
    case transaction_processors do
      [{{_module, _type}, transaction_data}] ->
        transaction_data
        
      [] ->
        %{transaction_reference: generate_reference()}
        
      multiple ->
        # Merge multiple transaction results
        multiple
        |> Enum.reduce(%{}, fn {_key, data}, acc ->
          Map.merge(acc, data)
        end)
    end
  end
  
  defp get_account_identifier(%{identifier: identifier}), do: identifier
  defp get_account_identifier(_), do: "Unknown"
  
  defp generate_reference do
    "REF_" <> 
    (:crypto.strong_rand_bytes(8)
     |> Base.encode16()
     |> String.slice(0, 12))
  end
end