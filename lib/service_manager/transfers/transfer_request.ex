defmodule ServiceManager.Transfers.TransferRequest do
  @moduledoc """
  Unified transfer request structure that standardizes all transfer types
  into a common format for processing through the transfer pipeline.
  """
  
  defstruct [
    :id,
    :type,
    :source,
    :destination, 
    :amount,
    :metadata,
    :created_at,
    :status,
    :context
  ]
  
  @type transfer_type :: :account_to_wallet | :wallet_to_account | :bank_to_bank | 
                        :bank_to_other_bank | :self_transfer | :reversal
                        
  @type account_source :: %{type: :account, identifier: String.t(), details: map()}
  @type wallet_source :: %{type: :wallet, identifier: String.t(), details: map()}
  @type source :: account_source() | wallet_source()
  
  @type t :: %__MODULE__{
    id: String.t(),
    type: transfer_type(),
    source: source(),
    destination: source(),
    amount: Decimal.t(),
    metadata: map(),
    created_at: DateTime.t(),
    status: atom(),
    context: map()
  }
end