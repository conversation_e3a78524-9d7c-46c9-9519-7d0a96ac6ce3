# Test the request pool with a create account request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting create account request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "POST",
    url: "https://fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************",
    headers: %{
      "Content-Type" => "application/json"
    },
    body: %{
      "header" => %{},
      "body" => %{
        "customerId" => "748719",
        "currency" => "MWK"
      }
    },
    name: "create_account",
    reference: "create_acc_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show the created account details in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nCreated Account Details:")

      case get_in(req.response, ["body", "body"]) do
        account when is_map(account) ->
          IO.puts("Account ID: #{account["accountId"]}")
          IO.puts("Customer ID: #{account["customerId"]}")
          IO.puts("Currency: #{account["currency"]}")
          IO.puts("Status: #{account["status"]}")

          if is_map_key(account, "message") do
            IO.puts("Message: #{account["message"]}")
          end

          if is_map_key(account, "transactionId") do
            IO.puts("Transaction ID: #{account["transactionId"]}")
          end

        _ ->
          IO.puts("\nNo account details found in response")
      end

    _ ->
      :ok
  end
end
