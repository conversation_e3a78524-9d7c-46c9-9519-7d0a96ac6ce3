# Test the request pool with an account transactions request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting account transactions request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "GET",
    url: "https://fdh-esb.ngrok.dev/api/esb/reports/v1/account/transactions/*************",
    headers: %{
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    # No body needed for GET request
    body: %{},
    name: "get_account_transactions",
    reference: "transactions_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show the transactions in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nAccount Transactions:")

      case get_in(req.response, ["body", "body"]) do
        transactions when is_list(transactions) ->
          Enum.each(transactions, fn transaction ->
            IO.puts("\nTransaction:")
            IO.puts("ID: #{transaction["transactionId"]}")
            IO.puts("Date: #{transaction["valueDate"]}")
            IO.puts("Type: #{transaction["transactionType"]}")
            IO.puts("Amount: #{transaction["amount"]} #{transaction["currency"]}")

            if is_map_key(transaction, "narrative") do
              IO.puts("Description: #{transaction["narrative"]}")
            end

            if is_map_key(transaction, "balance") do
              IO.puts("Balance After: #{transaction["balance"]}")
            end

            if is_map_key(transaction, "status") do
              IO.puts("Status: #{transaction["status"]}")
            end
          end)

          # Show summary if available in header
          case get_in(req.response, ["body", "header"]) do
            header when is_map(header) ->
              IO.puts("\nSummary:")

              if is_map_key(header, "total_size"),
                do: IO.puts("Total Transactions: #{header["total_size"]}")

              if is_map_key(header, "page_size"), do: IO.puts("Page Size: #{header["page_size"]}")

              if is_map_key(header, "page_start"),
                do: IO.puts("Page Start: #{header["page_start"]}")

            _ ->
              :ok
          end

        _ ->
          IO.puts("\nNo transactions found in response")
      end

    _ ->
      :ok
  end
end
