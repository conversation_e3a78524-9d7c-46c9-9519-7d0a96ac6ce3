# Test the request pool with a customer number details request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting customer number details request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "GET",
    url: "https://fdh-esb.ngrok.dev/api/esb/customer/number/1.0/details/144",
    headers: %{
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    # No body needed for GET request
    body: %{},
    name: "get_customer_number_details",
    reference: "customer_number_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show the customer details in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nCustomer Details:")

      case get_in(req.response, ["body", "body"]) do
        customer when is_map(customer) ->
          IO.puts("\nBasic Information:")
          IO.puts("Customer Number: #{customer["customerNumber"]}")

          if is_map_key(customer, "firstName") do
            IO.puts("Name: #{customer["firstName"]} #{customer["lastName"]}")
          end

          if is_map_key(customer, "phoneNumber") do
            IO.puts("Phone: #{customer["phoneNumber"]}")
          end

          if is_map_key(customer, "email") do
            IO.puts("Email: #{customer["email"]}")
          end

          if is_map_key(customer, "dateOfBirth") do
            IO.puts("Date of Birth: #{customer["dateOfBirth"]}")
          end

          # Show address information
          if Enum.any?(["street", "city", "postCode"], &Map.has_key?(customer, &1)) do
            IO.puts("\nAddress Information:")
            if is_map_key(customer, "street"), do: IO.puts("Street: #{customer["street"]}")
            if is_map_key(customer, "city"), do: IO.puts("City: #{customer["city"]}")
            if is_map_key(customer, "postCode"), do: IO.puts("Post Code: #{customer["postCode"]}")
          end

          # Show additional details
          if is_map_key(customer, "status") do
            IO.puts("\nStatus: #{customer["status"]}")
          end

          if is_map_key(customer, "customerType") do
            IO.puts("Customer Type: #{customer["customerType"]}")
          end

          # Show accounts if available
          case get_in(customer, ["accounts"]) do
            accounts when is_list(accounts) ->
              IO.puts("\nLinked Accounts:")

              Enum.each(accounts, fn account ->
                IO.puts("\nAccount:")
                IO.puts("Number: #{account["accountNumber"]}")

                if is_map_key(account, "accountType"),
                  do: IO.puts("Type: #{account["accountType"]}")

                if is_map_key(account, "currency"),
                  do: IO.puts("Currency: #{account["currency"]}")

                if is_map_key(account, "status"), do: IO.puts("Status: #{account["status"]}")

                if is_map_key(account, "branchCode"),
                  do: IO.puts("Branch Code: #{account["branchCode"]}")
              end)

            _ ->
              :ok
          end

          case get_in(req.response, ["body", "header"]) do
            header when is_map(header) ->
              IO.puts("\nResponse Information:")
              if is_map_key(header, "status"), do: IO.puts("Status: #{header["status"]}")
              if is_map_key(header, "message"), do: IO.puts("Message: #{header["message"]}")

            _ ->
              :ok
          end

        _ ->
          IO.puts("\nNo customer details found in response")
      end

    _ ->
      :ok
  end
end
