# Test the request pool with an account balance request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting account balance request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "GET",
    url:
      "http://172.25.136.154:9090/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************",
    # No headers needed for this request
    headers: %{},
    # No body needed for GET request
    body: %{},
    name: "get_account_balance",
    reference: "balance_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 3
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show balance information in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nAccount Balance Information:")

      case get_in(req.response, ["body", "body"]) do
        balance when is_map(balance) ->
          IO.puts("Account Number: #{balance["accountNumber"]}")
          IO.puts("Currency: #{balance["currency"]}")
          IO.puts("Available Balance: #{balance["availableBalance"]}")
          IO.puts("Current Balance: #{balance["currentBalance"]}")
          IO.puts("Working Balance: #{balance["workingBalance"]}")

          if is_map_key(balance, "lockedAmount") do
            IO.puts("Locked Amount: #{balance["lockedAmount"]}")
          end

        _ ->
          IO.puts("\nNo balance information found in response")
      end

    _ ->
      :ok
  end
end
