defmodule ServiceManager.Pool.RequestPool do
  @moduledoc """
  Context module for managing the request pool.
  Provides functions for creating and enqueueing requests.
  """

  import Ecto.Query
  import ServiceManager.Logging.FunctionTracker
  alias ServiceManager.Pool.{FinchRequest, FinchRequestJob}
  alias ServiceManager.Repo

  @doc """
  Creates and enqueues a new request.

  ## Examples

      iex> create_request(%{
        method: "GET",
        url: "https://api.example.com",
        name: "fetch_user",
        reference: "user_123"
      })
      {:ok, %FinchRequest{}}

  """
  @spec create_request(map()) :: {:ok, FinchRequest.t()} | {:error, Ecto.Changeset.t()}
  track do
    def create_request(attrs) do
      %FinchRequest{}
      |> FinchRequest.changeset(attrs)
      |> Repo.insert()
      |> case do
        {:ok, request} -> enqueue_request(request)
        error -> error
      end
    end
  end

  @doc """
  Creates a request from Finch parameters and enqueues it.

  ## Examples

      iex> create_and_enqueue("GET", "https://api.example.com", [], nil, [],
        name: "fetch_user",
        reference: "user_123"
      )
      {:ok, %FinchRequest{}}

  """
  @spec create_and_enqueue(
          Finch.Request.method(),
          Finch.Request.url(),
          Finch.Request.headers(),
          Finch.Request.body(),
          Keyword.t(),
          Keyword.t()
        ) :: {:ok, FinchRequest.t()} | {:error, term()}
  track do
    def create_and_enqueue(method, url, headers, body, options, meta) do
      with {:ok, request} <- FinchRequest.from_params(method, url, headers, body, options, meta),
           {:ok, request} <- Repo.insert(request),
           {:ok, _job} <- enqueue_request(request) do
        {:ok, request}
      end
    end
  end

  @doc """
  Enqueues an existing request for processing.
  """
  @spec enqueue_request(FinchRequest.t()) :: {:ok, Oban.Job.t()} | {:error, term()}
  track do
    def enqueue_request(%FinchRequest{} = request) do
      FinchRequestJob.enqueue(request)
    end
  end

  @doc """
  Gets a request by ID.
  """
  @spec get_request(integer()) :: {:ok, FinchRequest.t()} | {:error, :not_found}
  track do
    def get_request(id) do
      case Repo.get(FinchRequest, id) do
        nil -> {:error, :not_found}
        request -> {:ok, request}
      end
    end
  end

  @doc """
  Lists requests with optional filters.

  ## Examples

      iex> list_requests(status: "pending", reference: "user_123")
      [%FinchRequest{}, ...]

  """
  @spec list_requests(Keyword.t()) :: [FinchRequest.t()]
  track do
    def list_requests(filters) do
      FinchRequest
      |> apply_filters(filters)
      |> Repo.all()
    end
  end

  # Private functions

  defp apply_filters(query, filters) do
    Enum.reduce(filters, query, fn
      {:status, status}, query ->
        from(r in query, where: r.status == ^status)

      {:reference, reference}, query ->
        from(r in query, where: r.reference == ^reference)

      {:name, name}, query ->
        from(r in query, where: r.name == ^name)

      _filter, query ->
        query
    end)
  end
end
