curl --location 'https://fdh-esb.ngrok.dev/api/esb/customers/v1/customer/profile/1617214' \
--header 'Authorization: Basic YWRtaW46YWRtaW4='

curl --location 'http://172.25.136.154:9090/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************' \
--header 'Content-Type: application/json' \
--data '{
    "header": {},
    "body": {
        "customerId": "748719",
        "currency": "MWK"
    }
}
'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/cb/bookbalance/book/balances/*************' \
--header 'Authorization: Basic YWRtaW46YWRtaW4='

curl --location 'https://fdh-esb.ngrok.dev/api/esb/cb/aclockedevents/reserve/funds/1' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "accountId": "*************",
        "lockedAmount": 100.00
    }
}
'
curl --location 'https://fdh-esb.ngrok.dev/api/esb/report/transaction/FT24204740T1' \
--header 'Authorization: Basic YWRtaW46YWRtaW4='


curl --location 'http://172.25.136.154:9090/releaseReservedFunds/api/v1.0.0/party/aclockedevents/release/reserved/funds/*************' \
--header 'Content-Type: application/json' \
--data '{
    "header": {},
    "body": {
        "accountId": "*************",
        "lockedAmount": 100.00
    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/reports/v1/account/transactions/*************' \
--header 'Authorization: Basic YWRtaW46YWRtaW4='


curl --location 'http://172.25.136.154:9090/currencyExchanges/api/v1.0.0/party/currency/exchrate/MWK/USD/100.00/BUY'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/accounts/1.0/account/*************' \
--header 'Authorization: Basic YWRtaW46YWRtaW4='

curl --location 'https://fdh-esb.ngrok.dev/api/esb/transfers/own/1.0/payments/mobile/own' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccountNumber": "*************",
        "debitAmount": 100.00,
        "debitCurrency": "MWK",
        "creditAccountNumber": "*************"

    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/transfers/own/1.0/payments/mobile/own' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccountNumber": "*************",
        "debitAmount": 100.00,
        "debitCurrency": "MWK",
        "creditAccountNumber": "*************"

    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/transfers/other/1.0/bank' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccountNumber": "*************",
        "debitAmount": 100.00,
        "debitCurrency": "MWK",
        "creditAccountNumber": "*************"

    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/transfers/wallet/1.0/mobile/ofs/fdh' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccountNumber": "*************",
        "debitAmount": 100.00,
        "debitCurrency": "MWK",
        "creditAccountNumber": "*************"

    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/transfers/mobilemoney/wallet/1.0/mobile/ofs' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccountNumber": "*************",
        "debitAmount": 100.00,
        "debitCurrency": "MWK",
        "creditAccountNumber": "*************"

    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/transfers/fdh/1.0/payments/mobile/ofs' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
     "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccountNumber": "*************",
        "debitAmount": 100.00,
        "debitCurrency": "MWK",
        "creditAccountNumber": "*************"

    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/premier/1.0/bet' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccount": "*************",
        "debitAmount": 100.00,
        "debitCurrency": "MWK",
        "creditAccount": "*************",
        "creditCurrency": "MWK",
        "Amount": "MWK100.00",
        "CURRENCY": "MWK",
        "transactionRef": "transfer",
        "accountNumber": "*************"
    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/transaction/1.0/initiate/transaction' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccountNumber": "*************",
        "debitAmount": 100.00,
        "debitCurrency": "MWK",
        "creditAccountNumber": "*************",
        "creditCurrencyId": "MWK"
    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/paisa/1.0/payments/hellopaisa' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccount": "*************",
        "debitAmount": 100.00,
        "debitCurrency": "MWK",
        "creditAccount": "*************",
        "creditCurrency": "MWK",
        "Amount": "MWK100.00",
        "CURRENCY": "MWK"
    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/standing/1.0/orders/*************.902' \
--header 'Authorization: Basic YWRtaW46YWRtaW4='


curl --location 'https://fdh-esb.ngrok.dev/api/esb/statement/1.0/mini/stmt/*************' \
--header 'Authorization: Basic YWRtaW46YWRtaW4='


curl --location 'https://fdh-esb.ngrok.dev/api/esb/customer/1.0/phone/**********' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data ''


curl --location 'https://fdh-esb.ngrok.dev/api/esb/customer/number/1.0/details/144' \
--header 'Authorization: Basic YWRtaW46YWRtaW4='


curl --location 'https://fdh-esb.ngrok.dev/api/esb/account_by_phone/1.0/phone/**********' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data ''


curl --location 'https://fdh-esb.ngrok.dev/api/esb/crwd/1.0/bill/payment/app' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccount": "*************",
        "debitAmountId": 100.00,
        "debitCurrencyId": "MWK",
        "creditAccountId": "*************",
        "creditCurrencyId": "MWK",
        "Amount": "MWK100.00",
        "CURRENCY": "MWK"
    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/virtual/1.0/account/create/fdh/acc' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "customerId": "748719",
        "currency": "MWK"
    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/thunes/1.0/ebthunes/sms/commit' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "phoneNumber": "+************",
        "thunesId": "********"
    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/create_account/1.0/account/irisapi' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "customer": "1617214",
        "category": "1036",
        "currency": "MWK"
    }
}'

curl --location 'https://fdh-esb.ngrok.dev/api/esb/azam/1.0/azamtv/prepaid/mob' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46YWRtaW4=' \
--data '{
    "header": {},
    "body": {
        "transactionType": "AC",
        "debitAccount": "*************",
        "debitAmount": 100.00,
        "debitCurrency": "MWK",
        "creditAccount": "*************",
        "creditCurrency": "MWK",
        "Amount": "MWK100.00",
        "CURRENCY": "MWK",
        "transactionRef": "transfer"
    }
}'