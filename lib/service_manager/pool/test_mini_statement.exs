# Test the request pool with a mini statement request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting mini statement request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "GET",
    url: "https://fdh-esb.ngrok.dev/api/esb/statement/1.0/mini/stmt/1440000057995",
    headers: %{
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    # No body needed for GET request
    body: %{},
    name: "get_mini_statement",
    reference: "mini_stmt_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show the mini statement in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nMini Statement:")

      case get_in(req.response, ["body", "body"]) do
        statement when is_map(statement) ->
          # Show account information
          IO.puts("\nAccount Information:")
          IO.puts("Account Number: #{statement["accountNumber"]}")

          if is_map_key(statement, "accountName"),
            do: IO.puts("Account Name: #{statement["accountName"]}")

          if is_map_key(statement, "currency"), do: IO.puts("Currency: #{statement["currency"]}")

          if is_map_key(statement, "currentBalance"),
            do: IO.puts("Current Balance: #{statement["currentBalance"]}")

          # Show transactions
          case get_in(statement, ["transactions"]) do
            transactions when is_list(transactions) ->
              IO.puts("\nRecent Transactions:")

              Enum.each(transactions, fn txn ->
                IO.puts("\nTransaction:")
                IO.puts("Date: #{txn["valueDate"]}")
                IO.puts("Type: #{txn["transactionType"]}")
                IO.puts("Amount: #{txn["amount"]} #{txn["currency"]}")

                if is_map_key(txn, "narrative") do
                  IO.puts("Description: #{txn["narrative"]}")
                end

                if is_map_key(txn, "balance") do
                  IO.puts("Balance After: #{txn["balance"]}")
                end

                if is_map_key(txn, "reference") do
                  IO.puts("Reference: #{txn["reference"]}")
                end
              end)

            _ ->
              :ok
          end

          # Show summary if available in header
          case get_in(req.response, ["body", "header"]) do
            header when is_map(header) ->
              IO.puts("\nSummary:")
              if is_map_key(header, "status"), do: IO.puts("Status: #{header["status"]}")

              if is_map_key(header, "total_size"),
                do: IO.puts("Total Transactions: #{header["total_size"]}")

            _ ->
              :ok
          end

        _ ->
          IO.puts("\nNo statement details found in response")
      end

    _ ->
      :ok
  end
end
