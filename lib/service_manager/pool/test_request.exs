# Test the request pool with a transfer request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting request pool test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "POST",
    url: "https://fdh-esb.ngrok.dev/api/esb/transfers/v1/payments/generic/1",
    headers: %{
      "Content-Type" => "application/json",
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    body: %{
      "header" => %{},
      "body" => %{
        "transactionType" => "AC",
        "debitAccountId" => "*************",
        "debitAmount" => 50.00,
        "debitCurrencyId" => "MWK",
        "creditAccountId" => "*************",
        "creditCurrencyId" => "MWK"
      }
    },
    name: "test_transfer",
    reference: "transfer_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# Increased timeout
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# List all processed requests
IO.puts("\nAll processed requests:")
Logger.info("Listing all processed requests")

RequestPool.list_requests()
|> Enum.filter(&(&1.status in ["completed", "failed"]))
|> Enum.each(fn req ->
  request_info = """

  Request: #{req.name} (#{req.reference})
  Status: #{req.status}
  Response: #{inspect(req.response, pretty: true)}
  """

  IO.puts(request_info)
  Logger.info(request_info)
end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)
