# Test the request pool with a transaction report request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting transaction report request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "GET",
    url: "https://fdh-esb.ngrok.dev/api/esb/report/transaction/FT24204740T1",
    headers: %{
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    # No body needed for GET request
    body: %{},
    name: "get_transaction_report",
    reference: "txn_report_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show the transaction report in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nTransaction Report:")

      case get_in(req.response, ["body", "body"]) do
        transaction when is_map(transaction) ->
          IO.puts("\nTransaction Details:")
          IO.puts("Transaction ID: #{transaction["transactionId"]}")
          IO.puts("Status: #{transaction["status"]}")
          IO.puts("Type: #{transaction["transactionType"]}")

          if is_map_key(transaction, "amount") do
            IO.puts("Amount: #{transaction["amount"]}")
          end

          if is_map_key(transaction, "currency") do
            IO.puts("Currency: #{transaction["currency"]}")
          end

          if is_map_key(transaction, "valueDate") do
            IO.puts("Value Date: #{transaction["valueDate"]}")
          end

          if is_map_key(transaction, "narrative") do
            IO.puts("Narrative: #{transaction["narrative"]}")
          end

          case get_in(transaction, ["accounts"]) do
            accounts when is_list(accounts) ->
              IO.puts("\nAccounts Involved:")

              Enum.each(accounts, fn account ->
                IO.puts("\nAccount:")
                IO.puts("  Number: #{account["accountNumber"]}")
                IO.puts("  Role: #{account["role"]}")
                if Map.has_key?(account, "amount"), do: IO.puts("  Amount: #{account["amount"]}")

                if Map.has_key?(account, "currency"),
                  do: IO.puts("  Currency: #{account["currency"]}")
              end)

            _ ->
              :ok
          end

        _ ->
          IO.puts("\nNo transaction details found in response")
      end

    _ ->
      :ok
  end
end
