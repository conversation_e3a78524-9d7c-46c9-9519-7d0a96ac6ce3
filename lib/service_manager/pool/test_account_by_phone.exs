# Test the request pool with an account by phone request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting account by phone request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "GET",
    url: "https://fdh-esb.ngrok.dev/api/esb/account_by_phone/1.0/phone/**********",
    headers: %{
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    # No body needed for GET request
    body: %{},
    name: "get_account_by_phone",
    reference: "account_phone_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show the account details in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nAccount Details:")

      case get_in(req.response, ["body", "body"]) do
        accounts when is_list(accounts) ->
          Enum.each(accounts, fn account ->
            IO.puts("\nAccount Information:")
            IO.puts("Account Number: #{account["accountNumber"]}")

            if is_map_key(account, "accountName") do
              IO.puts("Account Name: #{account["accountName"]}")
            end

            if is_map_key(account, "accountType") do
              IO.puts("Account Type: #{account["accountType"]}")
            end

            if is_map_key(account, "currency") do
              IO.puts("Currency: #{account["currency"]}")
            end

            if is_map_key(account, "status") do
              IO.puts("Status: #{account["status"]}")
            end

            # Show customer details if available
            if is_map_key(account, "customerDetails") do
              customer = account["customerDetails"]
              IO.puts("\nCustomer Information:")
              if is_map_key(customer, "name"), do: IO.puts("Name: #{customer["name"]}")

              if is_map_key(customer, "phoneNumber"),
                do: IO.puts("Phone: #{customer["phoneNumber"]}")

              if is_map_key(customer, "email"), do: IO.puts("Email: #{customer["email"]}")
            end

            # Show balance information if available
            if is_map_key(account, "balances") do
              balances = account["balances"]
              IO.puts("\nBalance Information:")

              if is_map_key(balances, "availableBalance"),
                do: IO.puts("Available Balance: #{balances["availableBalance"]}")

              if is_map_key(balances, "currentBalance"),
                do: IO.puts("Current Balance: #{balances["currentBalance"]}")

              if is_map_key(balances, "workingBalance"),
                do: IO.puts("Working Balance: #{balances["workingBalance"]}")
            end

            # Show branch information if available
            if is_map_key(account, "branchDetails") do
              branch = account["branchDetails"]
              IO.puts("\nBranch Information:")

              if is_map_key(branch, "branchCode"),
                do: IO.puts("Branch Code: #{branch["branchCode"]}")

              if is_map_key(branch, "branchName"),
                do: IO.puts("Branch Name: #{branch["branchName"]}")
            end
          end)

          case get_in(req.response, ["body", "header"]) do
            header when is_map(header) ->
              IO.puts("\nResponse Information:")
              if is_map_key(header, "status"), do: IO.puts("Status: #{header["status"]}")
              if is_map_key(header, "message"), do: IO.puts("Message: #{header["message"]}")

              if is_map_key(header, "total_size"),
                do: IO.puts("Total Accounts: #{header["total_size"]}")

            _ ->
              :ok
          end

        _ ->
          IO.puts("\nNo account details found in response")
      end

    _ ->
      :ok
  end
end
