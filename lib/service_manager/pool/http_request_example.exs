Mix.install([
  {:httpoison, "~> 2.0"},
  {:jason, "~> 1.2"}
])

defmodule <PERSON>ttpExample do
  def run do
    request =
      """
      <?xml version="1.0"?>
      <post-payment-request>
      	<our-transaction-id>TT24254MFV2HI</our-transaction-id>
      	<account-number>BEDPVE</account-number>
      	<amount>100.00</amount>
      	<currency>MWK</currency>
      	<credit-account>*************</credit-account>
      	<credit-account-type>account</credit-account-type>
      	<debit-account>************</debit-account>
      	<debit-account-type>account</debit-account-type>
      	<customer-account-number>************</customer-account-number>
      	<customer-account-name><PERSON><PERSON></customer-account-name>
      </post-payment-request>
      """
      |> String.trim()

    response =
      HTTPoison.post(
        "https://fdh-esb.ngrok.dev/esb/api/bwb-postpaid-test/v1/transactions",
        request,
        [
          {"Content-Type", "text/xml"},
          {"Authorization", "Basic YWRtaW46YWRtaW4="}
        ],
        hackney: [:insecure],
        timeout: 50000,
        recv_timeout: 60000,
        ssl: [versions: [:"tlsv1.2"], verify: :verify_none]
      )

    case response do
      {:ok, %HTTPoison.Response{status_code: 200} = resp} ->
        IO.puts("Status: #{resp.status_code}")
        IO.puts("\nHeaders:")
        Enum.each(resp.headers, fn {k, v} -> IO.puts("#{k}: #{v}") end)
        IO.puts("\nBody:")
        IO.puts(resp.body)

      {:ok, %HTTPoison.Response{} = resp} ->
        IO.puts("Unexpected status code: #{resp.status_code}")
        IO.puts("Body: #{resp.body}")

      {:error, %HTTPoison.Error{reason: reason}} ->
        IO.puts("Error: #{inspect(reason)}")
    end
  end
end

HttpExample.run()
