# Test the request pool with a virtual account creation request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting virtual account creation request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "POST",
    url: "https://fdh-esb.ngrok.dev/api/esb/virtual/1.0/account/create/fdh/acc",
    headers: %{
      "Content-Type" => "application/json",
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    body: %{
      "header" => %{},
      "body" => %{
        "customerId" => "748719",
        "currency" => "MWK"
      }
    },
    name: "create_virtual_account",
    reference: "virtual_acc_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show the virtual account details in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nVirtual Account Creation Result:")

      case get_in(req.response, ["body", "body"]) do
        result when is_map(result) ->
          IO.puts("\nAccount Details:")
          IO.puts("Account ID: #{result["accountId"]}")
          IO.puts("Customer ID: #{result["customerId"]}")
          IO.puts("Currency: #{result["currency"]}")

          if is_map_key(result, "status") do
            IO.puts("Status: #{result["status"]}")
          end

          if is_map_key(result, "message") do
            IO.puts("Message: #{result["message"]}")
          end

          if is_map_key(result, "accountType") do
            IO.puts("Account Type: #{result["accountType"]}")
          end

          # Show virtual account specific details
          if is_map_key(result, "virtualAccountId") do
            IO.puts("\nVirtual Account Information:")
            IO.puts("Virtual Account ID: #{result["virtualAccountId"]}")
          end

          if is_map_key(result, "virtualAccountStatus") do
            IO.puts("Virtual Account Status: #{result["virtualAccountStatus"]}")
          end

          # Show customer details if available
          if is_map_key(result, "customerDetails") do
            customer = result["customerDetails"]
            IO.puts("\nCustomer Information:")
            if is_map_key(customer, "name"), do: IO.puts("Name: #{customer["name"]}")

            if is_map_key(customer, "phoneNumber"),
              do: IO.puts("Phone: #{customer["phoneNumber"]}")

            if is_map_key(customer, "email"), do: IO.puts("Email: #{customer["email"]}")
          end

          case get_in(req.response, ["body", "header"]) do
            header when is_map(header) ->
              IO.puts("\nHeader Information:")
              if is_map_key(header, "status"), do: IO.puts("Status: #{header["status"]}")
              if is_map_key(header, "message"), do: IO.puts("Message: #{header["message"]}")

              if is_map_key(header, "uniqueIdentifier"),
                do: IO.puts("Unique Identifier: #{header["uniqueIdentifier"]}")

            _ ->
              :ok
          end

        _ ->
          IO.puts("\nNo account details found in response")
      end

    _ ->
      :ok
  end
end
