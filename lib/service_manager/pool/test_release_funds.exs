# Test the request pool with a release reserved funds request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting release reserved funds request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "POST",
    url:
      "http://172.25.136.154:9090/releaseReservedFunds/api/v1.0.0/party/aclockedevents/release/reserved/funds/*************",
    headers: %{
      "Content-Type" => "application/json"
    },
    body: %{
      "header" => %{},
      "body" => %{
        "accountId" => "*************",
        "lockedAmount" => 100.00
      }
    },
    name: "release_reserved_funds",
    reference: "release_funds_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show the release funds result in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nRelease Reserved Funds Result:")

      case get_in(req.response, ["body", "body"]) do
        result when is_map(result) ->
          IO.puts("Account ID: #{result["accountId"]}")
          IO.puts("Released Amount: #{result["releasedAmount"]}")
          IO.puts("Status: #{result["status"]}")

          if is_map_key(result, "message") do
            IO.puts("Message: #{result["message"]}")
          end

          if is_map_key(result, "transactionId") do
            IO.puts("Transaction ID: #{result["transactionId"]}")
          end

          if is_map_key(result, "reservationId") do
            IO.puts("Reservation ID: #{result["reservationId"]}")
          end

        _ ->
          IO.puts("\nNo release funds result found in response")
      end

    _ ->
      :ok
  end
end
