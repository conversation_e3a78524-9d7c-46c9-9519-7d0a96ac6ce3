# Example usage of the Route Cache system

alias ServiceManager.Cache.RouteHelper

# Get full route details (raises on error)
route = RouteHelper.get_route_details("get_account_balance")
IO.puts("Route details: #{inspect(route, pretty: true)}")
# => %{
#      url: "172.25.136.154/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************",
#      method: "GET"
#    }

# Get just the URL (raises on error)
url = RouteHelper.get_url("create_account")
IO.puts("URL: #{url}")
# => "fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************"

# Get just the method (raises on error)
method = RouteHelper.get_method("create_account")
IO.puts("Method: #{method}")
# => "POST"

# Get complete URL with protocol (raises on error)
full_url = RouteHelper.build_url("get_account_balance")
IO.puts("Full URL: #{full_url}")
# => "http://172.25.136.154/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************"

# Safe versions that return tuples
case RouteHelper.get_route_details_safe("get_account_balance") do
  {:ok, details} ->
    IO.puts("Got details: #{inspect(details, pretty: true)}")

  {:error, :not_found} ->
    IO.puts("Route not found")
end

case RouteHelper.get_url_safe("non_existent") do
  {:ok, url} ->
    IO.puts("Got URL: #{url}")

  {:error, :not_found} ->
    IO.puts("Route not found")
end

# Example in a function
defmodule Example do
  def make_request(route_name, params \\ %{}) do
    route = RouteHelper.get_route_details(route_name)
    # Use the route info to make HTTP request
    IO.puts("Making #{route.method} request to #{route.url}")
    # ... rest of request logic
  end
end

Example.make_request("get_account_balance")
