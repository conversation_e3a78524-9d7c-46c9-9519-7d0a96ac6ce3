# Test the request pool with a Hello Paisa payment request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting Hello Paisa payment request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "POST",
    url: "https://fdh-esb.ngrok.dev/api/esb/paisa/1.0/payments/hellopaisa",
    headers: %{
      "Content-Type" => "application/json",
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    body: %{
      "header" => %{},
      "body" => %{
        "transactionType" => "AC",
        "debitAccount" => "*************",
        "debitAmount" => 100.00,
        "debitCurrency" => "MWK",
        "creditAccount" => "*************",
        "creditCurrency" => "MWK",
        "Amount" => "MWK100.00",
        "CURRENCY" => "MWK"
      }
    },
    name: "hello_paisa_payment",
    reference: "hello_paisa_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show the payment result in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nHello Paisa Payment Result:")

      case get_in(req.response, ["body", "body"]) do
        result when is_map(result) ->
          IO.puts("\nTransaction Details:")
          IO.puts("Transaction ID: #{result["transactionId"]}")
          IO.puts("Status: #{result["status"]}")

          if is_map_key(result, "message") do
            IO.puts("Message: #{result["message"]}")
          end

          if is_map_key(result, "processingDate") do
            IO.puts("Processing Date: #{result["processingDate"]}")
          end

          # Show debit details
          IO.puts("\nDebit Details:")
          IO.puts("Account: #{result["debitAccount"]}")
          IO.puts("Amount: #{result["debitAmount"]} #{result["debitCurrency"]}")

          # Show credit details
          IO.puts("\nCredit Details:")
          IO.puts("Account: #{result["creditAccount"]}")
          IO.puts("Currency: #{result["creditCurrency"]}")

          # Show payment specific details
          IO.puts("\nPayment Information:")
          IO.puts("Amount: #{result["Amount"]}")
          IO.puts("Currency: #{result["CURRENCY"]}")

          # Show Hello Paisa specific details
          if is_map_key(result, "paisaReference") do
            IO.puts("\nHello Paisa Information:")
            IO.puts("Reference: #{result["paisaReference"]}")
          end

          if is_map_key(result, "paisaStatus") do
            IO.puts("Status: #{result["paisaStatus"]}")
          end

          # Show fees if any
          if is_map_key(result, "fees") do
            IO.puts("\nFees:")

            Enum.each(result["fees"], fn fee ->
              IO.puts("#{fee["description"]}: #{fee["amount"]} #{fee["currency"]}")
            end)
          end

          case get_in(req.response, ["body", "header"]) do
            header when is_map(header) ->
              IO.puts("\nHeader Information:")
              if is_map_key(header, "status"), do: IO.puts("Status: #{header["status"]}")

              if is_map_key(header, "transactionStatus"),
                do: IO.puts("Transaction Status: #{header["transactionStatus"]}")

              if is_map_key(header, "uniqueIdentifier"),
                do: IO.puts("Unique Identifier: #{header["uniqueIdentifier"]}")

            _ ->
              :ok
          end

        _ ->
          IO.puts("\nNo payment details found in response")
      end

    _ ->
      :ok
  end
end
