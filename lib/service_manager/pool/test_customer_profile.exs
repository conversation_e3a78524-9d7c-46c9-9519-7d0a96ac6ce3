# Test the request pool with a customer profile request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting customer profile request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "GET",
    url: "https://fdh-esb.ngrok.dev/api/esb/customers/v1/customer/profile/1617214",
    headers: %{
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    # No body needed for GET request
    body: %{},
    name: "get_customer_profile",
    reference: "profile_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show customer profile in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nCustomer Profile:")

      case get_in(req.response, ["body", "body"]) do
        [profile | _] when is_map(profile) ->
          IO.puts("\nBasic Information:")
          IO.puts("Customer ID: #{profile["CUSTOMER"]}")
          IO.puts("Name: #{profile["holderName"]}")
          IO.puts("Status: #{profile["accountStatus"]}")

          case profile["customerDetails"] do
            [details | _] ->
              IO.puts("\nDetailed Information:")
              IO.puts("First Name: #{List.first(details["firstName"])}")
              IO.puts("Last Name: #{List.first(details["lastName"])}")
              IO.puts("Email: #{List.first(details["email"])}")
              IO.puts("Phone: #{List.first(details["phoneNumber"])}")
              IO.puts("Date of Birth: #{List.first(details["dateOfBirth"])}")

              IO.puts(
                "Address: #{List.first(details["street"])}, #{List.first(details["townCountry"])}"
              )

              IO.puts("Marital Status: #{List.first(details["maritalStatus"])}")
              IO.puts("Employment Status: #{List.first(details["employmentStatus"])}")

            _ ->
              :ok
          end

        _ ->
          IO.puts("\nNo profile details found in response")
      end

    _ ->
      :ok
  end
end
