# Test the request pool with a standing orders request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting standing orders request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "GET",
    url: "https://fdh-esb.ngrok.dev/api/esb/standing/1.0/orders/1010000003987.902",
    headers: %{
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    # No body needed for GET request
    body: %{},
    name: "get_standing_orders",
    reference: "standing_orders_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show the standing orders in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} when is_map_key(req.response, "body") ->
      IO.puts("\nStanding Orders:")

      case get_in(req.response, ["body", "body"]) do
        orders when is_list(orders) ->
          Enum.each(orders, fn order ->
            IO.puts("\nOrder Details:")
            IO.puts("Order ID: #{order["orderId"]}")
            IO.puts("Status: #{order["status"]}")

            if is_map_key(order, "frequency") do
              IO.puts("Frequency: #{order["frequency"]}")
            end

            if is_map_key(order, "startDate") do
              IO.puts("Start Date: #{order["startDate"]}")
            end

            if is_map_key(order, "endDate") do
              IO.puts("End Date: #{order["endDate"]}")
            end

            # Show debit details
            if is_map_key(order, "debitAccount") do
              IO.puts("\nDebit Account Details:")
              IO.puts("Account: #{order["debitAccount"]}")
              if is_map_key(order, "debitAmount"), do: IO.puts("Amount: #{order["debitAmount"]}")

              if is_map_key(order, "debitCurrency"),
                do: IO.puts("Currency: #{order["debitCurrency"]}")
            end

            # Show credit details
            if is_map_key(order, "creditAccount") do
              IO.puts("\nCredit Account Details:")
              IO.puts("Account: #{order["creditAccount"]}")

              if is_map_key(order, "creditAmount"),
                do: IO.puts("Amount: #{order["creditAmount"]}")

              if is_map_key(order, "creditCurrency"),
                do: IO.puts("Currency: #{order["creditCurrency"]}")
            end

            # Show next execution details
            if is_map_key(order, "nextExecutionDate") do
              IO.puts("\nNext Execution:")
              IO.puts("Date: #{order["nextExecutionDate"]}")
            end

            if is_map_key(order, "description") do
              IO.puts("\nDescription: #{order["description"]}")
            end
          end)

          # Show summary if available in header
          case get_in(req.response, ["body", "header"]) do
            header when is_map(header) ->
              IO.puts("\nSummary:")

              if is_map_key(header, "total_size"),
                do: IO.puts("Total Orders: #{header["total_size"]}")

              if is_map_key(header, "status"), do: IO.puts("Status: #{header["status"]}")

            _ ->
              :ok
          end

        _ ->
          IO.puts("\nNo standing orders found in response")
      end

    _ ->
      :ok
  end
end
