defmodule ServiceManager.Pool.FinchRequest do
  @moduledoc """
  Schema for storing and managing Finch HTTP requests.
  """

  use Ecto.Schema
  import Ecto.Changeset

  schema "finch_requests" do
    field :method, :string
    field :url, :string
    field :headers, :map, default: %{}
    field :body, :map, default: %{}
    field :options, :map, default: %{}
    field :status, :string, default: "pending"
    field :name, :string
    field :reference, :string
    field :response, :map, default: %{}

    timestamps()
  end

  @type t :: %__MODULE__{
          method: String.t(),
          url: String.t(),
          headers: map(),
          body: map(),
          options: keyword() | map(),
          status: String.t(),
          name: String.t() | nil,
          reference: String.t() | nil,
          response: map(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @doc """
  Creates a changeset for finch requests.
  """
  def changeset(finch_request, attrs) do
    finch_request
    |> cast(attrs, [
      :method,
      :url,
      :headers,
      :body,
      :options,
      :status,
      :name,
      :reference,
      :response
    ])
    |> validate_required([:method, :url])
    |> validate_inclusion(:method, ~w(GET POST PUT PATCH DELETE HEAD OPTIONS))
    |> validate_inclusion(:status, ~w(pending processing completed failed))
    |> ensure_json_body()
  end

  @doc """
  Builds a Finch.Request struct from the stored request data.

  ## Examples

      iex> build(request)
      %Finch.Request{}

  """
  @spec build(t()) :: Finch.Request.t()
  def build(%__MODULE__{} = request) do
    Finch.Request.build(
      request.method,
      request.url,
      map_to_header_list(request.headers),
      Jason.encode!(request.body),
      map_to_keyword_list(request.options)
    )
  end

  @doc """
  Creates a new FinchRequest from Finch.Request build parameters.

  ## Examples

      iex> from_params("POST", "https://api.example.com", [], %{data: "value"}, [],
        name: "create_user",
        reference: "user_123"
      )
      {:ok, %FinchRequest{}}

  """
  @spec from_params(
          Finch.Request.method(),
          Finch.Request.url(),
          Finch.Request.headers(),
          map() | String.t() | nil,
          Keyword.t(),
          Keyword.t()
        ) :: {:ok, t()} | {:error, Ecto.Changeset.t()}
  def from_params(method, url, headers, body, options, meta \\ []) do
    %__MODULE__{}
    |> changeset(%{
      method: to_string(method),
      url: url,
      headers: headers_to_map(headers),
      body: normalize_body(body),
      options: Enum.into(options, %{}),
      name: meta[:name],
      reference: meta[:reference]
    })
    |> apply_action(:insert)
  end

  @doc """
  Updates the request with a response.

  ## Examples

      iex> update_response(request, %{status: 200, body: "..."})
      {:ok, %FinchRequest{}}

  """
  @spec update_response(t(), map()) :: {:ok, t()} | {:error, Ecto.Changeset.t()}
  def update_response(request, response) do
    request
    |> changeset(%{
      status: "completed",
      response: response
    })
    |> apply_action(:update)
  end

  # Private functions

  defp headers_to_map(headers) when is_list(headers) do
    headers
    |> Enum.map(fn {k, v} -> {to_string(k), to_string(v)} end)
    |> Enum.into(%{})
  end

  defp headers_to_map(nil), do: %{}

  defp map_to_header_list(headers) when is_map(headers) do
    Enum.map(headers, fn {k, v} -> {to_string(k), to_string(v)} end)
  end

  defp map_to_keyword_list(map) when is_map(map) do
    map
    |> Enum.map(fn {k, v} -> {String.to_existing_atom(to_string(k)), v} end)
    |> Keyword.new()
  rescue
    _ -> []
  end

  defp map_to_keyword_list(_), do: []

  defp normalize_body(body) when is_binary(body) do
    case Jason.decode(body) do
      {:ok, decoded} -> decoded
      {:error, _} -> %{"raw" => body}
    end
  end

  defp normalize_body(body) when is_map(body), do: body
  defp normalize_body(nil), do: %{}

  defp ensure_json_body(changeset) do
    case get_change(changeset, :body) do
      nil ->
        changeset

      body when is_map(body) ->
        changeset

      body when is_binary(body) ->
        case Jason.decode(body) do
          {:ok, decoded} -> put_change(changeset, :body, decoded)
          {:error, _} -> put_change(changeset, :body, %{"raw" => body})
        end

      other ->
        put_change(changeset, :body, %{"raw" => inspect(other)})
    end
  end
end
