# Test the request pool with an account details request
alias ServiceManager.Pool.RequestPool
require Logger

Logger.configure(level: :info)
Logger.info("Starting account details request test")

# Create and enqueue the request
{:ok, request} =
  RequestPool.create_request(%{
    method: "GET",
    url: "https://fdh-esb.ngrok.dev/api/esb/accounts/1.0/account/*************",
    headers: %{
      "Authorization" => "Basic YWRtaW46YWRtaW4="
    },
    # No body needed for GET request
    body: %{},
    name: "get_account_details",
    reference: "account_#{:os.system_time(:millisecond)}"
  })

Logger.info("Created request: #{request.id}")

# Function to check request status
check_request = fn request_id ->
  case RequestPool.get_request(request_id) do
    {:ok, req} ->
      status_info = """

      Request Status Update:
      ID: #{req.id}
      Name: #{req.name}
      Status: #{req.status}
      #{if req.status in ["completed", "failed"], do: "Response: #{inspect(req.response, pretty: true, limit: :infinity)}", else: ""}
      """

      IO.puts(status_info)
      Logger.info(status_info)
      req.status

    {:error, :not_found} ->
      message = "\nRequest not found!"
      IO.puts(message)
      Logger.error(message)
      :not_found
  end
end

# Wait and check status periodically
# 60 seconds total
max_attempts = 30
# 2 seconds
check_interval = 2000

Logger.info(
  "Waiting for request to complete (max #{max_attempts * check_interval / 1000} seconds)"
)

final_status =
  Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
    status = check_request.(request.id)

    cond do
      status in ["completed", "failed"] ->
        Logger.info("Request finished with status: #{status}")
        {:halt, status}

      attempt == max_attempts ->
        message = "\nTimeout waiting for request to complete"
        IO.puts(message)
        Logger.error(message)
        {:halt, :timeout}

      true ->
        Logger.debug("Attempt #{attempt}/#{max_attempts}, status: #{status}")
        Process.sleep(check_interval)
        {:cont, nil}
    end
  end)

# Final status report
status_message = "\nTest completed with final status: #{final_status}"
IO.puts(status_message)
Logger.info(status_message)

# If successful, show account details in a more readable format
if final_status == "completed" do
  case RequestPool.get_request(request.id) do
    {:ok, req} ->
      case get_in(req.response, ["body", "body"]) do
        [account | _] when is_map(account) ->
          IO.puts("\nAccount Details:")
          IO.puts("Holder Name: #{account["holderName"]}")
          IO.puts("Account Status: #{account["accountStatus"]}")
          IO.puts("Category: #{account["categoryName"]}")
          IO.puts("Opening Date: #{account["openingDate"]}")
          IO.puts("Online Limit: #{account["onlineLimit"]}")

          case account["accountDetails"] do
            [details | _] ->
              IO.puts("\nAccount Information:")
              IO.puts("Account ID: #{List.first(details["accountID"])}")
              IO.puts("Currency: #{List.first(details["accountCurrency"])}")

            _ ->
              :ok
          end

          case account["customerDetails"] do
            [customer | _] ->
              IO.puts("\nCustomer Information:")

              IO.puts(
                "Name: #{List.first(customer["firstName"])} #{List.first(customer["lastName"])}"
              )

              IO.puts("Email: #{List.first(customer["email"])}")
              IO.puts("Phone: #{List.first(customer["phoneNumber"])}")
              IO.puts("Date of Birth: #{List.first(customer["dateOfBirth"])}")

              IO.puts(
                "Address: #{List.first(customer["street"])}, #{List.first(customer["townCountry"])}"
              )

            _ ->
              :ok
          end

        _ ->
          IO.puts("\nNo account details found in response")
      end

    _ ->
      :ok
  end
end
