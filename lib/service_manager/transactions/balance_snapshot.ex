defmodule ServiceManager.Transactions.BalanceSnapshot do
  @moduledoc """
  Schema for balance snapshots.
  Records account balances at specific points in time, typically before and after transactions.
  """
  use Ecto.Schema
  import Ecto.Changeset

  schema "balance_snapshots" do
    field :account_id, :string
    field :account_type, :string
    field :balance, :decimal
    field :transaction_id, :string
    field :snapshot_type, :string
    field :metadata, :map, default: %{}

    timestamps()
  end

  @doc """
  Changeset for balance snapshots.
  """
  def changeset(balance_snapshot, attrs) do
    balance_snapshot
    |> cast(attrs, [:account_id, :account_type, :balance, :transaction_id, :snapshot_type, :metadata])
    |> validate_required([:account_id, :account_type, :balance, :transaction_id, :snapshot_type])
    |> validate_inclusion(:account_type, ["wallet", "bank"])
    |> validate_inclusion(:snapshot_type, ["pre_transaction", "post_transaction"])
  end
end
