defmodule ServiceManager.Transactions.WalletReversal do
  @moduledoc """
  Module for handling wallet transaction reversals.

  This module provides functionality to reverse wallet transactions, including:
  - Creating reversal transactions
  - Updating wallet balances
  - Recording transaction status history
  - Creating balance snapshots
  """
  alias ServiceManager.Repo
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.Transactions.WalletTransactions
  alias ServiceManager.Services.{BalanceSnapshotService, TransactionStatusHistoryService}
  alias ServiceManager.Broadcasters.LoggerBroadcaster, as: Logger
  alias ServiceManager.Contexts.WalletTransactionsContext
  import Ecto.Query

  @doc """
  Reverses a wallet transaction.

  Creates a new wallet transaction that reverses the effects of the original transaction,
  updates the status of the original transaction to "reversed", and adjusts wallet
  balances accordingly.

  ## Parameters
    - original_transaction: The wallet transaction to reverse
    - reversal_reason: The reason for the reversal
    - reversal_params: Additional parameters for the reversal transaction (optional)

  ## Returns
    - {:ok, reversal_transaction} on success
    - {:error, reason} on failure
  """
  def reverse_transaction(original_transaction, reversal_reason, reversal_params \\ %{}) do
    Repo.transaction(fn ->
      # Create pre-reversal balance snapshots
      create_pre_reversal_snapshots(original_transaction)

      with {:ok, reversal} <- create_reversal_transaction(original_transaction, reversal_reason, reversal_params),
           {:ok, _} <- update_original_transaction_status(original_transaction, reversal),
           {:ok, _} <- adjust_wallet_balances(original_transaction, reversal) do

        # Create post-reversal balance snapshots
        create_post_reversal_snapshots(original_transaction, reversal)

        # Record initial status for the reversal transaction
        WalletTransactions.record_initial_status(reversal)

        {:ok, reversal}
      else
        {:error, error} ->
          Logger.error("Wallet transaction reversal failed: #{inspect(error)}")
          Repo.rollback(error)
      end
    end)
  end

  @doc """
  Creates a reversal transaction based on the original wallet transaction.

  ## Parameters
    - original_transaction: The wallet transaction to reverse
    - reversal_reason: The reason for the reversal
    - reversal_params: Additional parameters for the reversal transaction

  ## Returns
    - {:ok, reversal_transaction} on success
    - {:error, changeset} on failure
  """
  defp create_reversal_transaction(original_transaction, reversal_reason, reversal_params) do
    # Get current wallet balances for opening/closing balances
    from_wallet_balance = if original_transaction.to_account_id do
      case WalletTransactionsContext.get_wallet(original_transaction.to_account_id) do
        nil -> Decimal.new(0)
        wallet -> wallet.balance
      end
    else
      Decimal.new(0)
    end

    to_wallet_balance = if original_transaction.from_account_id do
      case WalletTransactionsContext.get_wallet(original_transaction.from_account_id) do
        nil -> Decimal.new(0)
        wallet -> wallet.balance
      end
    else
      Decimal.new(0)
    end

    # Calculate new balances after reversal
    new_from_wallet_balance = Decimal.sub(from_wallet_balance, original_transaction.amount)
    new_to_wallet_balance = Decimal.add(to_wallet_balance, original_transaction.amount)

    # Base reversal attributes
    reversal_attrs = %{
      # Now we can use "reversal" as the type since we added it to the allowed list
      type: "reversal",
      # Use positive amount for validation, but swap debit/credit
      amount: Decimal.abs(original_transaction.amount),
      credit_amount: original_transaction.debit_amount,
      debit_amount: original_transaction.credit_amount,
      description: "Reversal: #{reversal_reason}. Original reference: #{original_transaction.reference}",
      status: "completed",
      reference: "REV-#{original_transaction.reference}",
      value_date: Date.utc_today(),
      # Swap from and to accounts/wallets
      from_account_id: original_transaction.to_account_id,
      to_account_id: original_transaction.from_account_id,
      from_wallet_id: original_transaction.to_wallet_id,
      to_wallet_id: original_transaction.from_wallet_id,
      sender_account: original_transaction.receiver_account,
      receiver_account: original_transaction.sender_account,
      # Add opening and closing balances
      opening_balance: from_wallet_balance,
      closing_balance: new_from_wallet_balance,
      # Mark as reversal and link to original
      is_reversal: true,
      original_transaction_id: original_transaction.id
    }

    # Merge with any additional params
    merged_attrs = Map.merge(reversal_attrs, reversal_params)

    # Create the reversal transaction
    %WalletTransactions{}
    |> WalletTransactions.changeset(merged_attrs)
    |> Repo.insert()
  end

  @doc """
  Updates the status of the original wallet transaction to "reversed".

  ## Parameters
    - original_transaction: The wallet transaction being reversed
    - reversal_transaction: The reversal transaction

  ## Returns
    - {:ok, updated_transaction} on success
    - {:error, changeset} on failure
  """
  defp update_original_transaction_status(original_transaction, reversal_transaction) do
    original_transaction
    |> WalletTransactions.changeset(%{
      status: "reversed"
    })
    |> Repo.update()
  end

  @doc """
  Adjusts wallet balances based on the reversal.

  ## Parameters
    - original_transaction: The wallet transaction being reversed
    - reversal_transaction: The reversal transaction

  ## Returns
    - {:ok, :balances_updated} on success
    - {:error, reason} on failure
  """
  defp adjust_wallet_balances(original_transaction, reversal_transaction) do
    try do
      # Adjust from_account balances (original recipient)
      if original_transaction.to_account_id do
        from_wallet = WalletTransactionsContext.get_wallet_with_lock(original_transaction.to_account_id)

        if from_wallet do
          # Subtract the original amount (not the reversal amount which is positive)
          new_balance = Decimal.sub(from_wallet.balance, original_transaction.amount)

          from_wallet
          |> WalletUser.update_balance(%{balance: new_balance})
          |> Repo.update!()
        end
      end

      # Adjust to_account balances (original sender)
      if original_transaction.from_account_id do
        to_wallet = WalletTransactionsContext.get_wallet_with_lock(original_transaction.from_account_id)

        if to_wallet do
          # Add the original amount (not the reversal amount which is positive)
          new_balance = Decimal.add(to_wallet.balance, original_transaction.amount)

          to_wallet
          |> WalletUser.update_balance(%{balance: new_balance})
          |> Repo.update!()
        end
      end

      {:ok, :balances_updated}
    rescue
      e ->
        Logger.error("Failed to adjust wallet balances: #{inspect(e)}")
        {:error, "Failed to adjust wallet balances: #{inspect(e)}"}
    end
  end

  @doc """
  Creates pre-reversal balance snapshots.

  ## Parameters
    - original_transaction: The wallet transaction being reversed
  """
  defp create_pre_reversal_snapshots(original_transaction) do
    # Create snapshot for from_account if it exists (original recipient)
    if original_transaction.to_account_id do
      BalanceSnapshotService.create_pre_wallet_transaction_snapshot(
        original_transaction.to_account_id,
        original_transaction.id,
        "pre_wallet_reversal"
      )
    end

    # Create snapshot for to_account if it exists (original sender)
    if original_transaction.from_account_id do
      BalanceSnapshotService.create_pre_wallet_transaction_snapshot(
        original_transaction.from_account_id,
        original_transaction.id,
        "pre_wallet_reversal"
      )
    end
  end

  @doc """
  Creates post-reversal balance snapshots.

  ## Parameters
    - original_transaction: The wallet transaction being reversed
    - reversal_transaction: The reversal transaction
  """
  defp create_post_reversal_snapshots(original_transaction, reversal_transaction) do
    # Create snapshot for from_account if it exists (original recipient)
    if original_transaction.to_account_id do
      BalanceSnapshotService.create_post_wallet_transaction_snapshot(
        original_transaction.to_account_id,
        reversal_transaction.id,
        "post_wallet_reversal"
      )
    end

    # Create snapshot for to_account if it exists (original sender)
    if original_transaction.from_account_id do
      BalanceSnapshotService.create_post_wallet_transaction_snapshot(
        original_transaction.from_account_id,
        reversal_transaction.id,
        "post_wallet_reversal"
      )
    end
  end


end
