defmodule ServiceManager.Transactions.Reversal do
  @moduledoc """
  Module for handling transaction reversals.

  This module provides functionality to reverse transactions, including:
  - Creating reversal transactions
  - Updating account balances
  - Recording transaction status history
  - Creating balance snapshots
  """
  alias ServiceManager.Repo
  alias ServiceManager.Accounts.FundAccounts
  alias ServiceManager.Transactions.Transaction
  alias ServiceManager.Schemas.Accounting.AccountingEntry
  alias ServiceManager.Services.{BalanceSnapshotService, TransactionStatusHistoryService}
  alias ServiceManager.Broadcasters.LoggerBroadcaster, as: Logger
  import Ecto.Query

  @doc """
  Reverses a transaction.

  Creates a new transaction that reverses the effects of the original transaction,
  updates the status of the original transaction to "reversed", and adjusts account
  balances accordingly.

  ## Parameters
    - original_transaction: The transaction to reverse
    - reversal_reason: The reason for the reversal
    - reversal_params: Additional parameters for the reversal transaction (optional)

  ## Returns
    - {:ok, reversal_transaction} on success
    - {:error, reason} on failure
  """
  def reverse_transaction(original_transaction, reversal_reason, reversal_params \\ %{}) do
    Repo.transaction(fn ->
      # Create pre-reversal balance snapshots
      create_pre_reversal_snapshots(original_transaction)

      with {:ok, reversal} <- create_reversal_transaction(original_transaction, reversal_reason, reversal_params),
           {:ok, _} <- update_original_transaction_status(original_transaction, reversal),
           {:ok, _} <- create_reversal_accounting_entries(original_transaction, reversal),
           {:ok, _} <- adjust_account_balances(original_transaction, reversal) do

        # Create post-reversal balance snapshots
        create_post_reversal_snapshots(original_transaction, reversal)

        # Record initial status for the reversal transaction
        Transaction.record_initial_status(reversal)

        {:ok, reversal}
      else
        {:error, error} ->
          Logger.error("Transaction reversal failed: #{inspect(error)}")
          Repo.rollback(error)
      end
    end)
  end

  @doc """
  Creates a reversal transaction based on the original transaction.

  ## Parameters
    - original_transaction: The transaction to reverse
    - reversal_reason: The reason for the reversal
    - reversal_params: Additional parameters for the reversal transaction

  ## Returns
    - {:ok, reversal_transaction} on success
    - {:error, changeset} on failure
  """
  defp create_reversal_transaction(original_transaction, reversal_reason, reversal_params) do
    # Get current account balances for opening/closing balances
    from_account_balance = if original_transaction.to_account_id do
      case Repo.get(FundAccounts, original_transaction.to_account_id) do
        nil -> Decimal.new(0)
        account -> account.balance
      end
    else
      Decimal.new(0)
    end

    to_account_balance = if original_transaction.from_account_id do
      case Repo.get(FundAccounts, original_transaction.from_account_id) do
        nil -> Decimal.new(0)
        account -> account.balance
      end
    else
      Decimal.new(0)
    end

    # Calculate new balances after reversal
    new_from_account_balance = Decimal.sub(from_account_balance, original_transaction.amount)
    new_to_account_balance = Decimal.add(to_account_balance, original_transaction.amount)

    # Base reversal attributes
    reversal_attrs = %{
      # Now we can use "reversal" as the type since we added it to the allowed list
      type: "reversal",
      # Use positive amount for validation, but swap debit/credit
      amount: Decimal.abs(original_transaction.amount),
      credit_amount: original_transaction.debit_amount,
      debit_amount: original_transaction.credit_amount,
      description: "Reversal: #{reversal_reason}. Original reference: #{original_transaction.reference}",
      status: "completed",
      reference: "REV-#{original_transaction.reference}",
      value_date: Date.utc_today(),
      # Swap from and to accounts/wallets
      from_account_id: original_transaction.to_account_id,
      to_account_id: original_transaction.from_account_id,
      from_wallet_id: original_transaction.to_wallet_id,
      to_wallet_id: original_transaction.from_wallet_id,
      sender_account: original_transaction.receiver_account,
      receiver_account: original_transaction.sender_account,
      # Add opening and closing balances
      opening_balance: from_account_balance,
      closing_balance: new_from_account_balance,
      # Mark as reversal and link to original
      is_reversal: true,
      original_transaction_id: original_transaction.id,
      # Include transaction details with reversal info
      transaction_details: Map.merge(
        original_transaction.transaction_details || %{},
        %{
          "reversal_reason" => reversal_reason,
          "reversed_at" => DateTime.utc_now() |> DateTime.to_iso8601(),
          "original_transaction_reference" => original_transaction.reference
        }
      )
    }

    # Merge with any additional params
    merged_attrs = Map.merge(reversal_attrs, reversal_params)

    # Create the reversal transaction
    %Transaction{}
    |> Transaction.changeset(merged_attrs)
    |> Repo.insert()
  end

  @doc """
  Updates the status of the original transaction to "reversed".

  ## Parameters
    - original_transaction: The transaction being reversed
    - reversal_transaction: The reversal transaction

  ## Returns
    - {:ok, updated_transaction} on success
    - {:error, changeset} on failure
  """
  defp update_original_transaction_status(original_transaction, reversal_transaction) do
    original_transaction
    |> Transaction.changeset(%{
      status: "reversed",
      transaction_details: Map.merge(
        original_transaction.transaction_details || %{},
        %{
          "reversed_by_transaction_id" => reversal_transaction.id,
          "reversed_at" => DateTime.utc_now() |> DateTime.to_iso8601()
        }
      )
    })
    |> Repo.update()
  end

  @doc """
  Creates accounting entries for the reversal transaction.

  ## Parameters
    - original_transaction: The transaction being reversed
    - reversal_transaction: The reversal transaction

  ## Returns
    - {:ok, entries} on success
    - {:error, reason} on failure
  """
  defp create_reversal_accounting_entries(original_transaction, reversal_transaction) do
    # Fetch original accounting entries
    original_entries = Repo.all(
      from e in AccountingEntry,
      where: e.transaction_id == ^original_transaction.id
    )

    if Enum.empty?(original_entries) do
      # No accounting entries to reverse
      {:ok, []}
    else
      # Create reversing entries
      entries = Enum.map(original_entries, fn original_entry ->
        reversal_entry_attrs = %{
          entry_type: if(original_entry.entry_type == "debit", do: "credit", else: "debit"),
          amount: original_entry.amount,
          description: "Reversal for #{original_entry.description}",
          transaction_date: DateTime.utc_now(),
          value_date: Date.utc_today(),
          reference: "REV-#{original_entry.reference}",
          status: "posted",
          currency: original_entry.currency,
          ledger_id: original_entry.ledger_id,
          transaction_id: reversal_transaction.id,
          metadata: Map.merge(
            original_entry.metadata || %{},
            %{
              "original_entry_id" => original_entry.id,
              "is_reversal" => true
            }
          )
        }

        %AccountingEntry{}
        |> AccountingEntry.changeset(reversal_entry_attrs)
        |> Repo.insert!()
      end)

      {:ok, entries}
    end
  end

  @doc """
  Adjusts account balances based on the reversal.

  ## Parameters
    - original_transaction: The transaction being reversed
    - reversal_transaction: The reversal transaction

  ## Returns
    - {:ok, :balances_updated} on success
    - {:error, reason} on failure
  """
  defp adjust_account_balances(original_transaction, reversal_transaction) do
    # Determine which balances to adjust based on original transaction status
    balances_to_adjust = case original_transaction.status do
      "pending" -> [:working_balance]
      "completed" -> [:balance, :working_balance, :cleared_balance]
      "processing" -> [:working_balance]
      _ -> []
    end

    if Enum.empty?(balances_to_adjust) do
      # No balances to adjust
      {:ok, :no_balances_to_adjust}
    else
      try do
        # Adjust from_account balances (original recipient)
        if original_transaction.to_account_id do
          from_account = Repo.get!(FundAccounts, original_transaction.to_account_id)
          from_updates = Enum.reduce(balances_to_adjust, %{}, fn balance_type, acc ->
            current_value = Map.get(from_account, balance_type) || Decimal.new(0)
            # Subtract the original amount (not the reversal amount which is positive)
            Map.put(acc, balance_type, Decimal.sub(current_value, original_transaction.amount))
          end)

          from_account
          |> FundAccounts.changeset(from_updates)
          |> Repo.update!()
        end

        # Adjust to_account balances (original sender)
        if original_transaction.from_account_id do
          to_account = Repo.get!(FundAccounts, original_transaction.from_account_id)
          to_updates = Enum.reduce(balances_to_adjust, %{}, fn balance_type, acc ->
            current_value = Map.get(to_account, balance_type) || Decimal.new(0)
            # Add the original amount (not the reversal amount which is positive)
            Map.put(acc, balance_type, Decimal.add(current_value, original_transaction.amount))
          end)

          to_account
          |> FundAccounts.changeset(to_updates)
          |> Repo.update!()
        end

        {:ok, :balances_updated}
      rescue
        e ->
          Logger.error("Failed to adjust account balances: #{inspect(e)}")
          {:error, "Failed to adjust account balances: #{inspect(e)}"}
      end
    end
  end

  @doc """
  Creates pre-reversal balance snapshots.

  ## Parameters
    - original_transaction: The transaction being reversed
  """
  defp create_pre_reversal_snapshots(original_transaction) do
    # Create snapshot for from_account if it exists (original recipient)
    if original_transaction.to_account_id do
      BalanceSnapshotService.create_pre_transaction_snapshot(
        original_transaction.to_account_id,
        original_transaction.id,
        "pre_reversal"
      )
    end

    # Create snapshot for to_account if it exists (original sender)
    if original_transaction.from_account_id do
      BalanceSnapshotService.create_pre_transaction_snapshot(
        original_transaction.from_account_id,
        original_transaction.id,
        "pre_reversal"
      )
    end

    # Create snapshot for from_wallet if it exists (original recipient wallet)
    if original_transaction.to_wallet_id do
      BalanceSnapshotService.create_pre_wallet_transaction_snapshot(
        original_transaction.to_wallet_id,
        original_transaction.id,
        "pre_reversal"
      )
    end

    # Create snapshot for to_wallet if it exists (original sender wallet)
    if original_transaction.from_wallet_id do
      BalanceSnapshotService.create_pre_wallet_transaction_snapshot(
        original_transaction.from_wallet_id,
        original_transaction.id,
        "pre_reversal"
      )
    end
  end

  @doc """
  Creates post-reversal balance snapshots.

  ## Parameters
    - original_transaction: The transaction being reversed
    - reversal_transaction: The reversal transaction
  """
  defp create_post_reversal_snapshots(original_transaction, reversal_transaction) do
    # Create snapshot for from_account if it exists (original recipient)
    if original_transaction.to_account_id do
      BalanceSnapshotService.create_post_transaction_snapshot(
        original_transaction.to_account_id,
        reversal_transaction.id,
        "post_reversal"
      )
    end

    # Create snapshot for to_account if it exists (original sender)
    if original_transaction.from_account_id do
      BalanceSnapshotService.create_post_transaction_snapshot(
        original_transaction.from_account_id,
        reversal_transaction.id,
        "post_reversal"
      )
    end

    # Create snapshot for from_wallet if it exists (original recipient wallet)
    if original_transaction.to_wallet_id do
      BalanceSnapshotService.create_post_wallet_transaction_snapshot(
        original_transaction.to_wallet_id,
        reversal_transaction.id,
        "post_reversal"
      )
    end

    # Create snapshot for to_wallet if it exists (original sender wallet)
    if original_transaction.from_wallet_id do
      BalanceSnapshotService.create_post_wallet_transaction_snapshot(
        original_transaction.from_wallet_id,
        reversal_transaction.id,
        "post_reversal"
      )
    end
  end


end
