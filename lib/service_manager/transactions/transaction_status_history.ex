
defmodule ServiceManager.Transactions.TransactionStatusHistory do
  @moduledoc """
  Schema for transaction status history.
  Tracks changes in transaction status over time.
  """
  use Ecto.Schema
  import Ecto.Changeset

  schema "transaction_status_history" do
    field :transaction_id, :string
    field :from_status, :string
    field :to_status, :string
    field :changed_by, :string
    field :notes, :string
    field :metadata, :map, default: %{}

    timestamps()
  end

  @doc """
  Changeset for transaction status history.
  """
  def changeset(transaction_status_history, attrs) do
    transaction_status_history
    |> cast(attrs, [:transaction_id, :from_status, :to_status, :changed_by, :notes, :metadata])
    |> validate_required([:transaction_id, :from_status, :to_status])
  end
end
