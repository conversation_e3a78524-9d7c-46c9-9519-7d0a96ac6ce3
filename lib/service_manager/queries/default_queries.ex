defmodule ServiceManager.Queries.DefaultQueries do
  @moduledoc """
  Provides common query functions for filtering, sorting, and paginating Ecto queries.
  These functions can be composed together to build complex queries with consistent behavior.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo

  # List of allowed status values for status filtering
  @allowed_status ~w(ACTIVE INACTIVE inactive pending PENDING active)s

  @doc """
  Filters query by status, only allowing records with status in @allowed_status.
  """
  def status_query(query) do
    query
    |> where([a], a.status in @allowed_status)
  end

  @doc """
  Filters query by status. Returns unmodified query if status is nil or empty.
  """
  def status_query(query, %{"status" => status}) when is_nil(status) or status == "", do: query

  def status_query(query, %{"status" => status}) do
    query
    |> where([a], a.status == ^status)
  end

  def status_query(query, _), do: query

  @doc """
  Filters query by group_id. Returns unmodified query if group_id is nil or empty.
  """
  def group_id_query(query, %{"group_id" => group_id}) when is_nil(group_id) or group_id == "",
    do: query

  def group_id_query(query, %{"group_id" => group_id}) do
    query
    |> where([a], a.group_id == type(^group_id, :integer))
  end

  @doc """
  Applies sorting to query based on provided field and direction.
  Accepts both atom and string keys in params map.
  Falls back to sorting by inserted_at desc if no sort params provided.
  """
  def sorting_query(query, params) do
    case params do
      %{sort_field: field, sort_direction: direction} ->
        apply_sort(query, field, direction)

      %{"sort_field" => field, "sort_order" => direction} ->
        apply_sort(query, field, direction)

      _ ->
        query |> order_by([a], desc: :inserted_at)
    end
  end

  @doc """
  Applies pagination to query using Repo.paginate/2.
  Accepts both atom and string keys in params map.
  Uses default pagination if no page params provided.
  """
  def pagination_query(query, params) do
    case params do
      %{page: page, page_size: page_size} ->
        Repo.paginate(query, page: page, page_size: page_size)

      %{"page" => page, "page_size" => page_size} ->
        Repo.paginate(query, page: page, page_size: page_size)

      _ ->
        Repo.paginate(query)
    end
  end

  @doc """
  Filters query by amount range.
  Returns unmodified query if amounts are invalid or missing.
  """
  def amount_range_query(query, %{"amount_from" => amount_from, "amount_to" => amount_to})
      when is_nil(amount_from) or is_nil(amount_to) or amount_from == "" or amount_to == "",
      do: query

  def amount_range_query(query, %{"amount_from" => amount_from, "amount_to" => amount_to}) do
    case {Decimal.parse(amount_from), Decimal.parse(amount_to)} do
      {{:ok, from_decimal}, {:ok, to_decimal}} ->
        query
        |> where([a], a.amount >= ^from_decimal and a.amount <= ^to_decimal)

      _ ->
        query
    end
  end

  def amount_range_query(query, _), do: query

  @doc """
  Filters query by transaction type.
  Returns unmodified query if type is invalid or missing.
  """
  def type_query(query, %{"type" => type}) when is_nil(type) or type == "", do: query

  def type_query(query, %{"type" => type}) do
    query |> where([a], a.type == ^type)
  end

  def type_query(query, _), do: query

  @doc """
  Filters query by transaction reference.
  Returns unmodified query if reference is invalid or missing.
  """
  def reference_query(query, %{"reference" => reference})
      when is_nil(reference) or reference == "",
      do: query

  def reference_query(query, %{"reference" => reference}) do
    query |> where([a], ilike(a.reference, ^"%#{reference}%"))
  end

  def reference_query(query, _), do: query

  @doc """
  Filters query by date range using inserted_at field.
  Accepts start_date/end_date or from_date/to_date params.
  Returns unmodified query if dates are invalid or missing.
  """
  def date_range_query(query, params) do
    case params do
      %{"start_date" => start_date, "end_date" => end_date} ->
        process_date_range_if_valid(query, start_date, end_date)

      %{"from_date" => start_date, "to_date" => end_date} ->
        process_date_range_if_valid(query, start_date, end_date)

      _ ->
        query
    end
  end

  @doc """
  Filters query by account numbers (from_account and to_account).
  Returns unmodified query if account numbers are invalid or missing.
  """
  def from_account_query(query, %{"from_account" => account})
      when is_nil(account) or account == "",
      do: query

  def from_account_query(query, %{"from_account" => account}) do
    query |> where([a], a.from_account == ^account)
  end

  def from_account_query(query, _), do: query

  def to_account_query(query, %{"to_account" => account}) when is_nil(account) or account == "",
    do: query

  def to_account_query(query, %{"to_account" => account}) do
    query |> where([a], a.to_account == ^account)
  end

  def to_account_query(query, _), do: query

  @doc """
  Filters query by user fields.
  Returns unmodified query if fields are invalid or missing.
  """
  def email_query(query, %{"email" => email}) when is_nil(email) or email == "", do: query

  def email_query(query, %{"email" => email}) do
    query |> where([u], ilike(u.email, ^"%#{email}%"))
  end

  def email_query(query, _), do: query

  def name_query(query, %{"name" => name}) when is_nil(name) or name == "", do: query

  def name_query(query, %{"name" => name}) do
    query |> where([u], ilike(u.name, ^"%#{name}%"))
  end

  def name_query(query, _), do: query

  def nickname_query(query, %{"nickname" => nickname}) when is_nil(nickname) or nickname == "",
    do: query

  def nickname_query(query, %{"nickname" => nickname}) do
    query |> where([u], ilike(u.nickname, ^"%#{nickname}%"))
  end

  def nickname_query(query, _), do: query

  def first_name_query(query, %{"first_name" => first_name})
      when is_nil(first_name) or first_name == "",
      do: query

  def first_name_query(query, %{"first_name" => first_name}) do
    query |> where([u], ilike(u.first_name, ^"%#{first_name}%"))
  end

  def first_name_query(query, _), do: query

  def last_name_query(query, %{"last_name" => last_name})
      when is_nil(last_name) or last_name == "",
      do: query

  def last_name_query(query, %{"last_name" => last_name}) do
    query |> where([u], ilike(u.last_name, ^"%#{last_name}%"))
  end

  def last_name_query(query, _), do: query

  def phone_number_query(query, %{"phone_number" => phone}) when is_nil(phone) or phone == "",
    do: query

  def phone_number_query(query, %{"phone_number" => phone}) do
    query |> where([u], ilike(u.phone_number, ^"%#{phone}%"))
  end

  def phone_number_query(query, _), do: query

  def approved_query(query, %{"approved" => approved}) when is_nil(approved) or approved == "",
    do: query

  def approved_query(query, %{"approved" => approved}) do
    query |> where([u], u.approved == ^approved)
  end

  def approved_query(query, _), do: query

  @doc """
  Filters query by bank account fields.
  Returns unmodified query if fields are invalid or missing.
  """
  def tag_query(query, %{"tag" => tag}) when is_nil(tag) or tag == "", do: query

  def tag_query(query, %{"tag" => tag}) do
    query |> where([u, b], ilike(b.tag, ^"%#{tag}%"))
  end

  def tag_query(query, _), do: query

  def account_name_query(query, %{"account_name" => name}) when is_nil(name) or name == "",
    do: query

  def account_name_query(query, %{"account_name" => name}) do
    query |> where([u, b], ilike(b.name, ^"%#{name}%"))
  end

  def account_name_query(query, _), do: query

  def account_number_query(query, %{"account_number" => number})
      when is_nil(number) or number == "",
      do: query

  def account_number_query(query, %{"account_number" => number}) do
    query |> where([u, b], ilike(b.number, ^"%#{number}%"))
  end

  def account_number_query(query, _), do: query

  def account_type_query(query, %{"account_type" => type}) when is_nil(type) or type == "",
    do: query

  def account_type_query(query, %{"account_type" => type}) do
    query |> where([u, b], ilike(b.type, ^"%#{type}%"))
  end

  def account_type_query(query, _), do: query

  def currency_query(query, %{"currency" => currency}) when is_nil(currency) or currency == "",
    do: query

  def currency_query(query, %{"currency" => currency}) do
    query |> where([u, b], ilike(b.currency, ^"%#{currency}%"))
  end

  def currency_query(query, _), do: query

  def balance_range_query(query, %{"balance_from" => from, "balance_to" => to})
      when is_nil(from) or is_nil(to) or from == "" or to == "",
      do: query

  def balance_range_query(query, %{"balance_from" => from, "balance_to" => to}) do
    case {Decimal.parse(from), Decimal.parse(to)} do
      {{:ok, from_decimal}, {:ok, to_decimal}} ->
        query
        |> where([u, b], b.balance >= ^from_decimal and b.balance <= ^to_decimal)

      _ ->
        query
    end
  end

  def balance_range_query(query, _), do: query

  # Applies sort direction to query for given field
  # Converts field to atom and handles various direction formats
  defp apply_sort(query, field, direction) do
    field = String.to_existing_atom(field)

    if direction in ["asc", :asc, "ASC", :ASC] do
      query |> order_by([a], asc: ^field)
    else
      query |> order_by([a], desc: ^field)
    end
  end

  # Processes date range filtering if dates are valid
  # Converts dates to datetime range for filtering
  defp process_date_range_if_valid(query, start_date, end_date)
       when is_nil(start_date) or is_nil(end_date) or start_date == "" or end_date == "",
       do: query

  defp process_date_range_if_valid(query, start_date, end_date) do
    query
    |> process_from_date(start_date)
    |> process_to_date(end_date)
  end

  defp process_from_date(query, date) when is_nil(date) or date == "", do: query

  defp process_from_date(query, date) do
    query |> where([a], fragment("CAST(? AS DATE) >= ?", a.inserted_at, ^cast_to_date(date)))
  end

  defp process_to_date(query, date) when is_nil(date) or date == "", do: query

  defp process_to_date(query, date) do
    query |> where([a], fragment("CAST(? AS DATE) <= ?", a.inserted_at, ^cast_to_date(date)))
  end

  defp cast_to_date(date) do
    Date.from_iso8601!(date)
  end
end
