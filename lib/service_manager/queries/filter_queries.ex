defmodule ServiceManager.Queries.FilterQueries do
  @moduledoc false
  import Ecto.Query, warn: false

  def filter_handler(query, %{
        "query_field" => field,
        "operator" => operator,
        "query_search" => search
      }) do
    field = String.to_existing_atom(field)

    if search == "" or is_nil(search) do
      query
    else
      querying(query, field, operator, search)
    end
  end

  def filter_handler(query, %{query_field: field, operator: operator, query_search: search}) do
    field = String.to_existing_atom(field)

    if search == "" or is_nil(search) do
      query
    else
      querying(query, field, operator, search)
    end
  end

  def filter_handler(query, _anything), do: query

  defp querying(query, field, ">", search) do
    query
    |> where([a], field(a, ^field) > ^search)
  end

  defp querying(query, field, "<", search) do
    query
    |> where([a], field(a, ^field) < ^search)
  end

  defp querying(query, field, ">=", search) do
    query
    |> where([a], field(a, ^field) >= ^search)
  end

  defp querying(query, field, "<=", search) do
    query
    |> where([a], field(a, ^field) <= ^search)
  end

  defp querying(query, field, "==", search) do
    query
    |> where([a], field(a, ^field) == ^search)
  end

  defp querying(query, field, "!=", search) do
    query
    |> where([a], field(a, ^field) != ^search)
  end

  defp querying(query, field, "like", search) do
    query
    |> where([a], like(field(a, ^field), ^"%#{search}%"))
  end

  defp querying(query, field, "ilike", search) do
    query
    |> where([a], ilike(field(a, ^field), ^"%#{search}%"))
  end

  defp querying(query, _field, _any_operator, _search), do: query
end
