defmodule ServiceManager.Auth.ApiKeyValidator do
  alias ServiceManager.ThirdParty.ThirdPartyApiKey
  alias ServiceManager.Repo

  def validate_api_key(api_key) when is_binary(api_key) do
    case ThirdPartyApiKey |> Repo.get_by(api_key: api_key) do
      nil -> {:error, :invalid_api_key}
      api_key_record -> {:ok, api_key_record}
    end
  end

  def validate_api_key(_), do: {:error, :invalid_api_key}
end
