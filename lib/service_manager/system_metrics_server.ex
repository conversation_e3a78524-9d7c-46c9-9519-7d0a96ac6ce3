defmodule ServiceManager.SystemMetricsServer do
  use GenServer

  @megabyte 1024 * 1024
  @topic "metrics"

  def start_link(_) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  def get_metrics do
    GenServer.call(__MODULE__, :get_metrics)
  end

  @impl true
  def init(_) do
    # Start the update timer immediately
    Process.send(self(), :update, [])
    {:ok, get_system_metrics()}
  end

  @impl true
  def handle_call(:get_metrics, _from, metrics) do
    {:reply, metrics, metrics}
  end

  @impl true
  def handle_info(:update, _state) do
    metrics = get_system_metrics()
    # Broadcast to all subscribers
    Phoenix.PubSub.broadcast(ServiceManager.PubSub, @topic, {:metrics_update, metrics})
    # Schedule next update
    Process.send_after(self(), :update, 1000)
    {:noreply, metrics}
  end

  defp get_system_metrics do
    memory = :erlang.memory()
    {uptime_ms, _} = :erlang.statistics(:wall_clock)

    total_mem = div(memory[:total], @megabyte)
    process_mem = div(memory[:processes], @megabyte)

    days = div(uptime_ms, 86_400_000)
    remaining_ms = rem(uptime_ms, 86_400_000)
    hours = div(remaining_ms, 3_600_000)
    remaining_ms = rem(remaining_ms, 3_600_000)
    minutes = div(remaining_ms, 60_000)
    seconds = div(rem(remaining_ms, 60_000), 1000)

    uptime =
      cond do
        days > 0 -> "#{days}d #{hours}h #{minutes}m #{seconds}s"
        hours > 0 -> "#{hours}h #{minutes}m #{seconds}s"
        minutes > 0 -> "#{minutes}m #{seconds}s"
        true -> "#{seconds}s"
      end

    %{
      uptime: uptime,
      memory: "#{process_mem}/#{total_mem}MB"
    }
  end
end
