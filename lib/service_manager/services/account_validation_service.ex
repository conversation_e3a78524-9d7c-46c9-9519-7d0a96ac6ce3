defmodule ServiceManager.Services.AccountValidationService do
  @moduledoc """
  Service module for handling account balance and threshold validations.
  """

  alias ServiceManager.Repo
  alias ServiceManager.Notifications.SMSNotification

  @doc """
  Validates account thresholds and creates notifications if necessary.
  Returns :ok if all validations pass, or {:error, message} if any fail.
  """
  def validate_account_thresholds(account, amount, last_transaction_time) do
    with :ok <- validate_large_transaction(account, amount),
         :ok <- validate_low_balance(account, amount),
         :ok <- validate_suspicious_activity(account, last_transaction_time) do
      :ok
    else
      {:error, message} -> {:error, message}
    end
  end

  @doc """
  Validates if a transaction amount exceeds the large transaction threshold.
  Creates a notification if enabled and threshold is exceeded.
  """
  def validate_large_transaction(account, amount) do
    if should_check_large_transaction?(account) &&
         Decimal.compare(Decimal.new(amount), account.large_transaction_threshold) == :gt do
      create_notification(
        account,
        "Large Transaction Alert",
        "A large transaction of #{amount} #{account.currency} is being processed on your account #{account.account_number}"
      )
    end

    :ok
  end

  @doc """
  Validates if the account balance after transaction would fall below the low balance threshold.
  Creates a notification if enabled and threshold would be crossed.
  """
  def validate_low_balance(account, amount) do
    if should_check_low_balance?(account) do
      current_balance = Decimal.sub(account.balance, Decimal.new(amount))

      if Decimal.compare(current_balance, account.low_balance_threshold) == :lt do
        create_notification(
          account,
          "Low Balance Alert",
          "Your account #{account.account_number} balance will be below #{account.low_balance_threshold} #{account.currency} after this transaction"
        )
      end
    end

    :ok
  end

  @doc """
  Validates if there is suspicious activity based on transaction frequency.
  Creates a notification and returns error if suspicious activity is detected.
  """
  def validate_suspicious_activity(account, last_transaction_time) do
    if should_check_suspicious_activity?(account) && last_transaction_time do
      # Convert NaiveDateTime to DateTime in UTC
      last_transaction_datetime = DateTime.from_naive!(last_transaction_time, "Etc/UTC")
      now = DateTime.utc_now()
      # Calculate absolute time difference in seconds
      seconds_since_last = abs(DateTime.diff(now, last_transaction_datetime, :second))

      require Logger

      Logger.info(
        "Suspicious Activity Check - Time since last transaction: #{seconds_since_last} seconds, Threshold: #{account.suspicous_activity_seconds_between_transactions} seconds"
      )

      if seconds_since_last <= account.suspicous_activity_seconds_between_transactions do
        create_notification(
          account,
          "Suspicious Activity Alert",
          "Multiple transactions detected on your account #{account.account_number} within a short time period"
        )

        {:error, "Suspicious activity detected. Please try again later or contact support."}
      else
        :ok
      end
    else
      :ok
    end
  end

  # Private helper functions

  defp should_check_large_transaction?(account) do
    account.enable_alerts && account.large_transaction_alert
  end

  defp should_check_low_balance?(account) do
    account.enable_alerts && account.low_balance_alert
  end

  defp should_check_suspicious_activity?(account) do
    account.enable_alerts && account.suspicous_activity_alert
  end

  defp create_notification(account, title, message) do
    require Logger
    Logger.info("Creating notification - Title: #{title}, Phone: #{account.user.phone_number}")

    result =
      %SMSNotification{}
      |> SMSNotification.changeset(%{
        user_id: account.user_id,
        msisdn: account.user.phone_number,
        message: "#{title}: #{message}",
        details: %{
          title: title,
          account_number: account.account_number,
          timestamp: DateTime.utc_now()
        }
      })
      |> Repo.insert()

    case result do
      {:ok, notification} ->
        Logger.info("Notification created successfully - ID: #{notification.id}")
        {:ok, notification}

      {:error, changeset} ->
        Logger.error("Failed to create notification: #{inspect(changeset.errors)}")
        {:error, "Failed to create notification"}
    end
  end
end
