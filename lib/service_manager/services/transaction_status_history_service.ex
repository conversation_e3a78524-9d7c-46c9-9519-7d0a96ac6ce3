defmodule ServiceManager.Services.TransactionStatusHistoryService do
  @moduledoc """
  Service for creating and managing transaction status history.
  Provides functions to record status changes for transactions.
  """
  alias ServiceManager.Repo
  alias ServiceManager.Transactions.TransactionStatusHistory
  alias ServiceManager.Broadcasters.LoggerBroadcaster, as: Logger

  @doc """
  Records a status change for a transaction.
  
  ## Parameters
    - transaction_id: The ID of the transaction
    - from_status: The previous status of the transaction
    - to_status: The new status of the transaction
    - changed_by: The ID of the user who made the change (optional)
    - notes: Additional notes about the status change (optional)
    - metadata: Additional metadata about the status change (optional)
  
  ## Returns
    - {:ok, history} on success
    - {:error, changeset} on failure
  """
  def record_status_change(transaction_id, from_status, to_status, opts \\ []) do
    changed_by = Keyword.get(opts, :changed_by)
    notes = Keyword.get(opts, :notes)
    metadata = Keyword.get(opts, :metadata, %{})

    attrs = %{
      transaction_id: transaction_id,
      from_status: from_status,
      to_status: to_status,
      changed_by: changed_by,
      notes: notes,
      metadata: metadata
    }

    %TransactionStatusHistory{}
    |> TransactionStatusHistory.changeset(attrs)
    |> Repo.insert()
    |> case do
      {:ok, history} ->
        Logger.info("Recorded status change for transaction #{transaction_id}: #{from_status} -> #{to_status}")
        {:ok, history}
      
      {:error, changeset} ->
        Logger.error("Failed to record status change for transaction #{transaction_id}: #{inspect(changeset.errors)}")
        {:error, changeset}
    end
  end

  @doc """
  Gets the status history for a transaction.
  
  ## Parameters
    - transaction_id: The ID of the transaction
  
  ## Returns
    - List of status history entries, ordered by insertion date (newest first)
  """
  def get_transaction_status_history(transaction_id) do
    import Ecto.Query

    TransactionStatusHistory
    |> where([h], h.transaction_id == ^transaction_id)
    |> order_by([h], desc: h.inserted_at)
    |> Repo.all()
  end

  @doc """
  Gets the latest status change for a transaction.
  
  ## Parameters
    - transaction_id: The ID of the transaction
  
  ## Returns
    - The latest status history entry, or nil if none exists
  """
  def get_latest_status_change(transaction_id) do
    import Ecto.Query

    TransactionStatusHistory
    |> where([h], h.transaction_id == ^transaction_id)
    |> order_by([h], desc: h.inserted_at)
    |> limit(1)
    |> Repo.one()
  end
end
