defmodule ServiceManager.Services.GetAccounts do
  def retrieve(email) do
    # url = "http://localhost:4000/api/accounts/account_lookup"
    url =
      Application.get_env(:service_manager, :urls)[
        Application.get_env(:service_manager, :urls)[:active] |> String.to_atom()
      ] <> "/api/accounts/lookup"

    headers = [{"Content-type", "application/json"}]
    payload = %{email: email} |> Jason.encode!()

    case HTTPoison.post(url, payload, headers) do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        {:ok,
         body
         |> Jason.decode!()
         |> reformat_accounts()}

      {:ok, %HTTPoison.Response{status_code: 404, body: _body}} ->
        {:error, "Not found: The requested account number does not exist"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 400..499 ->
        {:error, "Client error: #{status_code}"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 500..599 ->
        {:error, "Server error: #{status_code}"}

      {:error, %HTTPoison.Error{reason: reason}} ->
        {:error, reason}
    end
  end

  defp reformat_accounts(%{"accounts" => accounts}) when is_list(accounts) do
    Enum.map(accounts, fn %{
                            "account_number" => account_number,
                            "balance" => balance,
                            "currency" => currency
                          } ->
      {"#{account_number} - #{currency}, #{balance}", account_number}
    end)
  end
end
