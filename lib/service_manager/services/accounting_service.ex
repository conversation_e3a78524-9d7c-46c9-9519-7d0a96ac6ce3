defmodule ServiceManager.Services.AccountingService do
  @moduledoc """
  Service module for handling accounting operations.
  This module provides functions to automatically create double entry accounting records
  when transactions are created or updated.
  """

  alias ServiceManager.Contexts.AccountingContext
  alias ServiceManager.Contexts.TransactionsContext
  alias ServiceManager.Contexts.WalletTransactionsContext
  alias ServiceManager.Transactions.Transaction
  alias ServiceManager.Transactions.WalletTransactions
  alias ServiceManager.Broadcasters.LoggerBroadcaster, as: Logger

  @doc """
  Creates a transaction with automatic double entry accounting records.
  
  This function:
  1. Creates the transaction record
  2. Creates the corresponding double entry accounting records
  
  Returns:
  - {:ok, %{transaction: transaction, accounting_entries: {debit_entry, credit_entry}}} on success
  - {:error, reason} on failure
  """
  def create_transaction_with_accounting(attrs, user_id \\ nil) do
    Logger.info("Creating transaction with accounting entries")
    
    # Start a database transaction
    Ecto.Multi.new()
    |> Ecto.Multi.run(:transaction, fn _, _ ->
      TransactionsContext.create_method(attrs)
    end)
    |> Ecto.Multi.run(:accounting_entries, fn _, %{transaction: transaction} ->
      AccountingContext.create_double_entry_for_transaction(transaction, user_id)
    end)
    |> ServiceManager.Repo.transaction()
    |> case do
      {:ok, %{transaction: transaction, accounting_entries: accounting_entries}} ->
        {:ok, %{transaction: transaction, accounting_entries: accounting_entries}}
      
      {:error, _operation, reason, _changes} ->
        Logger.error("Failed to create transaction with accounting entries: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  Creates a wallet transaction with automatic double entry accounting records.
  
  This function:
  1. Creates the wallet transaction record
  2. Creates the corresponding double entry accounting records
  
  Returns:
  - {:ok, %{wallet_transaction: wallet_transaction, accounting_entries: {debit_entry, credit_entry}}} on success
  - {:error, reason} on failure
  """
  def create_wallet_transaction_with_accounting(attrs, user_id \\ nil) do
    Logger.info("Creating wallet transaction with accounting entries")
    
    # Start a database transaction
    Ecto.Multi.new()
    |> Ecto.Multi.run(:wallet_transaction, fn _, _ ->
      WalletTransactionsContext.create(attrs)
    end)
    |> Ecto.Multi.run(:accounting_entries, fn _, %{wallet_transaction: wallet_transaction} ->
      AccountingContext.create_double_entry_for_wallet_transaction(wallet_transaction, user_id)
    end)
    |> ServiceManager.Repo.transaction()
    |> case do
      {:ok, %{wallet_transaction: wallet_transaction, accounting_entries: accounting_entries}} ->
        {:ok, %{wallet_transaction: wallet_transaction, accounting_entries: accounting_entries}}
      
      {:error, _operation, reason, _changes} ->
        Logger.error("Failed to create wallet transaction with accounting entries: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  Creates double entry accounting records for an existing transaction.
  
  This is useful for creating accounting entries for transactions that were created
  before the accounting system was implemented.
  
  Returns:
  - {:ok, {debit_entry, credit_entry}} on success
  - {:error, reason} on failure
  """
  def create_accounting_entries_for_transaction(transaction_id, user_id \\ nil) do
    with %Transaction{} = transaction <- TransactionsContext.get_data!(transaction_id),
         {:ok, entries} <- AccountingContext.create_double_entry_for_transaction(transaction, user_id) do
      {:ok, entries}
    else
      nil -> {:error, "Transaction not found"}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  Creates double entry accounting records for an existing wallet transaction.
  
  This is useful for creating accounting entries for wallet transactions that were created
  before the accounting system was implemented.
  
  Returns:
  - {:ok, {debit_entry, credit_entry}} on success
  - {:error, reason} on failure
  """
  def create_accounting_entries_for_wallet_transaction(transaction_id, user_id \\ nil) do
    with %WalletTransactions{} = transaction <- WalletTransactionsContext.get_data!(transaction_id),
         {:ok, entries} <- AccountingContext.create_double_entry_for_wallet_transaction(transaction, user_id) do
      {:ok, entries}
    else
      nil -> {:error, "Wallet transaction not found"}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  Creates double entry accounting records for all existing transactions.
  
  This is useful for creating accounting entries for transactions that were created
  before the accounting system was implemented.
  
  Returns:
  - {:ok, count} on success, where count is the number of transactions processed
  - {:error, reason} on failure
  """
  def create_accounting_entries_for_all_transactions(user_id \\ nil) do
    transactions = TransactionsContext.get_all_transactions()
    
    {success_count, error_count} =
      Enum.reduce(transactions, {0, 0}, fn transaction, {success, error} ->
        case AccountingContext.create_double_entry_for_transaction(transaction, user_id) do
          {:ok, _} -> {success + 1, error}
          {:error, _} -> {success, error + 1}
        end
      end)
    
    {:ok, %{success: success_count, error: error_count}}
  end

  @doc """
  Creates double entry accounting records for all existing wallet transactions.
  
  This is useful for creating accounting entries for wallet transactions that were created
  before the accounting system was implemented.
  
  Returns:
  - {:ok, count} on success, where count is the number of transactions processed
  - {:error, reason} on failure
  """
  def create_accounting_entries_for_all_wallet_transactions(user_id \\ nil) do
    transactions = WalletTransactionsContext.get_all_transactions()
    
    {success_count, error_count} =
      Enum.reduce(transactions, {0, 0}, fn transaction, {success, error} ->
        case AccountingContext.create_double_entry_for_wallet_transaction(transaction, user_id) do
          {:ok, _} -> {success + 1, error}
          {:error, _} -> {success, error + 1}
        end
      end)
    
    {:ok, %{success: success_count, error: error_count}}
  end
end
