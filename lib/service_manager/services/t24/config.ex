defmodule ServiceManager.Services.T24.Config do
  @moduledoc """
  Centralized configuration for T24 service integration.
  """

  def base_url, do: Application.get_env(:service_manager, :t24_base_url)
  def timeout, do: Application.get_env(:service_manager, :t24_timeout, :timer.seconds(20))
  def rate_limit, do: Application.get_env(:service_manager, :t24_rate_limit, 100)
  def pool_size, do: Application.get_env(:service_manager, :t24_pool_size, 20)

  def retry_config do
    %{
      max_retries: 1,
      base_delay: :timer.seconds(1),
      max_delay: :timer.seconds(3)
    }
  end

  def circuit_breaker_config do
    {{:standard, 5, :timer.seconds(10)}, {:reset, :timer.seconds(10)}}
  end
end
