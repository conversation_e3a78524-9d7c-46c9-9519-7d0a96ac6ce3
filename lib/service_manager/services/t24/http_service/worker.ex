defmodule ServiceManager.Services.T24.HTTPService.Worker do
  @moduledoc """
  Module for handling HTTP requests to T24.
  Implements retry logic, circuit breaking, and rate limiting.
  """
  require Logger

  alias ServiceManager.Contexts.T24LogsContext
  alias ServiceManager.Services.T24.Config

  # Configuration from centralized Config module
  @default_timeout Config.timeout()
  @max_retries Config.retry_config().max_retries
  @retry_delay_ms Config.retry_config().base_delay
  @fuse_name :t24_service
  @requests_per_second Config.rate_limit()

  @type response :: {:ok, map()} | {:error, String.t()} | {:retry, String.t()}

  @spec request(atom(), String.t(), list(), String.t()) :: response()
  def request(method, url, headers, body) do
    timeout = Application.get_env(:service_manager, :t24_timeout, @default_timeout)
    start_time = System.monotonic_time()

    :telemetry.execute(
      [:t24, :request, :start],
      %{system_time: System.system_time()},
      %{method: method, url: url}
    )

    result =
      case make_request(method, url, headers, body, timeout) do
        {:ok, response} ->
          {:ok, response}

        {:retry, reason} ->
          case make_request(method, url, headers, body, timeout) do
            {:ok, response} -> {:ok, response}
            {:error, reason} -> {:error, reason}
            {:retry, reason} -> {:error, "Max retries exceeded: #{reason}"}
          end

        {:error, reason} ->
          {:error, reason}
      end

    end_time = System.monotonic_time()
    duration = System.convert_time_unit(end_time - start_time, :native, :millisecond)

    :telemetry.execute(
      [:t24, :request, :stop],
      %{duration: duration},
      %{method: method, url: url, result: result}
    )

    result
  end

  defp make_request(method, url, headers, body, timeout, retry_count \\ 0) do
    case check_circuit_and_rate_limit() do
      :ok ->
        do_make_request(method, url, headers, body, timeout, retry_count)

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp check_circuit_and_rate_limit do
    with :ok <- check_circuit_breaker(),
         :ok <- check_rate_limit() do
      :ok
    end
  end

  defp check_circuit_breaker do
    case :fuse.ask(@fuse_name, :sync) do
      :ok -> :ok
      :blown -> {:error, "Circuit breaker is open"}
    end
  end

  defp check_rate_limit do
    case ExRated.check_rate(@fuse_name, 1_000, @requests_per_second) do
      {:ok, _} -> :ok
      {:error, _} -> {:error, "Rate limit exceeded"}
    end
  end

  defp do_make_request(method, url, headers, body, timeout, retry_count) do
    with :ok <- validate_method(method),
         :ok <- validate_url(url) do
      start_time = System.monotonic_time()
      {:ok, log_entry} = create_log_entry(method, url, headers, body, retry_count)

      try do
        # Properly handle nil or empty body
        request_body =
          case {method, body} do
            {method, _} when method in [:get, :delete] -> nil
            {_, nil} -> nil
            {_, ""} -> nil
            {_, body} when is_binary(body) -> body
            {_, body} -> Jason.encode!(body)
          end

        # Note: Finch.build expects the method as an atom
        request =
          Finch.build(method, url, headers, request_body)
          |> IO.inspect(label: "=======================================")

        case Finch.request(request, T24Finch, receive_timeout: timeout) do
          {:ok, response} ->
            handle_response(
              response,
              log_entry,
              System.monotonic_time() - start_time,
              retry_count
            )

          {:error, error} ->
            handle_error(error, log_entry, System.monotonic_time() - start_time, retry_count)
        end
      rescue
        error ->
          handle_exception(error, log_entry)
      end
    end
  end

  defp create_log_entry(method, url, headers, body, retry_count) do
    T24LogsContext.create_t24_log(%{
      method: to_string(method),
      url: url,
      headers: inspect(headers),
      request: body,
      message_id: "#{System.system_time(:nanosecond)}",
      response: "",
      retry_count: retry_count
    })
  end

  defp handle_response(response, log_entry, duration, retry_count) do
    duration_ms = System.convert_time_unit(duration, :native, :millisecond)

    case response.status do
      status when status in 200..299 ->
        update_log_success(log_entry, response, duration_ms, status)
        :fuse.melt(@fuse_name)
        {:ok, %{body: response.body, status: status, headers: response.headers}}

      status when status in [408, 429, 502, 503, 504] and retry_count < @max_retries ->
        update_log_retry(log_entry, response, duration_ms, status)
        Process.sleep(@retry_delay_ms)
        {:retry, "Received status code #{status}"}

      status ->
        update_log_failure(log_entry, response, duration_ms, status)
        # Change from blown to reset
        :fuse.reset(@fuse_name)
        {:error, "HTTP request failed with status #{status}"}
    end
  end

  defp handle_error(error, log_entry, duration, retry_count) do
    duration_ms = System.convert_time_unit(duration, :native, :millisecond)

    case error do
      %Finch.Error{reason: reason}
      when reason in [:timeout, :connect_timeout] and retry_count < @max_retries ->
        update_log_timeout(log_entry, duration_ms, reason)
        Process.sleep(@retry_delay_ms)
        {:retry, "Request timeout"}

      _ ->
        update_log_error(log_entry, duration_ms, error)
        # Change from blown to reset
        :fuse.reset(@fuse_name)
        {:error, "Request failed: #{inspect(error)}"}
    end
  end

  defp handle_exception(error, log_entry) do
    error_msg = "HTTP request failed with exception: #{inspect(error)}"
    Logger.error(error_msg)

    update_log_exception(log_entry, error)
    # Change from blown to reset
    :fuse.reset(@fuse_name)
    {:error, "Request failed unexpectedly"}
  end

  defp update_log_success(log_entry, response, duration_ms, status) do
    {:ok, _} =
      T24LogsContext.update_t24_log(log_entry, %{
        response: response.body,
        duration_ms: duration_ms,
        status_code: status,
        response_headers: inspect(response.headers)
      })
  end

  defp update_log_retry(log_entry, response, duration_ms, status) do
    {:ok, _} =
      T24LogsContext.update_t24_log(log_entry, %{
        response: response.body,
        duration_ms: duration_ms,
        status_code: status,
        response_headers: inspect(response.headers)
      })
  end

  defp update_log_failure(log_entry, response, duration_ms, status) do
    {:ok, _} =
      T24LogsContext.update_t24_log(log_entry, %{
        response: response.body,
        duration_ms: duration_ms,
        status_code: status,
        response_headers: inspect(response.headers)
      })
  end

  defp update_log_timeout(log_entry, duration_ms, reason) do
    {:ok, _} =
      T24LogsContext.update_t24_log(log_entry, %{
        response: "Timeout",
        duration_ms: duration_ms,
        error_details: inspect(reason)
      })
  end

  defp update_log_error(log_entry, duration_ms, error) do
    {:ok, _} =
      T24LogsContext.update_t24_log(log_entry, %{
        response: "Error",
        duration_ms: duration_ms,
        error_details: inspect(error)
      })
  end

  defp update_log_exception(log_entry, error) do
    {:ok, _} =
      T24LogsContext.update_t24_log(log_entry, %{
        response: "HTTP request failed with exception: #{inspect(error)}",
        duration_ms: 0,
        status_code: 500,
        error_details: inspect(error)
      })
  end

  defp validate_method(method) when method in [:get, :post, :put, :delete, :patch], do: :ok
  defp validate_method(_), do: {:error, "Invalid HTTP method"}

  defp validate_url(url) do
    case URI.parse(url) do
      %URI{scheme: scheme, host: host} when not is_nil(scheme) and not is_nil(host) -> :ok
      _ -> {:error, "Invalid URL format"}
    end
  end
end
