defmodule ServiceManager.Services.T24.T24Messages do
  @moduledoc """
  Facade module that exposes T24 message services.
  """

  alias ServiceManager.Services.T24.Messages.{
    GetCustomerProfile,
    GetAccountTransactions,
    GetTransaction
  }

  require Logger

  defmacro __using__(_opts) do
    quote do
      @behaviour ServiceManager.Services.T24.T24Behaviour

      def get_customer_profile(customer_id) do
        GetCustomerProfile.get_profile(customer_id)
      end

      def get_transaction_details(txn_number) do
        GetTransaction.execute(txn_number)
      end

      def get_account_transactions(account_number) do
        GetAccountTransactions.get_transactions(account_number)
      end
    end
  end
end
