defmodule ServiceManager.Services.T24.HTTPService do
  @moduledoc """
  HTTP Service for T24 integration with support for high traffic loads.

  Features:
  - Connection pooling with Finch
  - Rate limiting with ExRated
  - Circuit breaker with fuse
  - Telemetry for monitoring
  - Exponential backoff retry strategy

  ## Usage Examples

      # Basic GET request
      HTTPService.get("https://api.example.com/users", [{"Authorization", "Bearer token"}])

      # POST with JSON body
      HTTPService.post("https://api.example.com/users", Jason.encode!(%{name: "<PERSON>"}), [
        {"Content-Type", "application/json"}
      ])

  ## Rate Limiting
  The service is configured to handle #{@requests_per_second} requests per second.
  Exceeding this limit will result in {:error, :rate_limited}.

  ## Circuit Breaker
  The circuit breaker will open after 5 failures within 10 seconds,
  preventing further requests for 60 seconds.

  ## Retry Strategy
  Failed requests are retried with exponential backoff and jitter.
  Maximum retries: #{@max_retries}
  Base delay: #{@retry_delay_ms}ms
  """

  require Logger
  import ServiceManager.Logging.FunctionTracker

  alias ServiceManager.Services.T24.{RetryStrategy, HTTPService.Worker, Config}

  # Configuration from centralized Config module
  @requests_per_second Config.rate_limit()
  @fuse_name :t24_service
  @fuse_options Config.circuit_breaker_config()
  @default_timeout Config.timeout()
  @max_retries Config.retry_config().max_retries
  @retry_delay_ms Config.retry_config().base_delay

  # Types
  @type http_method :: :get | :post | :put | :delete | :patch
  @type error_reason :: :timeout | :circuit_open | :rate_limited | :connection_error | String.t()
  @type response :: {:ok, map()} | {:error, error_reason()}

  # Telemetry events
  @telemetry_prefix [:service_manager, :t24, :http]

  @doc "Performs a GET request"
  @spec get(String.t(), list()) :: response()
  track do
    def get(url, headers) do
      execute_request(:get, url, "", headers)
    end
  end

  @doc "Performs a POST request"
  @spec post(String.t(), String.t(), list()) :: response()
  track do
    def post(url, body, headers) do
      execute_request(:post, url, body, headers)
    end
  end

  @doc "Performs a PUT request"
  @spec put(String.t(), String.t(), list()) :: response()
  track do
    def put(url, body, headers) do
      execute_request(:put, url, body, headers)
    end
  end

  @doc "Performs a DELETE request"
  @spec delete(String.t(), list()) :: response()
  track do
    def delete(url, headers) do
      execute_request(:delete, url, "", headers)
    end
  end

  @doc """
  Performs a health check of the service.
  """
  @spec health_check() :: {:ok, map()} | {:error, atom()}
  track do
    def health_check do
      case :fuse.ask(@fuse_name, :sync) do
        :ok ->
          {:ok,
           %{
             status: :healthy,
             circuit_breaker: :closed,
             requests_per_second: @requests_per_second,
             retry_config: %{
               max_retries: @max_retries,
               base_delay: @retry_delay_ms
             }
           }}

        :blown ->
          {:error, :circuit_breaker_open}
      end
    end
  end

  # Private functions
  defp execute_request(method, url, body, headers) do
    request_id = Ecto.UUID.generate()

    context = %{
      request_id: request_id,
      start_time: System.system_time(:millisecond)
    }

    headers = [{"X-Request-ID", request_id} | headers]

    start_time = System.monotonic_time()

    :telemetry.execute(@telemetry_prefix ++ [:request, :start], %{}, %{
      method: method,
      url: url
    })

    result =
      with :ok <- check_rate_limit(),
           :ok <- check_circuit_breaker() do
        do_request_with_retry(method, url, headers, body, 0)
      end

    end_time = System.monotonic_time()
    duration = System.convert_time_unit(end_time - start_time, :native, :millisecond)

    :telemetry.execute(
      @telemetry_prefix ++ [:request, :stop],
      %{
        duration: duration
      },
      %{
        method: method,
        url: url,
        result: result
      }
    )

    track_metrics(method, url, start_time, result)

    result
  end

  defp do_request_with_retry(_method, _url, _headers, _body, retry_count)
       when retry_count >= @max_retries do
    {:error, :max_retries_exceeded}
  end

  defp do_request_with_retry(method, url, headers, body, retry_count) do
    case Worker.request(method, url, headers, body) do
      {:ok, _} = success ->
        success

      {:error, reason} = error ->
        Logger.warning("T24 request failed",
          error: reason,
          retry_count: retry_count,
          url: url
        )

        if should_retry?(reason) do
          delay = RetryStrategy.calculate_delay(retry_count)
          Process.sleep(delay)
          do_request_with_retry(method, url, headers, body, retry_count + 1)
        else
          error
        end
    end
  end

  defp should_retry?(reason) do
    true_value = true

    case reason do
      :timeout -> true_value
      :connection_error -> true_value
      {:status, status} when status in [500, 502, 503, 504] -> true_value
      _ -> false
    end
  end

  defp check_rate_limit do
    case ExRated.check_rate(@fuse_name, 1_000, @requests_per_second) do
      {:ok, _count} -> :ok
      {:error, _count} -> {:error, :rate_limited}
    end
  end

  defp check_circuit_breaker do
    case :fuse.ask(@fuse_name, :sync) do
      :ok -> :ok
      :blown -> {:error, :circuit_open}
    end
  end

  defp validate_response({:ok, response}) do
    case response do
      %{status: status, body: body} when status in 200..299 ->
        {:ok, body}

      %{status: 429} ->
        {:error, :rate_limited}

      %{status: status} when status in 500..599 ->
        {:error, :server_error}

      %{status: status} ->
        {:error, {:http_error, status}}
    end
  end

  defp track_metrics(method, url, start_time, result) do
    end_time = System.monotonic_time()
    duration = System.convert_time_unit(end_time - start_time, :native, :millisecond)

    :telemetry.execute(
      @telemetry_prefix ++ [:request],
      %{duration: duration},
      %{
        method: method,
        url: url,
        status: get_status(result)
      }
    )
  end

  defp get_status({:ok, _}), do: :success
  defp get_status({:error, reason}), do: reason
end
