defmodule ServiceManager.Services.T24.Messages.GetCurrencyExchange do
  @moduledoc """
  Service module for retrieving currency exchange rates from T24.
  """

  alias ServiceManager.Services.T24.HTTPService
  alias ServiceManager.Services.T24.Error

  @valid_currencies ["MWK", "USD", "EUR", "GBP", "ZAR", "CAD"]

  @doc """
  Retrieves currency exchange rate from T24 for converting between currencies.

  ## Parameters
    - base_code: String representing the base currency code (e.g. "MWK")
    - target_currency: String representing the target currency code
    - amount: String representing the amount to convert

  ## Returns
    - {:ok, response} on success where response contains exchange rate data
    - {:error, reason} on failure

  ## Example
      iex> GetCurrencyExchange.execute("MWK", "USD", "100.00")
      {:ok, %{rate: 0.00098, converted_amount: 0.098}}
  """
  @spec execute(String.t(), String.t(), String.t()) :: {:ok, map()} | {:error, Error.t()}
  def execute(base_code, target_currency, amount)
      when is_binary(base_code) and is_binary(target_currency) and is_binary(amount) do
    with :ok <- validate_currency(base_code),
         :ok <- validate_currency(target_currency),
         :ok <- validate_amount(amount) do
      url =
        "#{Application.get_env(:service_manager, :t24_base_url)}/currencyExchanges/api/v1.0.0/party/currency/exchrate/#{base_code}/#{target_currency}/#{amount}/BUY"

      headers = [
        {"Accept", "*/*"},
        {"Content-Type", "application/json"},
        {"Authorization", "Bearer #{Application.get_env(:service_manager, :t24_token)}"}
      ]

      HTTPService.get(url, headers)
      |> case do
        {:ok, response} ->
          {:ok, response}

        {:error, reason} when is_binary(reason) ->
          {:error, Error.new(:unexpected, reason)}

        {:error, %Error{} = error} ->
          {:error, error}

        error ->
          error
      end
    end
  end

  def execute(_, _, _) do
    {:error, Error.new(:validation, "Invalid parameters format")}
  end

  defp validate_currency(code) when code in @valid_currencies, do: :ok
  defp validate_currency(_), do: {:error, Error.new(:validation, "Invalid currency code")}

  defp validate_amount(amount) do
    case Float.parse(amount) do
      {value, ""} when value > 0 -> :ok
      _ -> {:error, Error.new(:validation, "Invalid amount format")}
    end
  end
end
