defmodule ServiceManager.Services.T24.Messages.PullProfileAccounts do
  @moduledoc """
  Service module for retrieving customer profile information from T24.
  """

  alias ServiceManager.Services.T24.HTTPService
  alias ServiceManager.Services.T24.Error

  import ServiceManager.Logging.FunctionTracker

  @doc """
  Retrieves customer profile information from T24 for the given customer ID.

  ## Parameters
    - customer_id: String representing the customer ID

  ## Returns
    - {:ok, response} on success where response contains customer profile data
    - {:error, reason} on failure
  """

  @spec get_accounts(String.t()) :: {:ok, map()} | {:error, String.t()}
  track do
    def get_accounts(customer_id) when is_binary(customer_id) and customer_id != "" do
      url = "https://fdh-esb.ngrok.dev/esb/customer/accounts/v1/api/#{customer_id}"
      username = Application.get_env(:service_manager, :t24_username)
      password = Application.get_env(:service_manager, :t24_password)
      credentials = Base.encode64("#{username}:#{password}")

      headers = [
        {"Accept", "*/*"},
        {"Content-Type", "application/json"},
        {"Authorization", "Basic #{credentials}"}
      ]

      HTTPService.get(url, headers)
      |> IO.inspect()
      |> case do
        {:ok, profile} ->
          {:ok, profile}

        {:error, reason} when is_binary(reason) ->
          {:error, Error.new(:unexpected, reason)}

        {:error, %Error{} = error} ->
          {:error, error}

        error ->
          error
      end
    end
  end

  # ServiceManager.Services.T24.Messages.PullProfileAccounts.get_accounts("********")

  def get_profile(_) do
    {:error, Error.new(:validation, "Invalid customer ID format")}
  end

  @spec test() :: any()
  def test() do
    "********"
    |> get_accounts()
    |> case do
      {:error, reason} ->
        {:error, Error.new(:validation, reason)}

      {:ok, %{status: _status, body: body, headers: _headers}} ->
        body |> Jason.decode!()
    end
  end

  track do
    def get_profile_parsed(customer_number) do
      customer_number
      |> get_accounts()
      |> case do
        {:error, reason} ->
          {:error, Error.new(:validation, reason)}

        {:ok, %{status: _status, body: body, headers: _headers}} ->
          body |> Jason.decode!()
      end
    end
  end

  @doc """
  Makes a direct HTTPoison GET request to retrieve customer accounts.
  Bypasses the HTTPService wrapper for direct API testing.

  ## Parameters
    - customer_id: String representing the customer ID

  ## Returns
    - {:ok, response} on success where response contains parsed JSON data
    - {:error, reason} on failure
  """

  # ServiceManager.Services.T24.Messages.PullProfileAccounts.get_account_ids_direct!("********")

  @spec get_accounts_direct(String.t()) :: {:ok, map()} | {:error, Error.t() | String.t()}
  def get_accounts_direct(customer_id) when is_binary(customer_id) and customer_id != "" do
    url = "https://fdh-esb.ngrok.dev/esb/customer/accounts/v1/api/#{customer_id}"

    # Using hardcoded admin:admin credentials as specified in the curl command
    credentials = Base.encode64("admin:admin")

    headers = [
      {"Accept", "*/*"},
      {"Authorization", "Basic #{credentials}"}
    ]

    case HTTPoison.get(url, headers) do
      {:ok, %HTTPoison.Response{status_code: status_code, body: body}}
      when status_code in 200..299 ->
        case Jason.decode(body) do
          {:ok, decoded} -> {:ok, decoded}
          {:error, _} -> {:error, Error.new(:unexpected, "Failed to decode JSON response")}
        end

      {:ok, %HTTPoison.Response{status_code: status_code, body: body}}
      when status_code >= 400 and status_code < 500 ->
        {:error, Error.new(:validation, "Client error: #{status_code} - #{body}")}

      {:ok, %HTTPoison.Response{status_code: status_code, body: body}} when status_code >= 500 ->
        {:error, Error.new(:unexpected, "Server error: #{status_code} - #{body}")}

      {:error, %HTTPoison.Error{reason: reason}} ->
        {:error, Error.new(:connection, "Connection error: #{inspect(reason)}")}
    end
  end

  def get_accounts_direct(_) do
    {:error, Error.new(:validation, "Invalid customer ID format")}
  end

  @doc """
  Retrieves a flat list of account IDs for the given customer ID.
  Makes a direct HTTPoison GET request and extracts just the account IDs.

  ## Parameters
    - customer_id: String representing the customer ID

  ## Returns
    - {:ok, list} on success where list contains account ID strings
    - {:error, reason} on failure
  """
  @spec get_account_ids_direct(String.t()) ::
          {:ok, list(String.t())} | {:error, Error.t() | String.t()}
  def get_account_ids_direct(customer_id) when is_binary(customer_id) and customer_id != "" do
    case get_accounts_direct(customer_id) do
      {:ok, %{"body" => body}} when is_list(body) ->
        account_ids =
          Enum.map(body, fn account ->
            Map.get(account, "ACCOUNT.ID")
          end)

        {:ok, account_ids}

      {:ok, response} ->
        {:error, Error.new(:unexpected, "Unexpected response format: #{inspect(response)}")}

      error ->
        error
    end
  end

  def get_account_ids_direct(_) do
    {:error, Error.new(:validation, "Invalid customer ID format")}
  end

  @doc """
  Retrieves a flat list of account IDs for the given customer ID.
  Makes a direct HTTPoison GET request and extracts just the account IDs.
  Returns the list directly without wrapping in a tuple.

  ## Parameters
    - customer_id: String representing the customer ID

  ## Returns
    - list of account ID strings on success
    - raises an exception on failure

  ## Examples
      iex> get_account_ids_direct!("********")
      ["*************", "*************", "*************", "*************"]
  """
  @spec get_account_ids_direct!(String.t()) :: list(String.t())
  def get_account_ids_direct!(customer_id) when is_binary(customer_id) and customer_id != "" do
    case get_account_ids_direct(customer_id) do
      {:ok, account_ids} -> account_ids
      {:error, %Error{} = error} -> raise "Failed to get account IDs: #{error.message}"
      {:error, reason} -> raise "Failed to get account IDs: #{inspect(reason)}"
    end
  end
end
