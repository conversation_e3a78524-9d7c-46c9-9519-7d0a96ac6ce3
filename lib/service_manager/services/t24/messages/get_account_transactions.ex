defmodule ServiceManager.Services.T24.Messages.GetAccountTransactions do
  @moduledoc """
  Service module for retrieving account transactions from T24.
  """

  alias ServiceManager.Services.T24.HTTPService
  alias ServiceManager.Services.T24.Error

  @doc """
  Retrieves account transactions from T24 for the given account number.

  ## Parameters
    - account_number: String representing the account number
  ## Returns
    - {:ok, response} on success where response contains transaction data
    - {:error, reason} on failure

  ## Example
      iex> GetAccountTransactions.get_transactions("*************")
      {:ok, %{transactions: [...]}}
  """
  @spec get_transactions(String.t()) :: {:ok, map()} | {:error, String.t()}
  def get_transactions(account_number) when is_binary(account_number) and account_number != "" do
    url = "https://fdh-esb.ngrok.dev/api/esb/reports/v1/account/transactions/#{account_number}"
    username = Application.get_env(:service_manager, :t24_username)
    password = Application.get_env(:service_manager, :t24_password)
    credentials = Base.encode64("#{username}:#{password}")

    headers = [
      {"Accept", "*/*"},
      {"Content-Type", "application/json"},
      {"Authorization", "Basic #{credentials}"}
    ]

    HTTPService.get(url, headers)
    |> case do
      {:ok, transactions} ->
        {:ok, transactions}

      {:error, reason} when is_binary(reason) ->
        {:error, Error.new(:unexpected, reason)}

      {:error, %Error{} = error} ->
        {:error, error}

      error ->
        error
    end
  end

  def get_transactions(_) do
    {:error, Error.new(:validation, "Invalid account number format")}
  end

  @spec test() :: any()
  def test() do
    "*************"
    |> get_transactions()
    |> case do
      {:error, reason} ->
        {:error, Error.new(:validation, reason)}

      {:ok, %{status: status, body: body, headers: headers}} ->
        body |> Jason.decode!()
    end
  end
end
