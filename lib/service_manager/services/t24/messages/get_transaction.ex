defmodule ServiceManager.Services.T24.Messages.GetTransaction do
  @moduledoc """
  Service module for retrieving account transactions from T24.
  """

  alias ServiceManager.Services.T24.HTTPService
  alias ServiceManager.Services.T24.Error

  @doc """
  Retrieves account transactions from T24 for the given account number.

  ## Parameters
    - account_number: String representing the account number
  ## Returns
    - {:ok, response} on success where response contains transaction data
    - {:error, reason} on failure

  ## Example
      iex> GetAccountTransactions.get_transactions("*************")
      {:ok, %{transactions: [...]}}
  """
  @spec execute(String.t()) :: {:ok, map()} | {:error, String.t()}
  def execute(txn_number) when is_binary(txn_number) and txn_number != "" do
    url =
      "#{Application.get_env(:service_manager, :t24_base_url)}/getTransaction/api/v1.0.0/party/transaction/#{txn_number}"

    headers = [
      {"Accept", "*/*"},
      {"Content-Type", "application/json"},
      {"Authorization", "Bearer #{Application.get_env(:service_manager, :t24_token)}"}
    ]

    HTTPService.get(url, headers)
    |> case do
      {:ok, transactions} ->
        {:ok, transactions}

      {:error, reason} when is_binary(reason) ->
        {:error, Error.new(:unexpected, reason)}

      {:error, %Error{} = error} ->
        {:error, error}

      error ->
        error
    end
  end

  def execute(_) do
    {:error, Error.new(:validation, "Invalid account number format")}
  end
end
