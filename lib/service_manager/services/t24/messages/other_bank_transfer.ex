defmodule ServiceManager.Services.T24.Messages.OtherBankTransfer do
  @moduledoc """
  Service module for handling external bank transfers through T24.
  """

  alias ServiceManager.Pool.RequestPool

  @doc """
  Executes an external bank transfer request.
  """
  def execute(from_account, to_account, transaction) do
    RequestPool.create_request(%{
      method: "POST",
      url: "https://fdh-esb.ngrok.dev/api/esb/transfers/other/1.0/bank",
      headers: %{
        "Content-Type" => "application/json",
        "Authorization" => "Basic YWRtaW46YWRtaW4="
      },
      body: build_request_body(from_account, to_account, transaction),
      name: "external transfer",
      reference: build_reference(from_account, to_account)
    })
  end

  defp build_request_body(from_account, to_account, transaction) do
    %{
      "header" => %{},
      "body" => %{
        "transactionType" => "AC",
        "debitAccountNumber" => transaction.sender_account,
        "debitAmount" => transaction.amount,
        "debitCurrency" => from_account.currency,
        "creditAccountNumber" => transaction.receiver_account
      }
    }
  end

  defp build_reference(from_account, to_account) do
    "transfer_#{from_account.account_number}_#{to_account.account_number}_#{:os.system_time(:millisecond)}"
  end
end
