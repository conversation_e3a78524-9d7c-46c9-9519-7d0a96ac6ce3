defmodule ServiceManager.Services.T24.Messages.GetCustomerProfile do
  @moduledoc """
  Service module for retrieving customer profile information from T24.
  """

  alias ServiceManager.Services.T24.HTTPService
  alias ServiceManager.Services.T24.Error

  import ServiceManager.Logging.FunctionTracker

  @doc """
  Retrieves customer profile information from T24 for the given customer ID.

  ## Parameters
    - customer_id: String representing the customer ID

  ## Returns
    - {:ok, response} on success where response contains customer profile data
    - {:error, reason} on failure
  """

  @spec get_profile(String.t()) :: {:ok, map()} | {:error, String.t()}
  track do
    def get_profile(customer_id) when is_binary(customer_id) and customer_id != "" do
      url = "https://fdh-esb.ngrok.dev/api/esb/customers/v1/customer/profile/#{customer_id}"
      username = Application.get_env(:service_manager, :t24_username)
      password = Application.get_env(:service_manager, :t24_password)
      credentials = Base.encode64("#{username}:#{password}")

      headers = [
        {"Accept", "*/*"},
        {"Content-Type", "application/json"},
        {"Authorization", "Basic #{credentials}"}
      ]

      HTTPService.get(url, headers)
      |> case do
        {:ok, profile} ->
          {:ok, profile}

        {:error, reason} when is_binary(reason) ->
          {:error, Error.new(:unexpected, reason)}

        {:error, %Error{} = error} ->
          {:error, error}

        error ->
          error
      end
    end
  end

  def get_profile(_) do
    {:error, Error.new(:validation, "Invalid customer ID format")}
  end

  @spec test() :: any()
  def test() do
    "35041871"
    |> get_profile()
    |> case do
      {:error, reason} ->
        {:error, Error.new(:validation, reason)}

      {:ok, %{status: status, body: body, headers: headers}} ->
        body |> Jason.decode!()
    end
  end

  track do
    def get_profile_parsed(customer_number) do
      customer_number
      |> get_profile()
      |> case do
        {:error, reason} ->
          {:error, Error.new(:validation, reason)}

        {:ok, %{status: status, body: body, headers: headers}} ->
          body |> Jason.decode!()
      end
    end
  end
end
