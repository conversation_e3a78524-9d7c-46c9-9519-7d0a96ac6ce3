defmodule ServiceManager.Services.T24.Messages.GetAccountBalance do
  @moduledoc """
  Service module for retrieving account balance information from T24.
  """

  alias ServiceManager.Services.T24.HTTPService
  alias ServiceManager.Services.T24.Error
  alias ServiceManager.Services.T24.Messages.GetAccountDetails

  @doc """
  Retrieves account balance information from T24 for the given account number.

  ## Parameters
    - account_number: String representing the account number

  ## Returns
    - {:ok, response} on success where response contains account balance data
    - {:error, reason} on failure
  """
  @spec get_account_balance(String.t()) :: {:ok, map()} | {:error, String.t()}
  def get_account_balance(account_number)
      when is_binary(account_number) and account_number != "" do
    url = "https://fdh-esb.ngrok.dev/api/esb/accounts/account/v1/balances/#{account_number}"
    username = Application.get_env(:service_manager, :t24_username)
    password = Application.get_env(:service_manager, :t24_password)
    credentials = Base.encode64("#{username}:#{password}")

    headers = [
      {"Accept", "*/*"},
      {"Content-Type", "application/json"},
      {"Authorization", "Basic #{credentials}"}
    ]

    HTTPService.get(url, headers)
    |> IO.inspect(label: "Processing ESB Call")
    |> case do
      {:ok, balance} ->
        {:ok, balance}

      {:error, reason} when is_binary(reason) ->
        {:error, Error.new(:unexpected, reason)}

      {:error, %Error{} = error} ->
        {:error, error}

      error ->
        error
    end
  end

  def get_account_balance(_) do
    {:error, Error.new(:validation, "Invalid account number format")}
  end

  @doc """
  Flattens the nested balance response into a single-level map.
  """
  def flatten_balance_response(%{
        "header" => %{
          "audit" => %{
            "T24_time" => t24_time,
            "responseParse_time" => response_parse_time,
            "requestParse_time" => request_parse_time
          },
          "page_start" => page_start,
          "page_token" => page_token,
          "total_size" => total_size,
          "page_size" => page_size,
          "status" => status
        },
        "body" => [balance_info | _]
      }) do
    %{
      "cleared_balance" => balance_info["clearedBalance"],
      "working_balance" => balance_info["workingBalance"],
      "online_actual_balance" => balance_info["onlineActualBalance"],
      "available_balance" => balance_info["availableBalance"],
      "currency_id" => balance_info["currencyId"]
    }
  end

  def flatten_balance_response(_), do: %{}

  @doc """
  Combines account details with balance information.
  """
  def get_combined_account_info(account_number) do
    with {:ok, account_details} <- GetAccountDetails.get_account_details_parsed(account_number),
         balance_info <- get_account_balance_parsed(account_number) do
      Map.merge(account_details, balance_info)
    else
      error -> error
    end
  end

  @spec test() :: any()
  def test() do
    "*************"
    |> get_account_balance()
    |> case do
      {:error, reason} ->
        {:error, Error.new(:validation, reason)}

      {:ok, %{status: status, body: body, headers: headers}} ->
        body
        |> Jason.decode!()
        |> flatten_balance_response()
    end
  end

  def get_account_balance_parsed(account_number) do
    account_number
    |> get_account_balance()
    |> case do
      {:error, reason} ->
        {:error, Error.new(:validation, reason)}

      {:ok, %{status: status, body: body, headers: headers}} ->
        body
        |> Jason.decode!()
        |> flatten_balance_response()
    end
  end
end

# ServiceManager.Services.T24.Messages.GetAccountBalance.get_account_balance_parsed("*************")
