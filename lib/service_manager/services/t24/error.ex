defmodule ServiceManager.Services.T24.Error do
  @moduledoc """
  Structured exception types for T24 service errors.
  """

  @derive Jason.Encoder
  defexception [:type, :message, :details]

  @type error_type ::
          :validation
          | :timeout
          | :circuit_open
          | :rate_limited
          | :connection
          | :unexpected
          | :max_retries

  @type t :: %__MODULE__{
          type: error_type(),
          message: String.t(),
          details: map() | nil
        }

  def new(type, message, details \\ nil) do
    %__MODULE__{
      type: type,
      message: message,
      details: details
    }
  end

  def message(%__MODULE__{message: message}), do: message

  @spec from_http_error(atom() | String.t()) :: t()
  def from_http_error(:timeout), do: new(:timeout, "Request timed out")
  def from_http_error(:circuit_open), do: new(:circuit_open, "Circuit breaker is open")
  def from_http_error(:rate_limited), do: new(:rate_limited, "Rate limit exceeded")
  def from_http_error(:connection_error), do: new(:connection, "Connection failed")
  def from_http_error(msg) when is_binary(msg), do: new(:unexpected, msg)
  def from_http_error(_), do: new(:unexpected, "Unexpected error occurred")
end
