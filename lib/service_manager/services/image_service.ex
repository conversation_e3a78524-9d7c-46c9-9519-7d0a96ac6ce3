defmodule ServiceManager.Services.ImageService do
  @moduledoc """
  Service for handling image processing operations including base64 conversion and format conversion.
  Supports both raster images (converted to PNG) and SVG vector graphics (both base64 and URL-encoded).
  """

  def save_base64_image(data_uri, file_name) do
    # Get MIME type and content from data URI
    {mime_type, content} = parse_data_uri(data_uri)

    # Ensure uploads directory exists
    File.mkdir_p!("priv/static/uploads")

    case mime_type do
      "image/svg+xml" ->
        # For SVG, handle both base64 and URL-encoded content
        decoded_content =
          case String.contains?(content, "%") do
            true ->
              # URL-encoded SVG
              URI.decode(content)

            false ->
              # Base64 encoded SVG
              case Base.decode64(content) do
                {:ok, decoded} -> decoded
                :error -> nil
              end
          end

        case decoded_content do
          nil ->
            {:error, "Invalid SVG content"}

          content ->
            # Save SVG content
            final_name = "#{file_name}.svg"
            final_path = "priv/static/uploads/#{final_name}"
            File.write!(final_path, content)
            {:ok, "/uploads/#{final_name}"}
        end

      _ ->
        # For other images, decode base64 and convert to PNG
        case Base.decode64(content) do
          {:ok, binary} ->
            # Save original file to temp location first
            File.mkdir_p!("priv/static/uploads/temp")
            temp_path = "priv/static/uploads/temp/#{file_name}"
            File.write!(temp_path, binary)

            # Convert to PNG
            final_name = "#{file_name}.png"
            final_path = "priv/static/uploads/#{final_name}"

            try do
              temp_path
              |> Mogrify.open()
              |> Mogrify.format("png")
              |> Mogrify.save(path: final_path)

              File.rm!(temp_path)
              {:ok, "/uploads/#{final_name}"}
            rescue
              e ->
                File.rm!(temp_path)
                {:error, "Image conversion failed: #{Exception.message(e)}"}
            end

          :error ->
            {:error, "Invalid base64 string"}
        end
    end
  end

  defp parse_data_uri(data_uri) do
    case String.split(data_uri, ",") do
      [header, content] ->
        mime_type =
          header
          |> String.replace("data:", "")
          |> String.split(";")
          |> List.first()

        {mime_type, content}

      [content] ->
        # Default to PNG if no header
        {"image/png", content}
    end
  end
end
