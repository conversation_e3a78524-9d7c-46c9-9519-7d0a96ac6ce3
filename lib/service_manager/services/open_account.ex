defmodule ServiceManager.Services.OpenAccountService do
  def process_account(params) do
    # url = "http://localhost:4000/api/accounts"
    url =
      (Application.get_env(:service_manager, :urls)[
         Application.get_env(:service_manager, :urls)[:active] |> String.to_atom()
       ] <> "/api/accounts")
      |> IO.inspect(label: "**************")

    params
    |> IO.inspect(label: "**************")

    headers = ["Content-Type": "application/json"]

    payload = Jason.encode!(params)

    case HTTPoison.post(url, payload, headers) do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        {:ok, body |> Jason.decode!()}

      {:ok, %HTTPoison.Response{status_code: 201, body: body}} ->
        {:ok, body |> Jason.decode!()}

      {:ok, %HTTPoison.Response{status_code: 404, body: _body}} ->
        {:error, "Not found: The requested account number does not exist"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 400..499 ->
        {:error, "Client error: #{status_code}"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 500..599 ->
        {:error, "Server error: #{status_code}"}

      {:error, %HTTPoison.Error{reason: reason}} ->
        {:error, reason}
    end
  end

  # def test_get_profile_by_account_number() do
  #   test_account_number = "ACCT89FBEA27972F109B"

  #   case get_profile_by_account_number(test_account_number) do
  #     {:ok, profile} ->
  #       IO.puts("Test passed: Received profile for account number #{test_account_number}")
  #       IO.inspect(profile)

  #     {:error, reason} ->
  #       IO.puts("Test failed: #{reason}")
  #   end
  # end
end
