defmodule ServiceManager.Services.BalanceSnapshotService do
  @moduledoc """
  Service for creating and managing balance snapshots.
  Provides functions to create pre and post transaction snapshots for accounts.
  """
  alias ServiceManager.Repo
  alias ServiceManager.Accounts.{FundAccounts, BalanceSnapshot}
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.Broadcasters.LoggerBroadcaster, as: Logger

  @doc """
  Creates a pre-transaction balance snapshot for a fund account.
  
  ## Parameters
    - account_id: The ID of the fund account
    - transaction_id: The ID of the transaction
    - reason: The reason for the snapshot (e.g., "transaction", "reversal")
  
  ## Returns
    - {:ok, snapshot} on success
    - {:error, reason} on failure
  """
  def create_pre_transaction_snapshot(account_id, transaction_id, reason) when is_integer(account_id) do
    case Repo.get(FundAccounts, account_id) do
      nil -> 
        Logger.error("Failed to create pre-transaction snapshot: Account #{account_id} not found")
        {:error, :account_not_found}
      account ->
        attrs = %{
          account_id: account.id,
          transaction_id: transaction_id,
          balance_type: "pre",
          balance: account.balance,
          working_balance: account.working_balance,
          cleared_balance: account.cleared_balance,
          currency: account.currency,
          snapshot_reason: reason
        }
        
        %BalanceSnapshot{}
        |> BalanceSnapshot.changeset(attrs)
        |> Repo.insert()
    end
  end

  @doc """
  Creates a post-transaction balance snapshot for a fund account.
  
  ## Parameters
    - account_id: The ID of the fund account
    - transaction_id: The ID of the transaction
    - reason: The reason for the snapshot (e.g., "transaction", "reversal")
  
  ## Returns
    - {:ok, snapshot} on success
    - {:error, reason} on failure
  """
  def create_post_transaction_snapshot(account_id, transaction_id, reason) when is_integer(account_id) do
    case Repo.get(FundAccounts, account_id) do
      nil -> 
        Logger.error("Failed to create post-transaction snapshot: Account #{account_id} not found")
        {:error, :account_not_found}
      account ->
        attrs = %{
          account_id: account.id,
          transaction_id: transaction_id,
          balance_type: "post",
          balance: account.balance,
          working_balance: account.working_balance,
          cleared_balance: account.cleared_balance,
          currency: account.currency,
          snapshot_reason: reason
        }
        
        %BalanceSnapshot{}
        |> BalanceSnapshot.changeset(attrs)
        |> Repo.insert()
    end
  end

  @doc """
  Creates a pre-transaction balance snapshot for a wallet account.
  
  ## Parameters
    - wallet_id: The ID of the wallet account
    - transaction_id: The ID of the transaction
    - reason: The reason for the snapshot (e.g., "transaction", "reversal")
  
  ## Returns
    - {:ok, snapshot} on success
    - {:error, reason} on failure
  """
  def create_pre_wallet_transaction_snapshot(wallet_id, transaction_id, reason) when is_integer(wallet_id) do
    case Repo.get(WalletUser, wallet_id) do
      nil -> 
        Logger.error("Failed to create pre-transaction snapshot: Wallet #{wallet_id} not found")
        {:error, :wallet_not_found}
      wallet ->
        attrs = %{
          account_id: wallet.id,
          transaction_id: transaction_id,
          balance_type: "pre",
          balance: wallet.balance,
          working_balance: wallet.balance, # Wallets don't have separate working balance
          cleared_balance: wallet.balance, # Wallets don't have separate cleared balance
          currency: wallet.currency,
          snapshot_reason: reason
        }
        
        %BalanceSnapshot{}
        |> BalanceSnapshot.changeset(attrs)
        |> Repo.insert()
    end
  end

  @doc """
  Creates a post-transaction balance snapshot for a wallet account.
  
  ## Parameters
    - wallet_id: The ID of the wallet account
    - transaction_id: The ID of the transaction
    - reason: The reason for the snapshot (e.g., "transaction", "reversal")
  
  ## Returns
    - {:ok, snapshot} on success
    - {:error, reason} on failure
  """
  def create_post_wallet_transaction_snapshot(wallet_id, transaction_id, reason) when is_integer(wallet_id) do
    case Repo.get(WalletUser, wallet_id) do
      nil -> 
        Logger.error("Failed to create post-transaction snapshot: Wallet #{wallet_id} not found")
        {:error, :wallet_not_found}
      wallet ->
        attrs = %{
          account_id: wallet.id,
          transaction_id: transaction_id,
          balance_type: "post",
          balance: wallet.balance,
          working_balance: wallet.balance, # Wallets don't have separate working balance
          cleared_balance: wallet.balance, # Wallets don't have separate cleared balance
          currency: wallet.currency,
          snapshot_reason: reason
        }
        
        %BalanceSnapshot{}
        |> BalanceSnapshot.changeset(attrs)
        |> Repo.insert()
    end
  end
end
