defmodule ServiceManager.Services.TransferCallbackServer do
  use GenServer
  require Logger

  # Client API

  def start_link(_) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Starting TransferCallbackServer"
    )

    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  def notify_transfer_update(transaction) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Received transfer update notification for transaction: #{transaction.id}"
    )

    Logger.debug(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Transaction details: #{inspect(transaction)}"
    )

    GenServer.cast(__MODULE__, {:notify_transfer_update, transaction})
  end

  # Server Callbacks

  @impl true
  def init(state) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Initializing TransferCallbackServer"
    )

    {:ok, state}
  end

  @impl true
  def handle_cast({:notify_transfer_update, transaction}, state) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Processing transfer callback for transaction: #{transaction.id}"
    )

    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Transaction status: #{transaction.status}"
    )

    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Transaction amount: #{transaction.amount}"
    )

    # Safely handle account information
    # from_account = if Ecto.assoc_loaded?(transaction.from_account),
    #   do: transaction.from_account.number,
    #   else: transaction.from_account_id || "unknown"
    # to_account = if Ecto.assoc_loaded?(transaction.to_account),
    #   do: transaction.to_account.number,
    #   else: transaction.to_account_id || "unknown"

    # Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - From account: #{from_account}")
    # Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - To account: #{to_account}")

    # Prepare callback payload and log it
    callback_payload = %{
      transaction_id: transaction.id,
      status: transaction.status,
      amount: transaction.amount,
      # from_account: from_account,
      # to_account: to_account,
      reference: transaction.reference,
      description: transaction.description,
      value_date: transaction.value_date,
      timestamp: DateTime.utc_now()
    }

    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Prepared callback payload: #{inspect(callback_payload)}"
    )

    # Here you would implement the actual callback sending logic
    # For example using HTTPoison to make a POST request:
    callback_url = System.get_env("CALLBACK_URL")

    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Attempting to send callback to: #{callback_url || "CALLBACK_URL not configured"}"
    )

    # case HTTPoison.post(
    #   callback_url,
    #   Jason.encode!(callback_payload),
    #   [{"Content-Type", "application/json"}]
    # ) do
    #   {:ok, response} ->
    #     Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Callback sent successfully")
    #     Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Response status: #{response.status_code}")
    #     Logger.debug("[#{DateTime.utc_now() |> DateTime.to_string()}] - Response body: #{inspect(response.body)}")
    #   {:error, error} ->
    #     Logger.error("[#{DateTime.utc_now() |> DateTime.to_string()}] - Failed to send callback")
    #     Logger.error("[#{DateTime.utc_now() |> DateTime.to_string()}] - Error details: #{inspect(error)}")
    # end

    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Completed processing transfer callback for transaction: #{transaction.id}"
    )

    {:noreply, state}
  end
end
