defmodule ServiceManager.Services.MerchantCallbackWorker do
  @moduledoc """
  Worker to process merchant transaction callbacks.
  """
  use GenServer
  require Logger
  import Ecto.Query

  alias ServiceManager.Repo
  alias ServiceManager.Schemas.MerchantTransaction
  alias HTTPoison

  # Check every 30 seconds
  @check_interval :timer.seconds(30)

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{})
  end

  def init(state) do
    schedule_work()
    {:ok, state}
  end

  def handle_info(:process_callbacks, state) do
    process_pending_callbacks()
    schedule_work()
    {:noreply, state}
  end

  defp schedule_work do
    Process.send_after(self(), :process_callbacks, @check_interval)
  end

  defp process_pending_callbacks do
    query =
      from t in MerchantTransaction,
        where:
          t.callback_sent == false and
            not is_nil(t.callback_url) and
            t.status == "completed",
        limit: 50

    query
    |> Repo.all()
    |> Enum.each(&send_callback/1)
  end

  defp send_callback(%MerchantTransaction{} = transaction) do
    callback_data = %{
      transaction_id: transaction.transaction_id,
      amount: transaction.amount,
      currency: transaction.currency,
      status: transaction.status,
      timestamp: NaiveDateTime.to_string(transaction.inserted_at)
    }

    headers = [
      {"Content-Type", "application/json"},
      {"X-Transaction-ID", transaction.transaction_id}
    ]

    case HTTPoison.post(transaction.callback_url, Jason.encode!(callback_data), headers)
         |> IO.inspect(label: "callback") do
      {:ok, response} ->
        update_transaction_callback_status(transaction, response)

      {:error, error} ->
        Logger.error(
          "Failed to send callback for transaction #{transaction.transaction_id}: #{inspect(error)}"
        )

        update_transaction_callback_error(transaction, error)
    end
  end

  defp update_transaction_callback_status(transaction, response) do
    attrs = %{
      callback_sent: true,
      callback_status: "#{response.status_code}",
      callback_response: response.body
    }

    transaction
    |> MerchantTransaction.changeset(attrs)
    |> Repo.update()
  end

  defp update_transaction_callback_error(transaction, error) do
    attrs = %{
      callback_status: "error",
      callback_response: inspect(error)
    }

    transaction
    |> MerchantTransaction.changeset(attrs)
    |> Repo.update()
  end
end
