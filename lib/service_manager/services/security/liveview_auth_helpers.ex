defmodule ServiceManager.Services.Security.LiveviewAuthHelpers do
  import Phoenix.LiveView
  alias ServiceManager.Services.Security.Authorization

  @moduledoc """
  Provides helper functions for LiveView authorization in the ServiceManager application.
  """

  @doc """
  Authorizes a LiveView action for a given user, action, and resource.

  ## Parameters

    - socket: The LiveView socket containing the current user.
    - action: The action being performed (e.g., :view, :edit, :delete).
    - resource: The resource being accessed (e.g., :main_section, :users, :reports).

  ## Returns

    - `{:ok, socket}` if the user is authorized.
    - `{:error, redirected_socket}` if the user is not authorized, redirecting to the unauthorized page.

  ## Examples

      iex> live_authorize(socket, :view, :users)
      {:ok, socket}

      iex> live_authorize(socket, :delete, :reports)
      {:error, redirected_socket}
  """
  def live_authorize(socket, action, resource) do
    # Extract the current user from the socket assigns
    user = socket.assigns.current_user

    # Check if the user is authorized for the given action and resource
    if Authorization.can?(user, action, resource) do
      {:ok, socket}
    else
      # If not authorized, redirect to the unauthorized page
      {:error, push_redirect(socket, to: "/unauthorized")}
    end
  end
end
