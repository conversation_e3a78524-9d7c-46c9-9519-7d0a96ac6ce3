defmodule ServiceManager.Services.Security.Authorization do
  alias ServiceManager.Accounts.User
  alias ServiceManager.Schema.Embedded.Rights

  @moduledoc """
  This module provides authorization functionality for the ServiceManager application.
  It checks if a user has permission to perform specific actions on resources.
  """

  @doc """
  Checks if a user has permission to perform an action on a resource.

  ## Parameters

    - user: The user to check permissions for.
    - action: The action being performed (e.g., :view, :edit, :delete).
    - resource: The resource being accessed (e.g., :main_section, :users, :reports).

  ## Returns

    - true if the user has permission, false otherwise.
  """
  def can?(user, action, resource) do
    user_rights = user.user_permissions.rights
    check_permission(user_rights, action, resource)
  end

  # Private function to check permissions based on the action and resource
  defp check_permission(rights, action, resource) do
    case {action, resource} do
      {:view, :main_section} ->
        has_main_section_access?(rights, :view)

      {action, module} when is_atom(module) ->
        has_module_access?(rights, module, action)

      _ ->
        false
    end
  end

  # Checks if the user has access to the main section for a specific action 
  defp has_main_section_access?(rights, action) do
    Map.get(rights, :main_section_access, %{})
    |> Map.get(action, false)
  end

  # Checks if the user has access to a specific module and action
  defp has_module_access?(rights, module, action) do
    try do
      with {:ok, backend_rights} <- Map.fetch(rights, :backend),
           {:ok, module_rights} <- Map.fetch(backend_rights, module),
           {:ok, action_value} <- Map.fetch(module_rights, action) do
        action_value
      else
        _ -> false
      end
    rescue
      _ -> false
    end
  end
end
