defmodule ServiceManager.Services.Security.SecurityEnforcement do
  @moduledoc """
  This module provides functionality for enforcing security and access control
  on menu options based on user permissions.
  """

  alias ServiceManager.Services.Security.Authorization

  @doc """
  This function filters a list of menu options based on the access rights of a given user.
  It iterates over each option, checks if the user has access to it, and recursively filters its sub-options.
  Options that the user does not have access to are removed from the list.
  """
  def filter_menu_options(options, user) when is_list(options) do
    # Map over each option to apply the option_handler function
    Enum.map(options, fn option ->
      # Handle each option based on user access
      case option_handler(option, user) do
        # If the user does not have access, return nil
        nil -> nil
        # If the user has access, return the updated option
        new_option -> new_option
      end
    end)
    # Reject any nil values from the mapped list to remove inaccessible options
    |> Enum.reject(&is_nil/1)
  end

  @doc """
  Handles a single menu option by checking if the user has access to it and recursively filtering its sub-options.

  ## Parameters
    - option: The menu option to handle.
    - user: The user to check access for.

  ## Returns
    - The updated option with filtered sub-options if the user has access, otherwise nil.
  """
  def option_handler(option, user) when is_map(option) do
    # Check if the user has access to the option
    if has_access?(option, user) do
      # If the option has sub-options, filter them recursively
      if Map.has_key?(option, :options) do
        updated_options =
          option.options
          |> filter_menu_options(user)

        # Update the option with the filtered sub-options
        Map.put(option, :options, updated_options)
      else
        # If the option does not have sub-options, return it as is
        option
      end
    else
      # If the user does not have access to the option, return nil
      nil
    end
  end

  @doc """
  Checks if a user has access to a specific menu option.
  """
  def has_access?(option, user) do
    case option.user_access do
      access_list when is_list(access_list) ->
        Enum.any?(access_list, fn access ->
          check_access(access, user)
        end)

      access ->
        check_access(access, user)
    end
  end

  # Private function to check specific access rights
  defp check_access({:main_section, action}, user) do
    Authorization.can?(user, action, :main_section)
  end

  defp check_access({:backend, module, action}, user) do
    Authorization.can?(user, action, module)
  end

  # Default to false for unrecognized access patterns
  defp check_access(_, _), do: false
end
