defmodule ServiceManager.Services.Notification.OrbitSms do
  @moduledoc """
  This module provides functionality to send SMS messages using the Orbit Mobile SMS service.

  It includes functions to send messages, handle responses, and format payloads for the API.
  The module uses HTTPoison for making HTTP requests and Jason for JSON encoding/decoding.
  """

  @auth %{
    username: "oxylane_ds",
    key: "1DgvtfRcsjNErxNluIpC",
    url: "https://bms.orbitmobile.co.zm/json.php"
  }

  @doc """
  Sends an SMS message to the specified MSISDN (phone number).

  ## Parameters

    - msisdn: String, the recipient's phone number
    - msg: String, the message content
    - auth: Map, authentication details (optional, defaults to module attribute @auth)

  ## Returns

    - {:ok, response} on success
    - {:error, reason} on failure

  """

  alias ServiceManager.Contexts.GeneralSettingsContext
  alias ServiceManager.Schemas.Settings.GeneralSettings
  alias ServiceManager.Repo

  def send_message(msisdn, msg, auth \\ @auth) do
    case Repo.get_by(GeneralSettings, key: "SMS-PROVIDER") do
      %GeneralSettings{value: "INTERNAL"} ->
        send_message_internal(msisdn, msg, auth)

      %GeneralSettings{value: "ORBIT"} ->
        obit_send_message_obit(msisdn, msg, auth)

      _ ->
        {:error, "Invalid or missing SMS provider configuration"}
    end
  end

  def send_message_internal(msisdn, msg, auth \\ @auth) do
    headers = [
      {"Content-type", "application/json"},
      {"Authorization", "Basic YWRtaW46YWRtaW4="}
    ]

    payload = format_payload_v2(msisdn, msg, auth)
    # url = get_url(auth)

    url = "https://fdh-esb.ngrok.dev/esb/sent-messages/v1/sent-messages"

    HTTPoison.post(url, payload, headers)
    |> IO.inspect(label: "***************")
    |> case do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        {:ok, response_handler(Jason.decode!(body))}

      {:ok, %HTTPoison.Response{status_code: 404}} ->
        {:error, "Not found: The requested account number does not exist"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 400..499 ->
        {:error, "Client error: #{status_code}"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 500..599 ->
        {:error, "Server error: #{status_code}"}

      {:error, %HTTPoison.Error{reason: reason}} ->
        {:error, reason}
    end
  end

  @doc """
  Handles the response from the API.

  ## Parameters

    - response: Map, the decoded JSON response from the API

  ## Returns

    - Map, containing status information and message

  """
  defp response_handler(details) do
    handle_response_code("200", "Message sent", details)
  end

  defp response_handler(%{"status" => %{"code" => code, "msg" => msg}} = details) do
    handle_response_code(code, msg, details)
  end

  defp response_handler(%{status: %{code: code, msg: msg}} = details) do
    handle_response_code(code, msg, details)
  end

  defp response_handler(body), do: body

  @doc """
  Interprets the response code and returns a standardized status map.

  ## Parameters

    - code: String, the response code from the API
    - msg: String, the message from the API

  ## Returns

    - Map, containing the code, status, and message

  """
  defp handle_response_code(code, msg, resp) do
    case code do
      "200" ->
        %{code: code, details: resp, status: "sent", msg: msg}

      "417" ->
        %{code: code, details: resp, status: "sent", msg: msg}

      "404" ->
        %{code: code, details: resp, status: "failed", msg: "No Authentication details specified"}

      "405" ->
        %{code: code, details: resp, status: "failed", msg: "No Request parameters specified"}

      "406" ->
        %{code: code, details: resp, status: "failed", msg: "Empty Request query specified"}

      "407" ->
        %{code: code, details: resp, status: "failed", msg: "Invalid Query specified"}

      "408" ->
        %{code: code, details: resp, status: "failed", msg: "Invalid Query parameter specified"}

      "409" ->
        %{
          code: code,
          details: resp,
          status: "failed",
          msg: "More than required Query parameters specified"
        }

      "410" ->
        %{code: code, details: resp, status: "failed", msg: "Missing required Query parameters"}

      "411" ->
        %{
          code: code,
          details: resp,
          status: "failed",
          msg: "Invalid Query parameter data type specified"
        }

      _ ->
        %{code: code, details: resp, status: "failed", msg: "unexpected error"}
    end
  end

  @doc """
  Formats the payload for the API request.

  ## Parameters

    - msisdn: String, the recipient's phone number
    - msg: String, the message content
    - auth: Map, authentication details

  ## Returns

    - String, JSON-encoded payload for the API request

  """
  defp format_payload(msisdn, msg, auth) do
    %{
      auth: %{
        username: auth[:username] || auth["username"],
        key: auth[:key] || auth["key"]
      },
      request: %{
        query: "send",
        params: %{
          messages: %{
            "1" => %{
              msisdn: msisdn,
              msg: msg,
              source: "FarmSwitch",
              channel: "SMS",
              country_code: "ZM"
            }
          }
        }
      }
    }
    |> Jason.encode!()
  end

  def format_payload_v2(msisdn, msg, auth) do
    %{
      client: "d79b32b5-b9a8-41de-b215-b038a913f619",
      message: msg,
      phoneNumber: msisdn
    }
    |> Jason.encode!()
  end

  defp get_url(%{url: value}), do: value
  defp get_url(%{"url" => value}), do: value

  def test() do
    send_message("265999122751", "FDH Message test")
  end

  @doc """
  Sends an SMS message to the specified MSISDN (phone number).

  ## Parameters

    - msisdn: String, the recipient's phone number
    - msg: String, the message content
    - auth: Map, authentication details (optional, defaults to module attribute @auth)

  ## Returns

    - {:ok, response} on success
    - {:error, reason} on failure

  """

  def obit_send_message_obit(msisdn, msg, auth \\ @auth) do
    headers = [{"Content-type", "application/json"}]
    payload = format_payload(msisdn, msg, auth)
    url = get_url(auth)

    HTTPoison.post(url, payload, headers)
    |> IO.inspect(label: "***************")
    |> case do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        {:ok, obit_response_handler(Jason.decode!(body))}

      {:ok, %HTTPoison.Response{status_code: 404}} ->
        {:error, "Not found: The requested account number does not exist"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 400..499 ->
        {:error, "Client error: #{status_code}"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 500..599 ->
        {:error, "Server error: #{status_code}"}

      {:error, %HTTPoison.Error{reason: reason}} ->
        {:error, reason}
    end
  end

  @doc """
  Handles the response from the API.

  ## Parameters

    - response: Map, the decoded JSON response from the API

  ## Returns

    - Map, containing status information and message

  """
  defp obit_response_handler(%{"status" => %{"code" => code, "msg" => msg}} = details) do
    handle_response_code(code, msg, details)
  end

  defp obit_response_handler(%{status: %{code: code, msg: msg}} = details) do
    handle_response_code(code, msg, details)
  end

  defp obit_response_handler(body), do: body

  @doc """
  Interprets the response code and returns a standardized status map.

  ## Parameters

    - code: String, the response code from the API
    - msg: String, the message from the API

  ## Returns

    - Map, containing the code, status, and message

  """
  defp handle_response_code(code, msg, resp) do
    case code do
      "200" ->
        %{code: code, details: resp, status: "sent", msg: msg}

      "417" ->
        %{code: code, details: resp, status: "sent", msg: msg}

      "404" ->
        %{code: code, details: resp, status: "failed", msg: "No Authentication details specified"}

      "405" ->
        %{code: code, details: resp, status: "failed", msg: "No Request parameters specified"}

      "406" ->
        %{code: code, details: resp, status: "failed", msg: "Empty Request query specified"}

      "407" ->
        %{code: code, details: resp, status: "failed", msg: "Invalid Query specified"}

      "408" ->
        %{code: code, details: resp, status: "failed", msg: "Invalid Query parameter specified"}

      "409" ->
        %{
          code: code,
          details: resp,
          status: "failed",
          msg: "More than required Query parameters specified"
        }

      "410" ->
        %{code: code, details: resp, status: "failed", msg: "Missing required Query parameters"}

      "411" ->
        %{
          code: code,
          details: resp,
          status: "failed",
          msg: "Invalid Query parameter data type specified"
        }

      _ ->
        %{code: code, details: resp, status: "failed", msg: "unexpected error"}
    end
  end

  def obit_test() do
    send_message("260976404051", "Hello Boldwin from Coilard")
  end
end
