defmodule ServiceManager.Services.Notification.SMSWorker do
  @moduledoc """
  A GenServer worker responsible for processing and sending SMS notifications.
  This worker periodically checks for pending notifications and sends them using the OrbitSms service.
  """

  use GenServer
  alias ServiceManager.Repo
  alias ServiceManager.Notifications.SMSNotification
  alias ServiceManager.Services.Notification.OrbitSms
  import Ecto.Query
  require Logger

  @doc """
  Starts the SMSWorker GenServer.
  """
  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{})
  end

  @doc """
  Initializes the GenServer state and schedules the first work cycle.
  """
  def init(state) do
    schedule_work()
    {:ok, state}
  end

  @doc """
  Handles the :work message, processing pending notifications and scheduling the next work cycle.
  """
  def handle_info(:work, state) do
    process_pending_notifications()
    schedule_work()
    {:noreply, state}
  end

  # Private Functions

  @doc """
  Schedules the next work cycle to run after 15 seconds.
  """
  defp schedule_work do
    # Run every 15 seconds
    Process.send_after(self(), :work, 15_000)
  end

  @doc """
  Processes pending notifications by querying the database and sending each notification.
  """
  defp process_pending_notifications do
    # Query for up to 100 ready notifications, ordered by insertion time
    query =
      from n in SMSNotification,
        where: n.status == "ready" and n.attempt_count <= 3,
        order_by: [asc: n.inserted_at],
        limit: 100

    notifications = Repo.all(query)

    # Send each notification
    Enum.each(notifications, &send_sms/1)
  end

  @doc """
  Sends an individual SMS notification and updates its status based on the result.
  """
  defp send_sms(notification) do
    try do
      case OrbitSms.send_message(notification.msisdn, notification.message) do
        {:ok, response} ->
          # Update notification as sent if successful
          update_notification(notification, "sent", %{provider_response: response})

        {:error, reason} ->
          # Update notification as failed if an error occurred
          update_notification(notification, "failed", %{error: reason})
      end
    rescue
      e ->
        Logger.error("Unexpected error while sending SMS: #{inspect(e)}")
        update_notification(notification, "failed", %{error: "Unexpected error: #{inspect(e)}"})
    catch
      kind, value ->
        Logger.error("Caught #{kind} while sending SMS: #{inspect(value)}")
        update_notification(notification, "failed", %{error: "Caught #{kind}: #{inspect(value)}"})
    end
  end

  @doc """
  Updates the status and details of a notification in the database.
  """
  defp update_notification(notification, status, details \\ %{}) do
    attrs = %{status: status, details: details, attempt_count: notification.attempt_count + 1}

    attrs =
      if status == "sent",
        do: Map.put(attrs, :sent_at, DateTime.truncate(DateTime.utc_now(), :second)),
        else: attrs

    notification
    |> SMSNotification.update_changeset(attrs)
    |> Repo.update()
    |> IO.inspect(label: "^^^^^^^^^^^^^^^^")
  rescue
    e ->
      Logger.error("Failed to update notification: #{inspect(e)}")
      {:error, :update_failed}
  end
end
