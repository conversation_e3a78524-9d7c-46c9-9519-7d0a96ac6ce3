defmodule ServiceManager.Processors.Supervisor do
  use Supervisor
  require <PERSON><PERSON>

  alias ServiceManager.Processors.{Switchboard, Initializer}

  def start_link(init_arg) do
    Supervisor.start_link(__MODULE__, init_arg, name: __MODULE__)
  end

  @impl Supervisor
  def init(_init_arg) do
    # Start Agent to track processor PIDs and supervisor options
    {:ok, _pid} =
      Agent.start_link(
        fn ->
          %{
            max_restarts: 3,
            max_seconds: 10,
            auto_restart: true
          }
        end,
        name: __MODULE__.State
      )

    children = [
      # Start Switchboard first
      Switchboard,
      # Start the Initializer
      Initializer
    ]

    # Get current supervisor options from state
    state = Agent.get(__MODULE__.State, & &1)

    opts = [
      strategy: :one_for_one,
      max_restarts: state.max_restarts,
      max_seconds: state.max_seconds,
      auto_restart: state.auto_restart
    ]

    Logger.info("Starting supervisor with options:")
    Logger.info("   - max_restarts: #{state.max_restarts}")
    Logger.info("   - max_seconds: #{state.max_seconds}")
    Logger.info("   - auto_restart: #{state.auto_restart}")

    Supervisor.init(children, opts)
  end

  @impl Supervisor
  def terminate(_reason, _state) do
    Agent.stop(__MODULE__.State)
    :ok
  end

  def handle_feature_change(flag_name, node_name) do
    current_node = to_string(Node.self())

    # Only handle if it's for this node
    if current_node == node_name do
      Logger.info("🔄 Processing feature change for: #{flag_name} on node: #{node_name}")

      case flag_name do
        "supervisor_" <> flag_type ->
          Logger.info("⚠️ Supervisor flag changed: #{flag_type}")
          # Update supervisor options without full restart
          update_supervisor_options(flag_type)
          :ok

        processor_flag
        when processor_flag in [
               # Regular processors
               "account_processor_enabled",
               "wallet_processor_enabled",
               "balance_sync_enabled",
               "callback_processor_enabled",
               # Notification processors
               "sms_worker_enabled",
               "notification_dispatcher_enabled",
               # Callback processors
               "merchant_callback_enabled",
               "transfer_callback_enabled",
               # Cache processors
               "route_cache_enabled",
               "config_cache_enabled",
               "ip_whitelist_cache_enabled",
               "statistics_cache_enabled",
               "otp_cache_enabled",
               "settings_cache_enabled"
             ] ->
          processor_module = get_processor_module(processor_flag)
          current_pid = Agent.get(__MODULE__.State, &Map.get(&1, processor_flag))
          is_enabled = Switchboard.feature_enabled?(String.to_atom(processor_flag), node_name)

          Logger.info("Processing service state change:")
          Logger.info("   - Module: #{processor_module}")
          Logger.info("   - Flag: #{processor_flag}")
          Logger.info("   - Node: #{node_name}")
          Logger.info("   - Target State: #{if is_enabled, do: "enabled", else: "disabled"}")
          Logger.info("   - Current PID: #{inspect(current_pid)}")

          if processor_module do
            cond do
              # Start if enabled and not running
              is_enabled and is_nil(current_pid) ->
                start_processor(processor_module, processor_flag, node_name)

              # Stop if disabled and running
              not is_enabled and current_pid ->
                stop_processor(processor_module, processor_flag, node_name)

              # Already in desired state
              is_enabled and current_pid ->
                Logger.info("✅ Service already running:")
                Logger.info("   - Module: #{processor_module}")
                Logger.info("   - Node: #{node_name}")
                Logger.info("   - PID: #{inspect(current_pid)}")

              not is_enabled and is_nil(current_pid) ->
                Logger.info("✅ Service already stopped:")
                Logger.info("   - Module: #{processor_module}")
                Logger.info("   - Node: #{node_name}")
            end
          end

        _ ->
          Logger.info("⚠️ Unhandled feature flag: #{flag_name}")
          :ok
      end
    else
      Logger.info("⏭️ Skipping feature change for different node:")
      Logger.info("   - Flag: #{flag_name}")
      Logger.info("   - Target Node: #{node_name}")
      Logger.info("   - Current Node: #{current_node}")
      :ok
    end
  end

  defp start_processor(processor_module, flag_name, node_name) do
    current_node = to_string(Node.self())

    if current_node == node_name do
      Logger.info("🚀 Attempting to start service: #{processor_module}")
      Logger.info("Service type: #{flag_name}")
      Logger.info("Node: #{node_name}")

      case DynamicSupervisor.start_child(ServiceManager.DynamicSupervisor, processor_module) do
        {:ok, pid} ->
          # Store PID for later reference
          Agent.update(__MODULE__.State, &Map.put(&1, flag_name, pid))
          Logger.info("✅ Service started successfully:")
          Logger.info("   - Module: #{processor_module}")
          Logger.info("   - PID: #{inspect(pid)}")
          Logger.info("   - Flag: #{flag_name}")
          Logger.info("   - Node: #{node_name}")
          {:ok, pid}

        error ->
          Logger.error("❌ Service failed to start:")
          Logger.error("   - Module: #{processor_module}")
          Logger.error("   - Error: #{inspect(error)}")
          Logger.error("   - Flag: #{flag_name}")
          Logger.error("   - Node: #{node_name}")
          error
      end
    else
      Logger.info("⏭️ Skipping start on different node:")
      Logger.info("   - Module: #{processor_module}")
      Logger.info("   - Target Node: #{node_name}")
      Logger.info("   - Current Node: #{current_node}")
      :ok
    end
  end

  defp stop_processor(processor_module, flag_name, node_name) do
    current_node = to_string(Node.self())

    if current_node == node_name do
      Logger.info("🛑 Attempting to stop service: #{processor_module}")
      Logger.info("Service type: #{flag_name}")
      Logger.info("Node: #{node_name}")

      # Get PID from state and terminate
      case Agent.get(__MODULE__.State, &Map.get(&1, flag_name)) do
        nil ->
          Logger.info("ℹ️ No running process found:")
          Logger.info("   - Module: #{processor_module}")
          Logger.info("   - Flag: #{flag_name}")
          Logger.info("   - Node: #{node_name}")
          :ok

        pid ->
          Logger.info("Found process PID: #{inspect(pid)}")

          case DynamicSupervisor.terminate_child(ServiceManager.DynamicSupervisor, pid) do
            :ok ->
              Agent.update(__MODULE__.State, &Map.delete(&1, flag_name))
              Logger.info("✅ Service stopped successfully:")
              Logger.info("   - Module: #{processor_module}")
              Logger.info("   - PID: #{inspect(pid)}")
              Logger.info("   - Flag: #{flag_name}")
              Logger.info("   - Node: #{node_name}")
              :ok

            error ->
              Logger.error("❌ Service failed to stop:")
              Logger.error("   - Module: #{processor_module}")
              Logger.error("   - PID: #{inspect(pid)}")
              Logger.error("   - Error: #{inspect(error)}")
              Logger.error("   - Flag: #{flag_name}")
              Logger.error("   - Node: #{node_name}")
              error
          end
      end
    else
      Logger.info("⏭️ Skipping stop on different node:")
      Logger.info("   - Module: #{processor_module}")
      Logger.info("   - Target Node: #{node_name}")
      Logger.info("   - Current Node: #{current_node}")
      :ok
    end
  end

  defp update_supervisor_options(flag_type) do
    case flag_type do
      "max_restarts" ->
        value = Switchboard.get_integer_value("supervisor_max_restarts", 3)
        Logger.info("Updating max_restarts to: #{value}")
        # Store in Agent for future supervisor restarts
        Agent.update(__MODULE__.State, &Map.put(&1, :max_restarts, value))

      "max_seconds" ->
        value = Switchboard.get_integer_value("supervisor_max_seconds", 5)
        Logger.info("Updating max_seconds to: #{value}")
        # Store in Agent for future supervisor restarts
        Agent.update(__MODULE__.State, &Map.put(&1, :max_seconds, value))

      "auto_restart" ->
        value = Switchboard.feature_enabled?("supervisor_auto_restart")
        Logger.info("Updating auto_restart to: #{value}")
        # Store in Agent for future supervisor restarts
        Agent.update(__MODULE__.State, &Map.put(&1, :auto_restart, value))

      _ ->
        Logger.warn("Unknown supervisor flag type: #{flag_type}")
    end
  end

  defp get_processor_module(flag_name) do
    case flag_name do
      # Regular processors
      "account_processor_enabled" -> ServiceManager.Processors.AccountTransactionProcessor
      "wallet_processor_enabled" -> ServiceManager.Processors.WalletTransactionProcessor
      "balance_sync_enabled" -> ServiceManager.Processors.UserBalanceSyncProcessor
      "callback_processor_enabled" -> ServiceManager.Processors.CallbackProcessor
      # Notification processors
      "sms_worker_enabled" -> ServiceManager.Services.Notification.SMSWorker
      "notification_dispatcher_enabled" -> ServiceManager.Notifications.Dispatcher
      # Callback processors
      "merchant_callback_enabled" -> ServiceManager.Services.MerchantCallbackWorker
      "transfer_callback_enabled" -> ServiceManager.Services.TransferCallbackServer
      # Cache processors
      "route_cache_enabled" -> ServiceManager.Cache.RouteCache
      "config_cache_enabled" -> ServiceManager.Cache.ConfigCache
      "ip_whitelist_cache_enabled" -> ServiceManager.Cache.IpWhitelistCache
      "statistics_cache_enabled" -> ServiceManager.Statistics.Cache
      "otp_cache_enabled" -> {ServiceManager.Cache.CacheWrapper, name: :otp_cache}
      "settings_cache_enabled" -> {ServiceManager.Cache.CacheWrapper, name: :settings}
      _ -> nil
    end
  end
end
