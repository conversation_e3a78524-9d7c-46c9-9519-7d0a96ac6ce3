defmodule ServiceManager.Processors.CallbackProcessor do
  @moduledoc """
  GenServer for processing and sending callbacks to registered external APIs.
  """
  use GenServer
  require Logger
  import Ecto.Query

  alias ServiceManager.Repo
  alias ServiceManager.Schemas.CallbackRegistry
  alias ServiceManager.Schemas.Callback
  alias ServiceManager.ThirdParty.ThirdPartyApiKey
  alias ServiceManager.Validators.CallbackSchemaValidator

  # Configuration
  # Retry intervals in milliseconds
  @retry_intervals [1_000, 5_000, 15_000, 30_000, 60_000]
  @max_retries length(@retry_intervals)
  # Check for pending transactions every 30 seconds
  @check_interval 30_000

  alias ServiceManager.Transactions.Transaction
  alias ServiceManager.Transactions.WalletTransactions
  alias ServiceManager.Pool.RequestPool

  # Client API

  def start_link(_opts) do
    Logger.info("Starting callback processor")
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  @doc """
  Processes callbacks for the given type and payload.
  Returns {:ok, message} on success or {:error, reason} on failure.
  """
  def send_callback(callback_type, payload) do
    # Get active callbacks for this type
    callbacks = get_active_callbacks(callback_type)

    # Process each callback and return aggregate result
    results =
      Enum.map(callbacks, fn callback ->
        # try do
        case process_single_callback(callback, payload) do
          {:ok, _} -> {:ok, "Callback processed"}
          {:error, reason} -> {:error, reason}
        end

        # rescue
        #   e ->
        #     Logger.error("Error processing callback: #{inspect(e)}")
        #     {:error, "Internal error"}
        # end
      end)

    if Enum.all?(results, fn
         {:ok, _} -> true
         _ -> false
       end) do
      {:ok, "All callbacks processed successfully"}
    else
      {:error, "One or more callbacks failed"}
    end
  end

  # Server Callbacks

  @impl true
  def init(state) do
    # Schedule retry processing and transaction checking
    schedule_retry_processing()
    schedule_transaction_check()
    {:ok, state}
  end

  @impl true
  def handle_cast({:process_callback, callback_type, payload}, state) do
    # Get all active callbacks for this type
    callbacks = get_active_callbacks(callback_type)

    # Process each callback
    Enum.each(callbacks, fn callback ->
      process_single_callback(callback, payload)
    end)

    {:noreply, state}
  end

  @impl true
  def handle_info(:process_retries, state) do
    process_pending_retries()
    schedule_retry_processing()
    {:noreply, state}
  end

  @impl true
  def handle_info(:check_transactions, state) do
    process_pending_transactions()
    schedule_transaction_check()
    {:noreply, state}
  end

  # Private Functions

  defp get_active_callbacks(callback_type) do
    CallbackRegistry
    |> where([c], c.status == "active" and c.callback_type == ^callback_type)
    |> Repo.all()
  end

  defp process_single_callback(callback, payload) do
    Logger.info(
      "Starting callback processing for callback_id: #{callback.id}, type: #{callback.callback_type}"
    )

    # Format payload with all available fields
    Logger.info("Formatting payload with all fields for callback_id: #{callback.id}")
    formatted_payload = format_payload(callback.callback_type, payload)

    # Build request payload using validation scheme
    mapped_data =
      case callback.validation_scheme do
        scheme when is_map(scheme) and map_size(scheme) > 0 ->
          Logger.info("Building request structure using schema for callback_id: #{callback.id}")

          case CallbackSchemaValidator.validate_and_map(
                 formatted_payload,
                 scheme,
                 callback.callback_type
               ) do
            {:ok, mapped} ->
              Logger.info("Successfully built request payload for callback_id: #{callback.id}")
              mapped

            {:error, reason} ->
              Logger.error(
                "Failed to build request payload for callback #{callback.id}: #{inspect(reason)}"
              )

              # Create error callback record
              %Callback{
                callback_type: callback.callback_type,
                callback_url: callback.callback_url,
                request_headers: %{},
                request_body: formatted_payload,
                status: "failed",
                callback_registry_id: callback.id,
                error_message: "Invalid callback schema: #{inspect(reason)}",
                response_body: %{"error" => "Invalid callback schema: #{inspect(reason)}"},
                metadata: %{
                  "completed_at" => DateTime.utc_now() |> DateTime.to_iso8601(),
                  "failure_reason" => "Schema validation failed",
                  "error_details" => inspect(reason)
                }
              }
              |> Repo.insert!()

              raise "Invalid callback schema: #{inspect(reason)}"
          end

        _ ->
          Logger.error("No validation schema found for callback_id: #{callback.id}")

          # Create error callback record
          %Callback{
            callback_type: callback.callback_type,
            callback_url: callback.callback_url,
            request_headers: %{},
            request_body: formatted_payload,
            status: "failed",
            callback_registry_id: callback.id,
            error_message: "Callback schema is required",
            response_body: %{"error" => "Callback schema is required"},
            metadata: %{
              "completed_at" => DateTime.utc_now() |> DateTime.to_iso8601(),
              "failure_reason" => "Missing schema",
              "error_details" => "No validation schema found"
            }
          }
          |> Repo.insert!()

          raise "Callback schema is required"
      end

    # Wrap mapped data in event structure
    final_payload = %{
      "event_type" => callback.callback_type,
      "timestamp" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "data" => mapped_data,
      "metadata" => %{
        "attempt" => 1,
        "callback_id" => callback.id
      }
    }

    # Create initial callback record
    Logger.info("Creating callback record for callback_id: #{callback.id}")

    callback_record =
      %Callback{
        callback_type: callback.callback_type,
        callback_url: callback.callback_url,
        # Will be updated after API key validation
        request_headers: %{},
        request_body: final_payload,
        status: "pending",
        callback_registry_id: callback.id
      }
      |> Repo.insert!()

    # Validate API key
    Logger.info("Validating API key for callback_id: #{callback.id}")

    case ThirdPartyApiKey.validate_api_key(callback.api_key) do
      {:ok, _api_key} ->
        Logger.info("API key validation successful for callback_id: #{callback.id}")

        headers =
          Map.merge(
            %{
              "Content-Type" => "application/json",
              "X-API-Key" => callback.api_key
            },
            callback.headers || %{}
          )

        # Add signature if secret key is present
        headers =
          case callback.secret_key do
            nil -> headers
            key -> Map.put(headers, "X-Signature", generate_signature(final_payload, key))
          end

        # Update callback record with validated headers
        callback_record =
          callback_record
          |> Ecto.Changeset.change(%{request_headers: headers})
          |> Repo.update!()

        process_validated_callback(callback, callback_record, headers, final_payload)

      {:error, reason} ->
        # API key validation failed
        Logger.error("Invalid API key for callback #{callback.id}: #{inspect(reason)}")

        callback_record
        |> Callback.changeset(%{
          status: "failed",
          error_message: "Invalid API key",
          duration_ms: 0,
          response_body: %{"error" => "Invalid API key"}
        })
        |> Repo.update!()
    end
  end

  defp process_validated_callback(callback, callback_record, headers, final_payload) do
    encoded_payload = Jason.encode!(final_payload)
    start_time = System.system_time(:millisecond)

    # Validate and prepare URL
    url =
      case validate_url(callback.callback_url) do
        {:ok, valid_url} ->
          Logger.info("Making HTTP request to #{valid_url} for callback_id: #{callback.id}")
          valid_url

        {:error, reason} ->
          Logger.error("Invalid URL for callback_id: #{callback.id}: #{reason}")
          handle_callback_error(callback_record, "Invalid URL: #{reason}", 0)
          raise "Invalid URL: #{reason}"
      end

    # Make the HTTP request
    case make_request(url, headers, encoded_payload) do
      {:ok, %{status: status, headers: resp_headers, body: resp_body}} when status in 200..299 ->
        Logger.info("Successful response (#{status}) for callback_id: #{callback.id}")

        duration = System.system_time(:millisecond) - start_time

        # Handle response body based on content type
        response_body =
          case Enum.find(resp_headers, fn {k, _} -> String.downcase(k) == "content-type" end) do
            {_, content_type} when is_binary(content_type) ->
              if String.contains?(content_type, "application/json") do
                case Jason.decode(resp_body) do
                  {:ok, decoded} -> decoded
                  {:error, _} -> %{"raw" => resp_body}
                end
              else
                %{"raw" => resp_body}
              end

            _ ->
              %{"raw" => resp_body}
          end

        # Update callback record with success
        {:ok, _updated} =
          callback_record
          |> Callback.changeset(%{
            response_status: status,
            response_headers: Map.new(resp_headers),
            response_body: response_body,
            duration_ms: duration,
            status: "success",
            metadata:
              Map.merge(callback_record.metadata || %{}, %{
                "completed_at" => DateTime.utc_now() |> DateTime.to_iso8601()
              })
          })
          |> Repo.update()

        Logger.info(
          "Callback record updated successfully: duration=#{duration}ms, status=#{status}"
        )

        # Update callback status to success
        update_callback_status(callback, :success)

        {:ok, "Callback successful"}

      {:ok, %{status: status, headers: resp_headers, body: resp_body}} ->
        Logger.error("Failed response (#{status}) for callback_id: #{callback.id}")

        duration = System.system_time(:millisecond) - start_time
        # Handle response body based on content type
        response_body =
          case Enum.find(resp_headers, fn {k, _} -> String.downcase(k) == "content-type" end) do
            {_, content_type} when is_binary(content_type) ->
              if String.contains?(content_type, "application/json") do
                case Jason.decode(resp_body) do
                  {:ok, decoded} -> decoded
                  {:error, _} -> %{"raw" => resp_body}
                end
              else
                %{"raw" => resp_body}
              end

            _ ->
              %{"raw" => resp_body}
          end

        handle_callback_error(
          callback_record,
          %{
            status: status,
            headers: resp_headers,
            body: response_body
          },
          duration
        )

      {:error, reason} ->
        duration = System.system_time(:millisecond) - start_time
        handle_callback_error(callback_record, reason, duration)
    end
  end

  defp make_request(url, headers, payload) do
    Logger.info("Creating request through RequestPool")

    case RequestPool.create_request(%{
           method: "POST",
           url: url,
           headers: headers,
           body: Jason.decode!(payload),
           name: "callback",
           reference: "callback_#{:os.system_time(:millisecond)}"
         }) do
      {:ok, request} -> wait_for_request_completion(request)
      {:error, reason} -> {:error, "Failed to create request: #{inspect(reason)}"}
    end
  end

  defp wait_for_request_completion(request, max_attempts \\ 30, interval \\ 2000) do
    Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
      case RequestPool.get_request(request.id) do
        {:ok, req} ->
          case req.status do
            "completed" ->
              {:halt,
               {:ok,
                %{
                  status: req.response["status"] || 200,
                  headers: req.response["headers"] || %{},
                  body: req.response["body"] || ""
                }}}

            "failed" ->
              {:halt, {:error, req.response}}

            _ when attempt == max_attempts ->
              {:halt, {:error, "Request timed out"}}

            _ ->
              Process.sleep(interval)
              {:cont, nil}
          end

        {:error, :not_found} ->
          {:halt, {:error, "Request not found"}}
      end
    end)
  end

  defp validate_url(url) when is_binary(url) do
    url = String.trim(url)

    case URI.parse(url) do
      %URI{scheme: scheme, host: host} when scheme in ["http", "https"] and not is_nil(host) ->
        {:ok, url}

      %URI{scheme: nil, host: nil, path: path} when is_binary(path) ->
        # No scheme, try to determine appropriate one
        new_url =
          if String.contains?(path, "localhost") do
            "http://" <> path
          else
            "https://" <> path
          end

        {:ok, new_url}

      _ ->
        {:error, "Invalid URL format"}
    end
  end

  defp validate_url(nil), do: {:error, "URL cannot be nil"}
  defp validate_url(_), do: {:error, "Invalid URL type"}

  defp handle_callback_error(callback_record, error, duration) do
    {error_message, response_body} =
      case error do
        %{status: status, body: body, headers: headers} ->
          # Handle response body based on content type
          response_body =
            case Enum.find(headers, fn {k, _} -> String.downcase(k) == "content-type" end) do
              {_, content_type} when is_binary(content_type) ->
                case Jason.decode(body) do
                  {:ok, decoded} -> decoded
                  {:error, _} -> %{"raw" => body}
                end

              _ ->
                %{"raw" => body}
            end

          {"HTTP #{status} response: #{inspect(response_body)}", response_body}

        %{message: msg} ->
          {msg, %{"error" => msg}}

        msg when is_binary(msg) ->
          {msg, %{"error" => msg}}

        other ->
          {inspect(other), %{"error" => inspect(other)}}
      end

    Logger.error("Callback error: #{error_message}")

    {:ok, _updated} =
      callback_record
      |> Callback.changeset(%{
        duration_ms: duration,
        status: "failed",
        error_message: error_message,
        response_body: response_body,
        metadata:
          Map.merge(callback_record.metadata || %{}, %{
            "completed_at" => DateTime.utc_now() |> DateTime.to_iso8601(),
            "failure_reason" => "Request error",
            "error_details" => error_message
          })
      })
      |> Repo.update()

    {:error, error_message}
  end

  defp generate_signature(payload, secret_key) do
    :crypto.mac(:hmac, :sha256, secret_key, Jason.encode!(payload))
    |> Base.encode16(case: :lower)
  end

  defp update_callback_status(callback, :success) do
    Logger.info("Updating callback status to success for callback_id: #{callback.id}")

    callback
    |> Ecto.Changeset.change(%{
      last_called_at: DateTime.utc_now(),
      retry_count: 0
    })
    |> Repo.update()
  end

  defp schedule_retry(callback, payload) do
    Logger.info(
      "Scheduling retry for callback_id: #{callback.id}, retry count: #{callback.retry_count}"
    )

    if callback.retry_count < @max_retries do
      next_retry = get_next_retry_time(callback.retry_count)

      # Store retry information in metadata
      metadata =
        Map.merge(callback.metadata || %{}, %{
          "last_payload" => payload,
          "next_retry" => next_retry
        })

      callback
      |> Ecto.Changeset.change(%{
        retry_count: callback.retry_count + 1,
        metadata: metadata
      })
      |> Repo.update()

      next_retry
    else
      Logger.error("Max retries (#{@max_retries}) reached for callback_id: #{callback.id}")
      nil
    end
  end

  defp get_next_retry_time(retry_count) do
    DateTime.utc_now()
    |> DateTime.add(Enum.at(@retry_intervals, retry_count, 60_000), :millisecond)
  end

  defp process_pending_retries do
    now = DateTime.utc_now()

    # Get callbacks that need retry
    query =
      from c in CallbackRegistry,
        where: c.status == "active" and c.retry_count > 0,
        where: fragment("(?->>'next_retry')::timestamp <= ?", c.metadata, ^now)

    Repo.all(query)
    |> Enum.each(fn callback ->
      payload = get_in(callback.metadata, ["last_payload"])
      process_single_callback(callback, payload)
    end)
  end

  defp schedule_retry_processing do
    # Check every 30 seconds
    Process.send_after(self(), :process_retries, 30_000)
  end

  defp schedule_transaction_check do
    Process.send_after(self(), :check_transactions, @check_interval)
  end

  defp process_pending_transactions do
    # Logger.info("Starting to process pending transactions")

    # Process standard transactions
    standard_transactions =
      Transaction
      |> where([t], t.callback_status == "pending")
      |> Repo.all()

    # Logger.info("Found #{length(standard_transactions)} pending standard transactions")

    Enum.each(standard_transactions, fn transaction ->
      # try do
      process_transaction(transaction)
      # rescue
      #   e ->
      #     Logger.error("Error processing standard transaction #{transaction.reference}: #{inspect(e)}")
      #     update_transaction_callback_status(transaction, "failed")
      # end
    end)

    # Process wallet transactions
    wallet_transactions =
      WalletTransactions
      |> where([t], t.callback_status == "pending")
      |> Repo.all()

    # Logger.info("Found #{length(wallet_transactions)} pending wallet transactions")

    Enum.each(wallet_transactions, fn transaction ->
      # try do
      process_transaction(transaction)
      # rescue
      #   e ->
      #     Logger.error("Error processing wallet transaction #{transaction.reference}: #{inspect(e)}")
      #     update_transaction_callback_status(transaction, "failed")
      # end
    end)
  end

  defp process_transaction(transaction) do
    Logger.info(
      "Processing transaction callback for reference: #{transaction.reference}, type: #{transaction.type}"
    )

    Logger.info("Building payload for transaction: #{transaction.reference}")

    payload = %{
      "reference" => transaction.reference,
      "transaction_id" => transaction.reference,
      "status" => transaction.status,
      "type" => transaction.type,
      "from_account" => transaction.from_account_id,
      "to_account" => transaction.to_account_id,
      "description" => transaction.description,
      "amount" => transaction.amount,
      "cbs_reference" => transaction.cbs_transaction_reference,
      "external_reference" => transaction.external_reference
    }

    # Get active callbacks for this transaction type
    callbacks = get_active_callbacks("transaction")

    Enum.each(callbacks, fn callback_registry ->
      Logger.info("Creating callback record for transaction: #{transaction.reference}")

      # Format payload with all available fields
      formatted_payload = format_payload("transaction", payload)

      # Build request payload using validation scheme
      mapped_data =
        case callback_registry.validation_scheme do
          scheme when is_map(scheme) and map_size(scheme) > 0 ->
            Logger.info("Building request structure for transaction: #{transaction.reference}")

            case CallbackSchemaValidator.validate_and_map(
                   formatted_payload,
                   scheme,
                   "transaction"
                 ) do
              {:ok, mapped} ->
                Logger.info(
                  "Successfully built request payload for transaction: #{transaction.reference}"
                )

                mapped

              {:error, reason} ->
                Logger.error(
                  "Failed to build request payload for transaction #{transaction.reference}: #{inspect(reason)}"
                )

                # Create error callback record
                %ServiceManager.Schemas.Callback{
                  callback_type: "transaction",
                  callback_url: callback_registry.callback_url,
                  request_headers: %{},
                  request_body: formatted_payload,
                  status: "failed",
                  callback_registry_id: callback_registry.id,
                  error_message: "Invalid callback schema: #{inspect(reason)}",
                  response_body: %{"error" => "Invalid callback schema: #{inspect(reason)}"},
                  metadata: %{
                    "completed_at" => DateTime.utc_now() |> DateTime.to_iso8601(),
                    "failure_reason" => "Schema validation failed",
                    "error_details" => inspect(reason),
                    "transaction_reference" => transaction.reference,
                    "transaction_type" => transaction.type
                  }
                }
                |> Repo.insert!()

                raise "Invalid callback schema: #{inspect(reason)}"
            end

          _ ->
            Logger.error("No validation schema found for transaction: #{transaction.reference}")

            # Create error callback record
            %ServiceManager.Schemas.Callback{
              callback_type: "transaction",
              callback_url: callback_registry.callback_url,
              request_headers: %{},
              request_body: formatted_payload,
              status: "failed",
              callback_registry_id: callback_registry.id,
              error_message: "Callback schema is required",
              response_body: %{"error" => "Callback schema is required"},
              metadata: %{
                "completed_at" => DateTime.utc_now() |> DateTime.to_iso8601(),
                "failure_reason" => "Missing schema",
                "error_details" => "No validation schema found",
                "transaction_reference" => transaction.reference,
                "transaction_type" => transaction.type
              }
            }
            |> Repo.insert!()

            raise "Callback schema is required"
        end

      # Wrap mapped data in event structure
      callback_info = %{
        "event_type" => "transaction",
        "timestamp" => DateTime.utc_now() |> DateTime.to_iso8601(),
        "metadata" => %{
          "attempt" => 1,
          "callback_id" => callback_registry.id,
          "transaction_reference" => transaction.reference
        }
      }

      final_payload = mapped_data |> Map.put("callback", callback_info)

      # Prepare headers with API key and signature if present
      headers =
        Map.merge(
          %{
            "Content-Type" => "application/json",
            "X-API-Key" => callback_registry.api_key
          },
          callback_registry.headers || %{}
        )

      # Add signature if secret key is present
      headers =
        case callback_registry.secret_key do
          nil -> headers
          key -> Map.put(headers, "X-Signature", generate_signature(final_payload, key))
        end

      # Validate callback URL
      case validate_url(callback_registry.callback_url) do
        {:ok, callback_url} ->
          Logger.info("Using callback URL: #{callback_url}")

          # Create initial callback record
          callback_record =
            %ServiceManager.Schemas.Callback{
              callback_type: "transaction",
              callback_url: callback_url,
              request_headers: headers,
              request_body: final_payload,
              status: "pending",
              callback_registry_id: callback_registry.id,
              metadata: %{
                "transaction_reference" => transaction.reference,
                "transaction_type" => transaction.type
              }
            }
            |> Repo.insert!()

          Logger.info("Sending callback for transaction: #{transaction.reference}")

          # Make the HTTP request
          start_time = System.system_time(:millisecond)
          encoded_payload = Jason.encode!(final_payload)

          case make_request(callback_url, headers, encoded_payload) do
            {:ok, %{status: status, headers: resp_headers, body: resp_body}}
            when status in 200..299 ->
              Logger.info("Callback successful for transaction: #{transaction.reference}")

              duration = System.system_time(:millisecond) - start_time

              # Handle response body based on content type
              response_body =
                case Enum.find(resp_headers, fn {k, _} -> String.downcase(k) == "content-type" end) do
                  {_, content_type} when is_binary(content_type) ->
                    case Jason.decode(resp_body |> Jason.encode!()) do
                      {:ok, decoded} -> decoded
                      {:error, _} -> %{"raw" => resp_body}
                    end

                  _ ->
                    %{"raw" => resp_body}
                end

              # Update callback record with success
              {:ok, _updated} =
                callback_record
                |> Callback.changeset(%{
                  response_status: status,
                  response_headers: Map.new(resp_headers),
                  response_body: response_body,
                  duration_ms: duration,
                  status: "success",
                  metadata:
                    Map.merge(callback_record.metadata, %{
                      "completed_at" => DateTime.utc_now() |> DateTime.to_iso8601()
                    })
                })
                |> Repo.update()

              Logger.info(
                "Callback record updated successfully: duration=#{duration}ms, status=#{status}"
              )

              # Update transaction status after successful callback
              update_transaction_callback_status(transaction, "success")

            {:ok, %{status: status, headers: resp_headers, body: resp_body}} ->
              duration = System.system_time(:millisecond) - start_time

              Logger.error(
                "Callback failed for transaction: #{transaction.reference} with status: #{status}"
              )

              # Handle response body based on content type
              response_body =
                case Enum.find(resp_headers, fn {k, _} -> String.downcase(k) == "content-type" end) do
                  {_, content_type} when is_binary(content_type) ->
                    case Jason.decode(resp_body) do
                      {:ok, decoded} -> decoded
                      {:error, _} -> %{"raw" => resp_body}
                    end

                  _ ->
                    %{"raw" => resp_body}
                end

              # Update callback record with failure
              {:ok, _updated} =
                callback_record
                |> Callback.changeset(%{
                  response_status: status,
                  response_headers: Map.new(resp_headers),
                  response_body: response_body,
                  duration_ms: duration,
                  status: "failed",
                  error_message: "HTTP #{status} response",
                  metadata:
                    Map.merge(callback_record.metadata, %{
                      "completed_at" => DateTime.utc_now() |> DateTime.to_iso8601(),
                      "failure_reason" => "Non-200 response status"
                    })
                })
                |> Repo.update()

              Logger.error("Callback failed: duration=#{duration}ms, status=#{status}")

              # Update transaction status after failed callback
              update_transaction_callback_status(transaction, "failed")

            {:error, reason} ->
              duration = System.system_time(:millisecond) - start_time

              Logger.error(
                "Callback error for transaction: #{transaction.reference}: #{inspect(reason)}"
              )

              # Update callback record with error
              {:ok, _updated} =
                callback_record
                |> Callback.changeset(%{
                  duration_ms: duration,
                  status: "failed",
                  error_message: inspect(reason),
                  response_body: %{"error" => inspect(reason)},
                  metadata:
                    Map.merge(callback_record.metadata, %{
                      "completed_at" => DateTime.utc_now() |> DateTime.to_iso8601(),
                      "failure_reason" => "Request error",
                      "error_details" => inspect(reason)
                    })
                })
                |> Repo.update()

              Logger.error("Callback error: duration=#{duration}ms, error=#{inspect(reason)}")

              # Update transaction status after failed callback
              update_transaction_callback_status(transaction, "failed")
          end

        {:error, reason} ->
          Logger.error("Invalid callback URL for transaction #{transaction.reference}: #{reason}")

          # Create error callback record
          %ServiceManager.Schemas.Callback{
            callback_type: "transaction",
            callback_url: callback_registry.callback_url,
            request_headers: %{},
            request_body: formatted_payload,
            status: "failed",
            callback_registry_id: callback_registry.id,
            error_message: "Invalid URL: #{reason}",
            response_body: %{"error" => "Invalid URL: #{reason}"},
            metadata: %{
              "completed_at" => DateTime.utc_now() |> DateTime.to_iso8601(),
              "failure_reason" => "Invalid URL",
              "error_details" => reason,
              "transaction_reference" => transaction.reference,
              "transaction_type" => transaction.type
            }
          }
          |> Repo.insert!()

          update_transaction_callback_status(transaction, "failed")
      end
    end)

    Logger.info("Finished processing transaction callback for: #{transaction.reference}")
  end

  defp update_transaction_callback_status(transaction, status) do
    Logger.info("Updating transaction #{transaction.reference} callback status to: #{status}")

    changeset =
      case transaction do
        %Transaction{} ->
          Transaction.changeset(transaction, %{callback_status: status})

        %WalletTransactions{} ->
          WalletTransactions.changeset(transaction, %{callback_status: status})
      end

    Repo.update(changeset)
  end

  # Initial payload formatting before schema validation
  defp format_payload("transaction", payload) do
    %{
      "transaction_id" => payload["reference"] || payload["transaction_id"],
      "reference" => payload["reference"] || payload["transaction_id"],
      "status" => payload["status"],
      "amount" => payload["amount"],
      "currency" => payload["currency"] || "USD",
      "narration" => payload["description"],
      "transaction_type" => payload["type"],
      "transaction_date" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "value_date" => payload["value_date"],
      "core_reference" => payload["cbs_transaction_reference"],
      "channel" => "API",
      "credit_account" => payload["to_account"],
      "debit_account" => payload["from_account"],
      "account_type" => "WALLET",
      "merchant_id" => get_in(payload, ["transaction_details", "merchant_id"]),
      "customer_name" => get_in(payload, ["transaction_details", "customer_name"]),
      "payment_method" =>
        get_in(payload, ["transaction_details", "payment_method"]) || "BANK_TRANSFER",
      "payment_reference" => payload["external_reference"]
    }
    |> Enum.reject(fn {_k, v} -> is_nil(v) end)
    |> Map.new()
  end

  defp format_payload("wallet", payload) do
    %{
      "wallet_id" => payload["reference"],
      "transaction_id" => payload["reference"],
      "reference" => payload["reference"],
      "status" => payload["status"],
      "amount" => payload["amount"],
      "currency" => payload["currency"] || "USD",
      "description" => payload["description"],
      "transaction_type" => payload["type"],
      "transaction_date" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "source_wallet" => payload["from_account"],
      "destination_wallet" => payload["to_account"],
      "merchant_id" => get_in(payload, ["transaction_details", "merchant_id"]),
      "payment_method" => get_in(payload, ["transaction_details", "payment_method"]) || "WALLET",
      "payment_reference" => payload["external_reference"],
      "metadata" => payload["transaction_details"] || %{}
    }
    |> Enum.reject(fn {_k, v} -> is_nil(v) end)
    |> Map.new()
  end

  # For other callback types, return payload as-is but remove nil values
  defp format_payload(_callback_type, payload) do
    payload
    |> Enum.reject(fn {_k, v} -> is_nil(v) end)
    |> Map.new()
  end
end
