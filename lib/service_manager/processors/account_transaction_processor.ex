defmodule ServiceManager.Processors.AccountTransactionProcessor do
  use GenServer
  alias ServiceManager.Broadcasters.LoggerBroadcaster, as: <PERSON><PERSON>
  alias ServiceManager.Repo
  alias ServiceManager.Transactions.Transaction
  alias ServiceManager.Transactions.WalletTransactions
  alias ServiceManager.Contexts.TransactionsContext
  alias ServiceManager.Pool.RequestPool
  alias ServiceManager.Processors.TransactionErrorDecoder
  alias ServiceManager.Cache.ConfigCache
  alias ServiceManager.Services.T24.Messages.{CreateTransaction, OtherBankTransfer}
  alias ServiceManager.Services.BalanceSnapshotService

  # 1 second
  @poll_interval 1000

  def start_link(_opts) do
    Logger.info("Starting Account Transaction Processor")
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  def init(state) do
    schedule_poll()
    {:ok, state}
  end

  def handle_info(:poll, state) do
    # Logger.debug("AccountTransactionProcessor: Starting poll cycle")
    process_pending_transactions()
    schedule_poll()
    # Logger.debug("AccountTransactionProcessor: Completed poll cycle")
    {:noreply, state}
  end

  defp schedule_poll do
    Process.send_after(self(), :poll, @poll_interval)
  end

  defp process_pending_transactions do
    # IO.inspect ("AccountTransactionProcessor: Fetching pending transactions")
    case TransactionsContext.get_pending_account_transactions() do
      [] ->
        # IO.inspect("AccountTransactionProcessor: No pending transactions found")
        []

      transactions ->
        # IO.inspect("AccountTransactionProcessor: Processing #{length(transactions)} transactions")
        Enum.each(transactions, &process_transaction/1)
    end
  end

  defp process_transaction(transaction) do
    Logger.info("AccountTransactionProcessor: Starting to process transaction #{transaction.id}")

    # First, get the transaction and mark it as processing
    case safe_get_transaction(transaction.id) do
      {:ok, transaction} ->
        case update_transaction_status(transaction, "processing") do
          {:ok, transaction} ->
            Logger.info(
              "AccountTransactionProcessor: Processing transaction #{transaction.id} of type #{transaction.type}"
            )

            # Create pre-transaction balance snapshots if accounts exist
            create_pre_transaction_snapshots(transaction)

            # Process the transaction outside of the DB transaction
            case process_by_type(transaction) do
              {:ok, _result} ->
                Logger.info(
                  "AccountTransactionProcessor: Successfully processed transaction #{transaction.id}"
                )

                # Create post-transaction balance snapshots
                create_post_transaction_snapshots(transaction)

                update_transaction_status(transaction, "completed")

              {:error, reason} ->
                Logger.error(
                  "AccountTransactionProcessor: Failed to process transaction #{transaction.id}: #{reason}"
                )

                update_transaction_status(transaction, "failed", reason)
            end

          {:error, reason} ->
            Logger.error(
              "AccountTransactionProcessor: Failed to update transaction status: #{reason}"
            )
        end

      {:error, :not_found} ->
        Logger.error("AccountTransactionProcessor: Transaction #{transaction.id} not found")

      {:error, error} ->
        Logger.error("Error processing transaction #{transaction.id}: #{inspect(error)}")
    end
  rescue
    e ->
      Logger.error("Error processing transaction #{transaction.id}: #{inspect(e)}")

      case safe_get_transaction(transaction.id) do
        {:ok, transaction} ->
          update_transaction_status(transaction, "failed", "Internal processing error")

        _ ->
          Logger.error("Could not update transaction status - transaction not found")
      end
  end

  defp process_by_type(%{type: "external-transfer"} = transaction) do
    IO.inspect("External-Transaction Found")
    # Get account numbers directly from transaction
    from_account = %{account_number: transaction.sender_account, currency: "MWK"}
    to_account = %{account_number: transaction.receiver_account, currency: "MWK"}

    Logger.info(
      "Processing External transfer from #{from_account.account_number} to #{to_account.account_number}"
    )

    create_external_transfer_request(from_account, to_account, transaction)
  end

  defp process_by_type(%{type: "direct-transfer"} = transaction) do
    IO.inspect("Direct-Transaction Found")
    # Get account numbers directly from transaction
    from_account = %{account_number: transaction.sender_account, currency: "MWK"}
    to_account = %{account_number: transaction.receiver_account, currency: "MWK"}

    Logger.info(
      "Processing minimal transfer from #{from_account.account_number} to #{to_account.account_number}"
    )

    create_transfer_request(from_account, to_account, transaction)
  end

  defp process_by_type(%{type: "transfer"} = transaction) do
    # Get account existence flags from transaction details
    from_account_exists =
      Map.get(transaction.transaction_details, "from_account_exists", true) |> IO.inspect()

    to_account_exists =
      Map.get(transaction.transaction_details, "to_account_exists", true) |> IO.inspect()

    Logger.info(
      "Processing transfer with account existence - From: #{from_account_exists}, To: #{to_account_exists}"
    )

    cond do
      # Both accounts exist - proceed with normal transfer
      from_account_exists and to_account_exists ->
        process_normal_transfer(transaction)

      # Only destination account exists - credit that account
      not from_account_exists and to_account_exists ->
        process_credit_only_transfer(transaction)

      # Only source account exists - debit that account
      from_account_exists and not to_account_exists ->
        process_debit_only_transfer(transaction)

      # Neither account exists - create direct request
      true ->
        process_direct_request(transaction)
    end
  end

  defp process_normal_transfer(transaction) do
    with {:ok, from_account} <- ensure_account_loaded(transaction.from_account),
         {:ok, to_account} <- ensure_account_loaded(transaction.to_account) do
      if from_account.account_number == to_account.account_number do
        {:error, "Cannot transfer to the same account"}
      else
        create_transfer_request(from_account, to_account, transaction)
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp process_credit_only_transfer(transaction) do
    with {:ok, to_account} <- ensure_account_loaded(transaction.to_account) do
      # Use system account as source for crediting
      case ConfigCache.get("CREDIT-SUSPENSE-ACCOUNT") do
        {:ok, suspense_account} ->
          create_transfer_request(
            %{account_number: suspense_account, currency: to_account.currency},
            to_account,
            transaction
          )

        {:error, :not_found} ->
          Logger.error("CREDIT-SUSPENSE-ACCOUNT config not found")
          {:error, "System account not configured"}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp process_debit_only_transfer(transaction) do
    with {:ok, from_account} <- ensure_account_loaded(transaction.from_account) do
      # Use system account as destination for debiting
      case ConfigCache.get("DEBIT-SUSPENSE-ACCOUNT") do
        {:ok, suspense_account} ->
          create_transfer_request(
            from_account,
            %{account_number: suspense_account, currency: from_account.currency},
            transaction
          )

        {:error, :not_found} ->
          Logger.error("DEBIT-SUSPENSE-ACCOUNT config not found")
          {:error, "System account not configured"}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp process_external_direct_request(transaction) do
    # Create direct request using just account numbers
    create_external_transfer_request(
      %{account_number: transaction.from_account, currency: "MWK"},
      %{account_number: transaction.to_account, currency: "MWK"},
      transaction
    )
  end

  defp process_direct_request(transaction) do
    # Create direct request using just account numbers
    create_transfer_request(
      %{account_number: transaction.from_account, currency: "MWK"},
      %{account_number: transaction.to_account, currency: "MWK"},
      transaction
    )
  end

  defp create_external_transfer_request(from_account, to_account, transaction) do
    case OtherBankTransfer.execute(from_account, to_account, transaction) do
      {:ok, request} -> wait_for_request_completion(request, transaction)
      {:error, reason} -> {:error, "Failed to initiate transfer: #{inspect(reason)}"}
    end
  end

  defp create_transfer_request(from_account, to_account, transaction) do
    case CreateTransaction.execute(from_account, to_account, transaction) do
      {:ok, request} -> wait_for_request_completion(request, transaction)
      {:error, reason} -> {:error, "Failed to initiate transfer: #{inspect(reason)}"}
    end
  end

  defp process_by_type(%{type: "credit"} = transaction) do
    # Handle credit operation as a transfer from system account
    with {:ok, to_account} <- ensure_account_loaded(transaction.to_account) do
      # Initiate transfer from system account
      case CreateTransaction.execute_credit(to_account, transaction) do
        {:ok, request} -> wait_for_request_completion(request, transaction)
        {:error, reason} -> {:error, "Failed to initiate credit: #{inspect(reason)}"}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp process_by_type(%{type: "debit"} = transaction) do
    # Handle debit operation as a transfer to system account
    with {:ok, from_account} <- ensure_account_loaded(transaction.from_account) do
      # Initiate transfer to system account
      case CreateTransaction.execute_debit(from_account, transaction) do
        {:ok, request} -> wait_for_request_completion(request, transaction)
        {:error, reason} -> {:error, "Failed to initiate debit: #{inspect(reason)}"}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp safe_get_transaction(id) do
    try do
      transaction =
        Transaction
        |> Repo.get(id)
        |> case do
          nil ->
            {:error, :not_found}

          transaction ->
            transaction = Repo.preload(transaction, [:from_account, :to_account])
            {:ok, transaction}
        end

      transaction
    rescue
      e -> {:error, e}
    end
  end

  defp ensure_account_loaded(%{account_number: _} = account), do: {:ok, account}
  defp ensure_account_loaded(_), do: {:error, "Account not properly loaded"}

  defp update_wallet_transaction(transaction, cbs_ref, external_ref) do
    case Repo.get_by(WalletTransactions, reference: transaction.reference) do
      nil ->
        Logger.info("No wallet transaction found with reference: #{transaction.reference}")
        {:ok, :not_found}

      wallet_transaction ->
        case Repo.update(
               WalletTransactions.changeset(wallet_transaction, %{
                 status: "completed",
                 cbs_transaction_reference: cbs_ref,
                 external_reference: external_ref
               })
             ) do
          {:ok, updated} ->
            {:ok, updated}

          {:error, changeset} ->
            Logger.error(
              "Failed to update wallet transaction references: #{inspect(changeset.errors)}"
            )

            {:error, "Failed to update wallet transaction references"}
        end
    end
  end

  defp update_wallet_transaction_error_details(transaction_reference, error_attrs) do
    case Repo.get_by(WalletTransactions, reference: transaction_reference) do
      nil ->
        Logger.info("No wallet transaction found with reference: #{transaction_reference}")
        {:ok, :not_found}

      wallet_transaction ->
        attrs = %{
          status: "failed",
          failure_reason: error_attrs.error_description,
          transaction_details: error_attrs.transaction_details
        }

        case Repo.update(WalletTransactions.changeset(wallet_transaction, attrs)) do
          {:ok, updated} ->
            {:ok, updated}

          {:error, changeset} ->
            Logger.error(
              "Failed to update wallet transaction error details: #{inspect(changeset.errors)}"
            )

            {:error, "Failed to update wallet transaction error details"}
        end
    end
  end

  defp wait_for_request_completion(request, transaction, max_attempts \\ 30, interval \\ 2000) do
    Enum.reduce_while(1..max_attempts, nil, fn attempt, _ ->
      case RequestPool.get_request(request.id) do
        {:ok, req} ->
          case req.status do
            "completed" ->
              # Extract references from response
              cbs_ref = get_in(req.response, ["body", "header", "id"])
              external_ref = get_in(req.response, ["body", "header", "uniqueIdentifier"])

              # Update both transactions with references
              update_transaction_status(transaction, "completed", nil, cbs_ref, external_ref)
              update_wallet_transaction(transaction, cbs_ref, external_ref)

              {:halt, {:ok, req.response}}

            "failed" ->
              Logger.info(
                "AccountTransactionProcessor: Failed request response: #{inspect(req.response)}"
              )

              try do
                {error_type, error_data} = TransactionErrorDecoder.decode_t24_error(req.response)

                error_attrs =
                  TransactionErrorDecoder.format_error_for_storage({error_type, error_data})

                # Update both transactions with error details
                update_transaction_error_details(transaction, error_attrs)
                update_wallet_transaction_error_details(transaction.reference, error_attrs)

                {:halt, {:error, error_data.description}}
              rescue
                e ->
                  Logger.error(
                    "AccountTransactionProcessor: Error decoding response: #{inspect(e)}\nResponse: #{inspect(req.response)}"
                  )

                  error_message = "Error processing T24 response"
                  # Update both transactions with generic error
                  update_transaction_status(transaction, "failed", error_message)

                  update_wallet_transaction_error_details(transaction.reference, %{
                    error_description: error_message,
                    transaction_details: %{"error" => error_message}
                  })

                  {:halt, {:error, error_message}}
              end

            _ when attempt == max_attempts ->
              error_message = "Request timed out"
              # Update both transactions for timeout
              update_transaction_status(transaction, "failed", error_message)

              update_wallet_transaction_error_details(transaction.reference, %{
                error_description: error_message,
                transaction_details: %{"error" => error_message}
              })

              {:halt, {:error, error_message}}

            _ ->
              Process.sleep(interval)
              {:cont, nil}
          end

        {:error, :not_found} ->
          error_message = "Request not found"
          # Update both transactions when request is not found
          update_transaction_status(transaction, "failed", error_message)

          update_wallet_transaction_error_details(transaction.reference, %{
            error_description: error_message,
            transaction_details: %{"error" => error_message}
          })

          {:halt, {:error, error_message}}
      end
    end)
  end

  defp update_transaction_error_details(transaction, error_attrs) do
    attrs = %{
      status: "failed",
      failure_reason: error_attrs.error_description,
      transaction_details: error_attrs.transaction_details
    }

    case Repo.update(Transaction.changeset(transaction, attrs)) do
      {:ok, updated} ->
        {:ok, updated}

      {:error, changeset} ->
        Logger.error("Failed to update transaction error details: #{inspect(changeset.errors)}")
        {:error, "Failed to update transaction error details"}
    end
  end

  defp update_transaction_status(
         transaction,
         status,
         reason \\ nil,
         cbs_ref \\ nil,
         external_ref \\ nil
       ) do
    attrs =
      %{status: status}
      |> maybe_add_failure_reason(reason)
      |> maybe_add_cbs_reference(cbs_ref)
      |> maybe_add_external_reference(external_ref)

    case Repo.update(Transaction.changeset(transaction, attrs)) do
      {:ok, updated} ->
        {:ok, updated}

      {:error, changeset} ->
        Logger.error("Failed to update transaction status: #{inspect(changeset.errors)}")
        {:error, "Failed to update transaction status"}
    end
  end

  defp maybe_add_failure_reason(attrs, nil), do: attrs
  defp maybe_add_failure_reason(attrs, reason), do: Map.put(attrs, :failure_reason, reason)

  defp maybe_add_cbs_reference(attrs, nil), do: attrs

  defp maybe_add_cbs_reference(attrs, cbs_ref),
    do: Map.put(attrs, :cbs_transaction_reference, cbs_ref)

  defp maybe_add_external_reference(attrs, nil), do: attrs

  defp maybe_add_external_reference(attrs, external_ref),
    do: Map.put(attrs, :external_reference, external_ref)

  @doc """
  Creates pre-transaction balance snapshots for the accounts involved in a transaction.
  """
  defp create_pre_transaction_snapshots(transaction) do
    # Create snapshot for from_account if it exists
    if transaction.from_account_id do
      BalanceSnapshotService.create_pre_transaction_snapshot(
        transaction.from_account_id,
        transaction.id,
        "transaction_#{transaction.type}"
      )
    end

    # Create snapshot for to_account if it exists
    if transaction.to_account_id do
      BalanceSnapshotService.create_pre_transaction_snapshot(
        transaction.to_account_id,
        transaction.id,
        "transaction_#{transaction.type}"
      )
    end

    # Create snapshot for from_wallet if it exists
    if transaction.from_wallet_id do
      BalanceSnapshotService.create_pre_wallet_transaction_snapshot(
        transaction.from_wallet_id,
        transaction.id,
        "transaction_#{transaction.type}"
      )
    end

    # Create snapshot for to_wallet if it exists
    if transaction.to_wallet_id do
      BalanceSnapshotService.create_pre_wallet_transaction_snapshot(
        transaction.to_wallet_id,
        transaction.id,
        "transaction_#{transaction.type}"
      )
    end
  end

  @doc """
  Creates post-transaction balance snapshots for the accounts involved in a transaction.
  """
  defp create_post_transaction_snapshots(transaction) do
    # Create snapshot for from_account if it exists
    if transaction.from_account_id do
      BalanceSnapshotService.create_post_transaction_snapshot(
        transaction.from_account_id,
        transaction.id,
        "transaction_#{transaction.type}"
      )
    end

    # Create snapshot for to_account if it exists
    if transaction.to_account_id do
      BalanceSnapshotService.create_post_transaction_snapshot(
        transaction.to_account_id,
        transaction.id,
        "transaction_#{transaction.type}"
      )
    end

    # Create snapshot for from_wallet if it exists
    if transaction.from_wallet_id do
      BalanceSnapshotService.create_post_wallet_transaction_snapshot(
        transaction.from_wallet_id,
        transaction.id,
        "transaction_#{transaction.type}"
      )
    end

    # Create snapshot for to_wallet if it exists
    if transaction.to_wallet_id do
      BalanceSnapshotService.create_post_wallet_transaction_snapshot(
        transaction.to_wallet_id,
        transaction.id,
        "transaction_#{transaction.type}"
      )
    end
  end
end
