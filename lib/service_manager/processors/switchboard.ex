defmodule ServiceManager.Processors.Switchboard do
  use GenServer
  import Ecto.Query
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Switchboard.FeatureFlag
  require Logger

  @ets_table :feature_flags
  @pubsub_topic "feature_flags"

  def start_link(_) do
    GenServer.start_link(__MODULE__, [], name: __MODULE__)
  end

  def init(_) do
    case create_ets_table() do
      {:ok, table} ->
        load_features_into_ets()
        {:ok, %{table: table, initialized: true}}

      {:error, reason} ->
        Logger.error("Failed to create ETS table: #{inspect(reason)}")
        {:stop, :ets_init_failed}
    end
  end

  def ready?() do
    case :ets.info(@ets_table) do
      :undefined -> false
      _ -> true
    end
  end

  # Public API

  @doc """
  Check if a feature is enabled.
  Returns true if the feature is enabled, false otherwise.
  """
  def feature_enabled?(name, node \\ Node.self()) when is_atom(name) or is_binary(name) do
    name = to_string(name)
    node_name = to_string(node)

    case :ets.lookup(@ets_table, {name, node_name}) do
      [{_, flag}] ->
        flag.enabled && parse_bool_value(flag.value || flag.default_value)

      [] ->
        try do
          case Repo.get_by(FeatureFlag, name: name, node_name: node_name) do
            %FeatureFlag{} = flag ->
              flag.enabled && parse_bool_value(flag.default_value)

            nil ->
              false
          end
        rescue
          _ -> false
        end
    end
  end

  @doc """
  Register a new feature with optional default value and category.
  """
  def register_feature(name, opts \\ []) when is_atom(name) or is_binary(name) do
    name = to_string(name)
    default_value = Keyword.get(opts, :default, false)
    description = Keyword.get(opts, :description, "")
    category = Keyword.get(opts, :category, "general")
    value_type = Keyword.get(opts, :value_type, get_value_type(default_value))
    node_name = to_string(Node.self())

    attrs = %{
      name: name,
      description: description,
      category: category,
      default_value: stringify_value(default_value),
      value: stringify_value(default_value),
      enabled: is_boolean(default_value) && default_value,
      registered: true,
      value_type: value_type,
      node_name: node_name
    }

    with {:ok, flag} <- create_or_update_flag(attrs) do
      update_ets(flag)
      broadcast_change(flag)
      {:ok, flag}
    end
  end

  @doc """
  Toggle a feature's enabled status.
  """
  def toggle_feature(name, value, node \\ Node.self())
      when (is_atom(name) or is_binary(name)) and is_boolean(value) do
    name = to_string(name)
    node_name = to_string(node)

    try do
      case Repo.get_by(FeatureFlag, name: name, node_name: node_name) do
        nil ->
          {:error, :not_found}

        flag ->
          {:ok, updated_flag} =
            flag
            |> FeatureFlag.changeset(%{enabled: value})
            |> Repo.update()

          update_ets(updated_flag)
          broadcast_change(updated_flag)
          {:ok, updated_flag}
      end
    rescue
      _ -> {:error, :not_found}
    end
  end

  @doc """
  Get all features for a specific category.
  """
  def get_features_by_category(category, node \\ Node.self()) when is_binary(category) do
    node_name = to_string(node)
    query = from(f in FeatureFlag, where: f.category == ^category and f.node_name == ^node_name)
    Repo.all(query)
  end

  @doc """
  Get all features for a specific node.
  """
  def get_all_features(node \\ Node.self()) do
    node_name = to_string(node)
    Repo.all(from f in FeatureFlag, where: f.node_name == ^node_name)
  end

  @doc """
  List all available nodes that have registered features.
  """
  def list_nodes do
    query =
      from f in FeatureFlag,
        select: f.node_name,
        distinct: true,
        order_by: f.node_name

    Repo.all(query)
  end

  @doc """
  Get metrics about features for a specific node.
  """
  def get_metrics(node \\ Node.self()) do
    features = get_all_features(node)

    %{
      total_features: length(features),
      enabled_count: Enum.count(features, & &1.enabled),
      disabled_count: Enum.count(features, &(not &1.enabled)),
      unregistered_count: Enum.count(features, &(not &1.registered)),
      categories_count: features |> Enum.map(& &1.category) |> Enum.uniq() |> length()
    }
  end

  @doc """
  Get the integer value of a feature flag.
  Returns the value if it exists and is an integer, otherwise returns the default.
  """
  def get_integer_value(name, default \\ 0, node \\ Node.self())

  def get_integer_value(name, default, node) when is_atom(name) or is_binary(name) do
    name = to_string(name)
    node_name = to_string(node)

    case :ets.lookup(@ets_table, {name, node_name}) do
      [{_, %{value_type: "integer"} = flag}] ->
        if flag.enabled do
          parse_int_value(flag.value || flag.default_value, default)
        else
          default
        end

      _ ->
        case Repo.get_by(FeatureFlag, name: name, node_name: node_name) do
          %FeatureFlag{value_type: "integer"} = flag ->
            if flag.enabled do
              parse_int_value(flag.default_value, default)
            else
              default
            end

          _ ->
            default
        end
    end
  end

  @doc """
  Update the value of a feature flag.
  """
  def update_value(name, value, node \\ Node.self()) when is_atom(name) or is_binary(name) do
    name = to_string(name)
    node_name = to_string(node)

    case Repo.get_by(FeatureFlag, name: name, node_name: node_name) do
      nil ->
        {:error, :not_found}

      flag ->
        {:ok, updated_flag} =
          flag
          |> FeatureFlag.changeset(%{value: stringify_value(value)})
          |> Repo.update()

        update_ets(updated_flag)
        broadcast_change(updated_flag)
        {:ok, updated_flag}
    end
  end

  # Private functions

  defp create_ets_table do
    try do
      table = :ets.new(@ets_table, [:set, :named_table, :public, read_concurrency: true])
      {:ok, table}
    rescue
      error -> {:error, error}
    end
  end

  defp create_or_update_flag(attrs) do
    case Repo.get_by(FeatureFlag, name: attrs.name, node_name: attrs.node_name) do
      nil ->
        %FeatureFlag{}
        |> FeatureFlag.changeset(attrs)
        |> Repo.insert()

      flag ->
        flag
        |> FeatureFlag.changeset(attrs)
        |> Repo.update()
    end
  end

  defp load_features_into_ets do
    try do
      FeatureFlag
      |> Repo.all()
      |> Enum.each(&update_ets/1)
    rescue
      _ -> []
    end
  end

  defp update_ets(%FeatureFlag{} = flag) do
    :ets.insert(@ets_table, {{flag.name, flag.node_name}, flag})
  end

  defp broadcast_change(flag) do
    # Broadcast to LiveView subscribers
    Phoenix.PubSub.broadcast(
      ServiceManager.PubSub,
      @pubsub_topic,
      {:feature_updated, flag}
    )

    # Notify supervisor to handle process lifecycle with node info
    ServiceManager.Processors.Supervisor.handle_feature_change(flag.name, flag.node_name)
  end

  defp parse_bool_value("true"), do: true
  defp parse_bool_value("false"), do: false
  defp parse_bool_value(true), do: true
  defp parse_bool_value(false), do: false
  defp parse_bool_value(_), do: false

  defp parse_int_value(value, default) when is_binary(value) do
    case Integer.parse(value) do
      {int, ""} -> int
      _ -> default
    end
  end

  defp parse_int_value(value, _default) when is_integer(value), do: value
  defp parse_int_value(_, default), do: default

  defp stringify_value(value) when is_boolean(value), do: to_string(value)
  defp stringify_value(value) when is_integer(value), do: to_string(value)
  defp stringify_value(value) when is_binary(value), do: value
  defp stringify_value(_), do: "false"

  defp get_value_type(value) when is_boolean(value), do: "boolean"
  defp get_value_type(value) when is_integer(value), do: "integer"
  defp get_value_type(_), do: "boolean"
end
