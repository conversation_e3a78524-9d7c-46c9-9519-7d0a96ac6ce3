defmodule ServiceManager.Processors.Initializer do
  use GenServer
  require <PERSON><PERSON>
  alias ServiceManager.Processors.Switchboard

  @supervisor_flags [
    {:supervisor_max_restarts, "Maximum restarts allowed", "supervisor", 3, "integer"},
    {:supervisor_max_seconds, "Time window for max restarts", "supervisor", 5, "integer"},
    {:supervisor_auto_restart, "Auto-restart failed services", "supervisor", true, "boolean"}
  ]

  @processor_flags [
    {:account_processor_enabled, "Enable Account Transaction Processor", "processors", true,
     "boolean"},
    {:wallet_processor_enabled, "Enable Wallet Transaction Processor", "processors", true,
     "boolean"},
    # {:balance_sync_enabled, "Enable User Balance Sync", "processors", true, "boolean"},
    {:callback_processor_enabled, "Enable Callback Processor", "processors", true, "boolean"}
  ]

  @notification_flags [
    {:sms_worker_enabled, "Enable SMS Notification Worker", "notifications", true, "boolean"},
    {:notification_dispatcher_enabled, "Enable Notification Dispatcher", "notifications", true,
     "boolean"}
  ]

  @callback_flags [
    {:merchant_callback_enabled, "Enable Merchant Callback Worker", "callbacks", true, "boolean"},
    {:transfer_callback_enabled, "Enable Transfer Callback Server", "callbacks", true, "boolean"}
  ]

  @cache_flags [
    {:route_cache_enabled, "Enable Route Cache", "cache", true, "boolean"},
    {:config_cache_enabled, "Enable Config Cache", "cache", true, "boolean"},
    {:ip_whitelist_cache_enabled, "Enable IP Whitelist Cache", "cache", true, "boolean"},
    {:statistics_cache_enabled, "Enable Statistics Cache", "cache", true, "boolean"},
    {:otp_cache_enabled, "Enable OTP Cache", "cache", true, "boolean"},
    {:settings_cache_enabled, "Enable Settings Cache", "cache", true, "boolean"}
  ]

  def start_link(_) do
    GenServer.start_link(__MODULE__, [], name: __MODULE__)
  end

  def init(_) do
    # Schedule initialization after a short delay
    Process.send_after(self(), :initialize, 1000)
    {:ok, %{initialized: false}}
  end

  def handle_info(:initialize, state) do
    if Switchboard.ready?() do
      register_feature_flags()
      start_enabled_processors()
      {:noreply, %{state | initialized: true}}
    else
      # Retry after a delay
      Process.send_after(self(), :initialize, 1000)
      {:noreply, state}
    end
  end

  # Private functions

  defp register_feature_flags do
    # Helper function to register flags
    register_flags = fn flags, type ->
      Enum.each(flags, fn {name, description, category, default, value_type} ->
        case Switchboard.register_feature(
               name,
               description: description,
               category: category,
               default: default,
               value_type: value_type
             ) do
          {:ok, _} -> :ok
          error -> Logger.error("Failed to register #{type} flag #{name}: #{inspect(error)}")
        end
      end)
    end

    # Register all flag types
    register_flags.(@supervisor_flags, "supervisor")
    register_flags.(@processor_flags, "processor")
    register_flags.(@notification_flags, "notification")
    register_flags.(@callback_flags, "callback")
    register_flags.(@cache_flags, "cache")
  end

  defp start_enabled_processors do
    # Helper function to start processors of a specific type
    start_processors = fn processors, type ->
      Logger.info("Starting #{type} processors...")
      Enum.each(processors, &maybe_start_processor/1)
    end

    # Start all processor types
    start_processors.(
      [
        {ServiceManager.Processors.AccountTransactionProcessor, :account_processor_enabled},
        {ServiceManager.Processors.WalletTransactionProcessor, :wallet_processor_enabled},
        # {ServiceManager.Processors.UserBalanceSyncProcessor, :balance_sync_enabled},
        {ServiceManager.Processors.CallbackProcessor, :callback_processor_enabled}
      ],
      "regular"
    )

    start_processors.(
      [
        {ServiceManager.Services.Notification.SMSWorker, :sms_worker_enabled},
        {ServiceManager.Notifications.Dispatcher, :notification_dispatcher_enabled}
      ],
      "notification"
    )

    start_processors.(
      [
        {ServiceManager.Services.MerchantCallbackWorker, :merchant_callback_enabled},
        {ServiceManager.Services.TransferCallbackServer, :transfer_callback_enabled}
      ],
      "callback"
    )

    start_processors.(
      [
        {ServiceManager.Cache.RouteCache, :route_cache_enabled},
        {ServiceManager.Cache.ConfigCache, :config_cache_enabled},
        {ServiceManager.Cache.IpWhitelistCache, :ip_whitelist_cache_enabled},
        {ServiceManager.Statistics.Cache, :statistics_cache_enabled},
        {{ServiceManager.Cache.CacheWrapper, name: :otp_cache}, :otp_cache_enabled},
        {{ServiceManager.Cache.CacheWrapper, name: :settings}, :settings_cache_enabled}
      ],
      "cache"
    )
  end

  defp maybe_start_processor({module, flag}) do
    flag_name = Atom.to_string(flag)
    current_node = Node.self()
    current_node_name = to_string(current_node)
    current_pid = Agent.get(ServiceManager.Processors.Supervisor.State, &Map.get(&1, flag_name))
    is_enabled = Switchboard.feature_enabled?(flag, current_node)

    Logger.info("🔍 Checking service state on startup:")
    Logger.info("   - Module: #{module}")
    Logger.info("   - Flag: #{flag_name}")
    Logger.info("   - Node: #{current_node_name}")
    Logger.info("   - Enabled: #{is_enabled}")
    Logger.info("   - Current PID: #{inspect(current_pid)}")

    cond do
      # Already running
      current_pid != nil ->
        Logger.info("✅ Service already running:")
        Logger.info("   - Module: #{module}")
        Logger.info("   - Node: #{current_node_name}")
        Logger.info("   - PID: #{inspect(current_pid)}")

      # Should be running but isn't
      is_enabled ->
        Logger.info("🚀 Initializing service:")
        Logger.info("   - Module: #{module}")
        Logger.info("   - Node: #{current_node_name}")

        case DynamicSupervisor.start_child(ServiceManager.DynamicSupervisor, module) do
          {:ok, pid} ->
            Agent.update(ServiceManager.Processors.Supervisor.State, &Map.put(&1, flag_name, pid))
            Logger.info("✅ Service initialized successfully:")
            Logger.info("   - Module: #{module}")
            Logger.info("   - Node: #{current_node_name}")
            Logger.info("   - PID: #{inspect(pid)}")

          {:error, {:already_started, pid}} ->
            Logger.info("✅ Service was already started:")
            Logger.info("   - Module: #{module}")
            Logger.info("   - Node: #{current_node_name}")
            Logger.info("   - PID: #{inspect(pid)}")
            # Update state to track the existing PID
            Agent.update(ServiceManager.Processors.Supervisor.State, &Map.put(&1, flag_name, pid))

          {:error, reason} ->
            Logger.error("❌ Service failed to initialize:")
            Logger.error("   - Module: #{module}")
            Logger.error("   - Node: #{current_node_name}")
            Logger.error("   - Error: #{inspect(reason)}")
        end

      # Should not be running and isn't
      true ->
        Logger.info("ℹ️ Service disabled by feature flag:")
        Logger.info("   - Module: #{module}")
        Logger.info("   - Node: #{current_node_name}")
    end
  end
end
