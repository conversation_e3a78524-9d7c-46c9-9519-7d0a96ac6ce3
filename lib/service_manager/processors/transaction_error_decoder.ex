defmodule ServiceManager.Processors.TransactionErrorDecoder do
  @moduledoc """
  Centralized module for decoding and handling transaction errors from T24 and other sources.
  """

  @doc """
  Decodes T24 API error responses and returns a structured error format.
  """
  def decode_t24_error(response) do
    case response do
      %{"error" => error_string} when is_binary(error_string) ->
        # Handle double-encoded JSON error
        case Jason.decode(error_string) do
          {:ok, decoded_error} -> decode_error_body(decoded_error)
          {:error, _} -> decode_error_body(error_string)
        end

      %{"error" => error} when is_map(error) ->
        decode_error_body(error)

      %{"header" => %{"status" => "failed"}} = error ->
        # Direct error response without error wrapper
        decode_error_body(error)

      error when is_binary(error) ->
        # Try to decode string error
        case Jason.decode(error) do
          {:ok, decoded} ->
            decode_t24_error(decoded)

          {:error, _} ->
            {:unknown_error,
             %{code: "INVALID_ERROR_FORMAT", description: error, details: %{raw_error: error}}}
        end

      _ ->
        {
          :unknown_t24_error,
          %{
            code: "UNKNOWN_ERROR",
            description: "Unknown T24 error format",
            details: %{raw_error: inspect(response)}
          }
        }
    end
  end

  defp decode_error_body(error) do
    cond do
      # Case 1: Override error
      has_override_details?(error) ->
        %{
          "override" => %{
            "overrideDetails" => [%{"code" => code, "description" => description} | _]
          }
        } = error

        {
          :t24_override_error,
          %{
            code: code,
            description: description,
            details: extract_error_details(error)
          }
        }

      # Case 2: Transaction error with header
      has_error_header?(error) ->
        {
          :t24_transaction_error,
          %{
            code: "TRANSACTION_FAILED",
            description: get_error_description(error),
            details: extract_error_details(error)
          }
        }

      # Case 3: Unknown format
      true ->
        {
          :unknown_t24_error,
          %{
            code: "UNKNOWN_ERROR",
            description: "Unknown T24 error format",
            details: error
          }
        }
    end
  end

  defp has_override_details?(error) do
    case error do
      %{"override" => %{"overrideDetails" => [%{"code" => _, "description" => _} | _]}} -> true
      _ -> false
    end
  end

  defp has_error_header?(error) do
    case error do
      %{"header" => %{"status" => status}} when status in ["failed", "error", "Error"] -> true
      _ -> false
    end
  end

  defp get_error_description(error) do
    cond do
      get_in(error, ["header", "errorMessage"]) ->
        get_in(error, ["header", "errorMessage"])

      get_in(error, ["header", "message"]) ->
        get_in(error, ["header", "message"])

      true ->
        "Transaction processing failed"
    end
  end

  @doc """
  Formats the error for database storage.
  """
  def format_error_for_storage({error_type, error_data}) do
    %{
      error_type: Atom.to_string(error_type),
      error_code: error_data.code,
      error_description: error_data.description,
      error_details: Jason.encode!(error_data.details),
      transaction_details: %{
        error: %{
          type: Atom.to_string(error_type),
          code: error_data.code,
          description: error_data.description,
          details: error_data.details
        }
      }
    }
  end

  # Private helper functions
  defp extract_error_details(error) do
    %{
      transaction_id: get_in(error, ["header", "id"]),
      unique_identifier: get_in(error, ["header", "uniqueIdentifier"]),
      request_body: get_in(error, ["body"]),
      processing_times: get_in(error, ["header", "audit"]),
      raw_error: error
    }
  end
end
