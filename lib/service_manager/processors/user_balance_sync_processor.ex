defmodule ServiceManager.Processors.UserBalanceSyncProcessor do
  @moduledoc """
  GenServer for synchronizing user account balances with T24 core banking system.
  """
  use GenServer
  require Logger
  import Ecto.Query

  alias ServiceManager.Accounts.User
  alias ServiceManager.Accounts.FundAccounts
  alias ServiceManager.Repo
  alias ServiceManager.Contexts.T24Context
  import ServiceManager.Logging.FunctionTracker

  # 5 minutes
  @sync_interval 300_000
  # 2 seconds
  @auth_sync_timeout 2_000

  # ETS table names
  @background_queue :balance_sync_background_queue
  @priority_queue :balance_sync_priority_queue

  # Client API

  @doc """
  Initializes the ETS tables for sync queues.
  """
  def init_ets do
    :ets.new(@background_queue, [:named_table, :public, :ordered_set])
    :ets.new(@priority_queue, [:named_table, :public, :ordered_set])
  end

  @doc """
  Synchronizes user balance with timeout for authentication.
  Returns {:ok, balance} if sync completes within timeout,
  {:background, :timeout} if it exceeds timeout.

  ServiceManager.Processors.UserBalanceSyncProcessor.sync_with_timeout(112)

  """
  def sync_with_timeout(user_id) do
    ref = make_ref()
    GenServer.cast(__MODULE__, {:priority_sync, user_id, ref, self()})

    receive do
      {:sync_complete, ^ref, balance} -> {:ok, balance}
    after
      @auth_sync_timeout ->
        # Move to background queue and continue auth
        add_to_background_queue(user_id)
        {:background, :timeout}
    end
  end

  @doc """
  Adds a sync request to the background queue.
  """
  def add_to_background_queue(user_id) do
    timestamp = System.system_time(:millisecond)
    :ets.insert(@background_queue, {timestamp, user_id})
  end

  @doc """
  Adds a sync request to the priority queue.
  """
  def add_to_priority_queue(user_id) do
    timestamp = System.system_time(:millisecond)
    :ets.insert(@priority_queue, {timestamp, user_id})
  end

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  @doc """
  Triggers an immediate balance sync for a specific user.
  """
  def sync_user(user_id) do
    GenServer.cast(__MODULE__, {:sync_user, user_id})
  end

  @doc """
  Triggers an immediate sync for all pending users.
  """
  def sync_all_pending() do
    GenServer.cast(__MODULE__, :sync_all_pending)
  end

  # Server Callbacks

  @impl true
  def init(_state) do
    init_ets()
    schedule_next_sync()
    schedule_queue_processing()
    {:ok, %{priority_syncs: %{}}}
  end

  @impl true
  def handle_cast({:priority_sync, user_id, ref, from}, state) do
    case do_sync_balance_priority(user_id) do
      {:ok, balance} ->
        send(from, {:sync_complete, ref, balance})

      {:error, _reason} ->
        # On error, move to background queue
        add_to_background_queue(user_id)
    end

    {:noreply, state}
  end

  @impl true
  def handle_cast({:sync_user, user_id}, state) do
    add_to_background_queue(user_id)

    with {:ok, user} <- fetch_user(user_id),
         {:ok, _updated_user} <- do_sync_balance(user) do
      Logger.info("Successfully synced balance for user #{user_id}")
    else
      {:error, reason} ->
        Logger.error("Failed to sync balance for user #{user_id}: #{inspect(reason)}")
    end

    {:noreply, state}
  end

  @impl true
  def handle_cast(:sync_all_pending, state) do
    User
    |> where([u], u.sync_status in ["pending", "failed"])
    |> Repo.all()
    |> Enum.each(fn user ->
      case do_sync_balance(user) do
        {:ok, _} ->
          Logger.info("Successfully synced balance for user #{user.id}")

        {:error, reason} ->
          Logger.error("Failed to sync balance for user #{user.id}: #{inspect(reason)}")
      end
    end)

    {:noreply, state}
  end

  @impl true
  def handle_info(:sync_pending, state) do
    handle_cast(:sync_all_pending, state)
    schedule_next_sync()
    {:noreply, state}
  end

  @impl true
  def handle_info(:process_queues, state) do
    process_priority_queue()
    process_background_queue()
    schedule_queue_processing()
    {:noreply, state}
  end

  # Private Functions

  defp schedule_next_sync do
    Process.send_after(self(), :sync_pending, @sync_interval)
  end

  defp schedule_queue_processing do
    Process.send_after(self(), :process_queues, 1_000)
  end

  defp process_priority_queue do
    case :ets.first(@priority_queue) do
      :"$end_of_table" ->
        :ok

      key ->
        [{_timestamp, user_id}] = :ets.lookup(@priority_queue, key)
        :ets.delete(@priority_queue, key)
        do_sync_balance_priority(user_id)
        process_priority_queue()
    end
  end

  defp process_background_queue do
    case :ets.first(@background_queue) do
      :"$end_of_table" ->
        :ok

      key ->
        [{_timestamp, user_id}] = :ets.lookup(@background_queue, key)
        :ets.delete(@background_queue, key)

        with {:ok, user} <- fetch_user(user_id) do
          do_sync_balance(user)
        end

        process_background_queue()
    end
  end

  defp do_sync_balance_priority(user_id) do
    with {:ok, user} <- fetch_user(user_id),
         {:ok, user} <- mark_sync_started(user),
         {:ok, working_balance, available_balance, cleared_balance, online_actual_balance} <-
           T24Context.get_account_balance_extended(user.account_number) do
      update_user_with_balance(
        user,
        working_balance,
        available_balance,
        cleared_balance,
        online_actual_balance
      )

      {:ok, working_balance}
    else
      error ->
        Logger.error("Priority sync failed for user #{user_id}: #{inspect(error)}")
        error
    end
  end

  defp fetch_user(user_id) do
    case Repo.get(User, user_id) do
      nil -> {:error, :user_not_found}
      user -> {:ok, user}
    end
  end

  defp do_sync_balance(%User{} = user) do
    user
    |> mark_sync_started()
    |> fetch_and_update_balance()
  end

  defp mark_sync_started(%User{} = user) do
    # Use a specific sync changeset that doesn't require password
    user
    |> Ecto.Changeset.change(%{
      sync_status: "syncing",
      last_sync_at: DateTime.utc_now() |> DateTime.truncate(:second)
    })
    |> Repo.update()
  end

  defp fetch_and_update_balance({:ok, user}) do
    case T24Context.get_account_balance_extended(user.account_number) do
      {:ok, working_balance, available_balance, cleared_balance, online_actual_balance} ->
        update_user_with_balance(
          user,
          working_balance,
          available_balance,
          cleared_balance,
          online_actual_balance
        )

      {:error, reason} ->
        mark_sync_failed(user, reason)
    end
  end

  defp fetch_and_update_balance({:error, _} = error), do: error

  defp update_user_with_balance(%User{} = user, balance) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    # Find matching fund account
    fund_account = Repo.get_by(FundAccounts, account_number: user.account_number)

    # Prepare user changeset
    user_changeset =
      Ecto.Changeset.change(user, %{
        account_balance: balance,
        sync_status: "synced",
        last_sync_at: now,
        sync_error: nil
      })

    # If fund account exists, prepare its changeset
    fund_account_changeset =
      if fund_account do
        Ecto.Changeset.change(fund_account, %{
          balance: balance,
          last_transaction_date: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
        })
      end

    # Update both in a transaction
    Repo.transaction(fn ->
      case Repo.update(user_changeset) do
        {:ok, updated_user} ->
          case fund_account_changeset do
            nil ->
              {:ok, updated_user}

            changeset ->
              case Repo.update(changeset) do
                {:ok, _updated_account} -> {:ok, updated_user}
                {:error, reason} -> Repo.rollback(reason)
              end
          end

        {:error, reason} ->
          Repo.rollback(reason)
      end
    end)
  end

  defp update_user_with_balance(
         %User{} = user,
         working_balance,
         available_balance,
         cleared_balance,
         online_actual_balance
       ) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    # Find matching fund account
    fund_account = Repo.get_by(FundAccounts, account_number: user.account_number)

    # Prepare user changeset
    user_changeset =
      Ecto.Changeset.change(user, %{
        account_balance: available_balance,
        working_balance: working_balance,
        clearing_balance: cleared_balance,
        sync_status: "synced",
        last_sync_at: now,
        sync_error: nil
      })

    # If fund account exists, prepare its changeset
    fund_account_changeset =
      if fund_account do
        Ecto.Changeset.change(fund_account, %{
          balance: available_balance,
          working_balance: working_balance,
          cleared_balance: cleared_balance,
          last_transaction_date: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
        })
      else
        account_params = %{
          account_number: user.account_number,
          user_id: user.id,
          balance: available_balance,
          working_balance: working_balance,
          cleared_balance: cleared_balance,
          last_transaction_date: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
        }

        case %FundAccounts{}
             |> FundAccounts.changeset(account_params)
             |> Repo.insert()
             |> IO.inspect(label: "Balanceupdate") do
          {:ok, clone_account} ->
            Ecto.Changeset.change(clone_account, %{
              balance: available_balance,
              working_balance: working_balance,
              cleared_balance: cleared_balance,
              last_transaction_date: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
            })

          {:error, _error} ->
            %FundAccounts{}
        end

        # update_user_with_balance(user, working_balance, available_balance, cleared_balance, online_actual_balance)
      end

    # Update both in a transaction
    Repo.transaction(fn ->
      with {:ok, updated_user} <- Repo.update(user_changeset),
           {:ok, result} <- handle_fund_account_update(updated_user, fund_account_changeset) do
        result
      else
        {:error, reason} ->
          Repo.rollback(reason)
      end
    end)
  end

  defp handle_fund_account_update(updated_user, fund_account_changeset) do
    case fund_account_changeset do
      nil ->
        {:ok, updated_user}

      changeset ->
        case Repo.update(changeset) do
          {:ok, _updated_account} ->
            case sync_customer_no(updated_user) do
              {:ok, updated_user} -> {:ok, updated_user}
              {:error, sync_reason} -> {:error, sync_reason}
            end

          {:error, reason} ->
            {:error, reason}
        end
    end
  end

  @doc """
  Synchronizes user's customer_no with T24 if it's empty.
  Returns {:ok, user} if sync is successful or not needed,
  {:error, reason} if sync fails.
  """
  track do
    def sync_customer_no(%User{} = user) do
      if is_empty_customer_no?(user) do
        case ServiceManager.Services.T24.Messages.GetAccountDetails.get_account_details_parsed(
               user.account_number
             ) do
          {:ok, account_details} ->
            user
            |> Ecto.Changeset.change(%{customer_no: to_string(account_details["customer_id"])})
            |> Repo.update()

          error ->
            error
        end
      else
        {:ok, user}
      end
    end
  end

  defp is_empty_customer_no?(%User{customer_no: customer_no}) do
    is_nil(customer_no) || customer_no == ""
  end

  defp mark_sync_failed(%User{} = user, reason) do
    # Use a specific sync changeset that doesn't require password
    user
    |> Ecto.Changeset.change(%{
      sync_status: "failed",
      last_sync_at: DateTime.utc_now() |> DateTime.truncate(:second),
      sync_error: to_string(reason)
    })
    |> Repo.update()
  end
end
