defmodule ServiceManager.Processors.WalletTransactionProcessor do
  use GenServer
  require Logger
  alias ServiceManager.Repo
  alias ServiceManager.Transactions.{Transaction, WalletTransactions}
  alias ServiceManager.Contexts.{TransactionsContext, WalletTransactionsContext}
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.Processors.TransactionErrorDecoder
  alias ServiceManager.Broadcasters.LoggerBroadcaster, as: Logger
  alias ServiceManager.Services.BalanceSnapshotService

  import Ecto.Query

  # 1 second
  @poll_interval 1000

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  def init(state) do
    schedule_poll()
    {:ok, state}
  end

  def handle_info(:poll, state) do
    # Logger.info("Starting wallet transaction processor poll cycle")
    process_pending_transactions()
    schedule_poll()
    # Logger.info("Completed wallet transaction processor poll cycle")
    {:noreply, state}
  end

  defp schedule_poll do
    Process.send_after(self(), :poll, @poll_interval)
  end

  defp process_pending_transactions do
    # Logger.info("Fetching pending wallet transactions")
    case WalletTransactionsContext.get_pending_wallet_transactions() do
      [] ->
        []

      # Logger.info("No pending wallet transactions found")
      transactions ->
        count = length(transactions)
        Enum.each(transactions, &process_transaction/1)
    end
  end

  defp safe_get_transaction(id) do
    try do
      transaction =
        WalletTransactions
        |> Repo.get(id)
        |> case do
          nil ->
            {:error, :not_found}

          transaction ->
            transaction = Repo.preload(transaction, [:from_account, :to_account])
            {:ok, transaction}
        end

      transaction
    rescue
      e -> {:error, e}
    end
  end

  defp process_transaction(transaction) do
    if is_nil(transaction.id) do
      Logger.error("Invalid wallet transaction - missing ID")
    else
      Logger.info(
        "Starting to process wallet transaction #{transaction.id} of type #{transaction.type}"
      )

      Logger.info(
        "Transaction details - Amount: #{transaction.amount}, From: #{transaction.from_account_id}, To: #{transaction.to_account_id}"
      )

      case safe_get_transaction(transaction.id) do
        {:ok, transaction} ->
          case update_transaction_status(transaction, "pending") do
            {:ok, transaction} ->
              Logger.info("Updated wallet transaction #{transaction.id} status to processing")

              # Process the transaction in a DB transaction for balance updates
              Repo.transaction(fn ->
                try do
                  # Re-fetch accounts with lock for balance updates
                  preloads = []

                  preloads =
                    if not is_nil(transaction.from_account_id) do
                      query =
                        from(w in WalletUser,
                          where: w.id == ^transaction.from_account_id,
                          lock: "FOR UPDATE"
                        )

                      [{:from_account, query} | preloads]
                    else
                      preloads
                    end

                  preloads =
                    if not is_nil(transaction.to_account_id) do
                      query =
                        from(w in WalletUser,
                          where: w.id == ^transaction.to_account_id,
                          lock: "FOR UPDATE"
                        )

                      [{:to_account, query} | preloads]
                    else
                      preloads
                    end

                  transaction =
                    if length(preloads) > 0 do
                      transaction |> Repo.preload(preloads, force: true)
                    else
                      transaction
                    end

                  # Create pre-transaction balance snapshots
                  create_pre_transaction_snapshots(transaction)

                  case process_by_type(transaction) do
                    {:ok, _status} ->
                      Logger.info("Successfully processed wallet transaction #{transaction.id}")

                      # Create post-transaction balance snapshots
                      create_post_transaction_snapshots(transaction)

                      update_transaction_status(transaction, "completed")
                      :ok

                    {:error, reason} ->
                      Logger.error(
                        "Failed to process wallet transaction #{transaction.id}: #{reason}"
                      )

                      update_transaction_status(transaction, "failed", reason)
                      # Repo.rollback(reason)
                  end

                  :ok
                rescue
                  e ->
                    Logger.error(
                      "Internal error processing wallet transaction #{transaction.id}: #{inspect(e)}"
                    )

                    update_transaction_status(transaction, "failed", "Internal processing error")
                    # Repo.rollback("Internal processing error")
                end
              end)

            {:error, reason} ->
              Logger.error("Failed to update wallet transaction status: #{reason}")
          end

        {:error, :not_found} ->
          Logger.error("Wallet transaction #{transaction.id} not found")

        {:error, error} ->
          Logger.error("Error retrieving wallet transaction #{transaction.id}: #{inspect(error)}")
      end
    end
  rescue
    e ->
      Logger.error(
        "Unexpected error processing wallet transaction #{transaction.id}: #{inspect(e)}"
      )

      case safe_get_transaction(transaction.id) do
        {:ok, transaction} ->
          update_transaction_status(transaction, "failed", "Internal processing error")

        _ ->
          Logger.error("Could not update transaction status - transaction not found")
      end
  end

  defp process_by_type(%{type: "transfer"} = transaction) do
    Logger.info("Processing wallet transfer transaction #{transaction.id}")
    # Handle transfer between wallets

    case check_existing_transaction(transaction.reference) do
      {:ok, %Transaction{status: "failed"}} ->
        Logger.info(
          "Found existing failed transaction with reference #{transaction.reference}. Skipping credit operation."
        )

        Repo.transaction(fn ->
          case update_transaction_status(transaction, "failed", "Transaction previously failed") do
            {:ok, _updated} ->
              Logger.info("Updated wallet transaction #{transaction.id} status to failed")
              # Repo.rollback("Transaction previously failed")
              :ok

            {:error, reason} ->
              Logger.error("Failed to update wallet transaction status: #{reason}")
              # Repo.rollback(reason)
              :error
          end
        end)

        {:error, "Transaction previously failed"}

      {:ok, %Transaction{status: "completed"}} ->
        with {:ok, to_account} <- ensure_account_loaded(transaction.to_account) do
          Logger.info(
            "Account loaded successfully for credit. Account: #{to_account.mobile_number}"
          )

          case update_transaction_status(transaction, "completed", "Wallet Transaction Completed") do
            {:ok, updated} ->
              Logger.info(
                "Successfully credited amount #{transaction.amount} to wallet #{to_account.mobile_number}"
              )

              {:ok, updated}

            {:error, reason} ->
              Logger.error("Failed to credit wallet #{to_account.mobile_number}: #{reason}")
              {:error, reason}
          end
        else
          {:error, reason} ->
            Logger.error("Failed to load account for credit: #{reason}")
            {:error, reason}
        end

      _ ->
        Logger.error("Re-queing transaction")

        update_transaction_status(
          transaction,
          "pending",
          "Credit Transaction scheduled for re-processing"
        )
    end

    # with {:ok, from_account} <- ensure_account_loaded(transaction.from_account),
    #      {:ok, to_account} <- ensure_account_loaded(transaction.to_account) do
    #   Logger.info("Accounts loaded successfully for transfer. From: #{from_account.mobile_number}, To: #{to_account.mobile_number}")

    #   # with {:ok, debit_result} <- debit_wallet(from_account, transaction.amount) do
    #   #   Logger.info("Successfully debited amount #{transaction.amount} from wallet #{from_account.mobile_number}")

    #   #   # case credit_wallet(to_account, transaction.amount) do
    #   #   #   {:ok, credit_result} ->
    #   #   #     Logger.info("Successfully credited amount #{transaction.amount} to wallet #{to_account.mobile_number}")
    #   #   #     Logger.info("Transfer completed successfully. Reference: #{transaction.reference}")
    #   #   #     {:ok, "Transfer completed successfully"}
    #   #   #   {:error, reason} ->
    #   #   #     Logger.error("Failed to credit wallet #{to_account.mobile_number}: #{reason}")
    #   #   #     {:error, reason}
    #   #   # end

    #   # else
    #   #   {:error, reason} ->
    #   #     Logger.error("Failed to debit wallet #{from_account.mobile_number}: #{reason}")
    #   #     {:error, reason}
    #   # end
    # else
    #   {:error, reason} ->
    #     Logger.error("Failed to load accounts for transfer: #{reason}")
    #     {:error, reason}
    # end
  end

  defp check_existing_transaction(reference) do
    case Repo.get_by(Transaction, reference: reference) do
      nil -> {:ok, nil}
      transaction -> {:ok, transaction}
    end
  end

  defp process_by_type(%{type: "credit"} = transaction) do
    Logger.info("Processing wallet credit transaction #{transaction.id}")

    case check_existing_transaction(transaction.reference) do
      {:ok, %Transaction{status: "failed"}} ->
        Logger.info(
          "Found existing failed transaction with reference #{transaction.reference}. Skipping credit operation."
        )

        Repo.transaction(fn ->
          case update_transaction_status(transaction, "failed", "Transaction previously failed") do
            {:ok, _updated} ->
              Logger.info("Updated wallet transaction #{transaction.id} status to failed")

            # Repo.rollback("Transaction previously failed")
            {:error, reason} ->
              Logger.error("Failed to update wallet transaction status: #{reason}")
              # Repo.rollback(reason)
          end
        end)

        {:error, "Transaction previously failed"}

      {:ok, %Transaction{status: "completed"}} ->
        with {:ok, to_account} <- ensure_account_loaded(transaction.to_account) do
          Logger.info(
            "Account loaded successfully for credit. Account: #{to_account.mobile_number}"
          )

          case update_transaction_status(transaction, "completed", "Wallet Transaction Completed") do
            {:ok, updated} ->
              Logger.info(
                "Successfully credited amount #{transaction.amount} to wallet #{to_account.mobile_number}"
              )

              {:ok, updated}

            {:error, reason} ->
              Logger.error("Failed to credit wallet #{to_account.mobile_number}: #{reason}")
              {:error, reason}
          end
        else
          {:error, reason} ->
            Logger.error("Failed to load account for credit: #{reason}")
            {:error, reason}
        end

      _ ->
        Logger.error("Re-queing transaction")

        update_transaction_status(
          transaction,
          "pending",
          "Credit Transaction scheduled for re-processing"
        )
    end
  end

  defp process_by_type(%{type: "debit"} = transaction) do
    Logger.info("Processing wallet debit transaction #{transaction.id}")

    case check_existing_transaction(transaction.reference) do
      {:ok, %Transaction{status: "failed"}} ->
        Logger.info(
          "Found existing failed transaction with reference #{transaction.reference}. Skipping debit operation."
        )

        Repo.transaction(fn ->
          case update_transaction_status(transaction, "failed", "Transaction previously failed") do
            {:ok, _updated} ->
              Logger.info("Updated wallet transaction #{transaction.id} status to failed")

            # Repo.rollback("Transaction previously failed")
            {:error, reason} ->
              Logger.error("Failed to update wallet transaction status: #{reason}")
              # Repo.rollback(reason)
          end
        end)

        {:error, "Transaction previously failed"}

      {:ok, %Transaction{status: "completed"}} ->
        with {:ok, from_account} <- ensure_account_loaded(transaction.from_account) do
          Logger.info(
            "Account loaded successfully for debit. Account: #{from_account.mobile_number}"
          )

          case update_transaction_status(transaction, "completed", "Wallet Transaction Completed") do
            {:ok, updated} ->
              Logger.info(
                "Successfully debited amount #{transaction.amount} from wallet #{from_account.mobile_number}"
              )

              {:ok, updated}

            {:error, reason} ->
              Logger.error("Failed to debit wallet #{from_account.mobile_number}: #{reason}")
              {:error, reason}
          end
        else
          {:error, reason} ->
            Logger.error("Failed to load account for debit: #{reason}")
            {:error, reason}
        end

      _ ->
        Logger.error("Re-queing transaction")

        update_transaction_status(
          transaction,
          "pending",
          "Debit Transaction scheduled for re-processing"
        )
    end
  end

  defp ensure_account_loaded(
         %WalletUser{id: id, mobile_number: mobile_number, balance: balance} = account
       )
       when not is_nil(id) and not is_nil(mobile_number) and not is_nil(balance) do
    Logger.info("Account validation successful - ID: #{id}, Mobile: #{mobile_number}")
    {:ok, account}
  end

  defp ensure_account_loaded(nil) do
    Logger.error("Account validation failed - Account not found")
    {:error, "Account not found"}
  end

  defp ensure_account_loaded(_) do
    Logger.error("Account validation failed - Missing required fields")
    {:error, "Account not properly loaded - missing required fields"}
  end

  defp debit_wallet(wallet, amount) do
    Logger.info("Starting debit operation for wallet #{wallet.mobile_number}")
    Logger.info("Debit details - Amount: #{amount}, Current Balance: #{wallet.balance}")

    # Update wallet balance directly in database with pessimistic locking
    wallet_with_lock = WalletTransactionsContext.get_wallet_with_lock(wallet.id)

    cond do
      is_nil(wallet_with_lock) ->
        Logger.error("Debit failed - Wallet #{wallet.mobile_number} not found with lock")
        {:error, "Wallet not found"}

      is_nil(wallet_with_lock.balance) ->
        Logger.error("Debit failed - Wallet #{wallet.mobile_number} has invalid balance")
        {:error, "Invalid wallet balance"}

      Decimal.compare(wallet_with_lock.balance, amount) == :lt ->
        Logger.error(
          "Debit failed - Insufficient funds in wallet #{wallet.mobile_number}. Required: #{amount}, Available: #{wallet_with_lock.balance}"
        )

        {:error, "Insufficient funds"}

      true ->
        new_balance = Decimal.sub(wallet_with_lock.balance, amount)

        Logger.info(
          "Attempting to update wallet balance from #{wallet_with_lock.balance} to #{new_balance}"
        )

        case Repo.update(WalletUser.update_balance(wallet_with_lock, %{balance: new_balance})) do
          {:ok, updated_wallet} ->
            Logger.info(
              "Successfully updated wallet #{wallet.mobile_number} balance to #{updated_wallet.balance}"
            )

            {:ok, updated_wallet}

          {:error, changeset} ->
            Logger.error(
              "Failed to update wallet #{wallet.mobile_number} balance: #{inspect(changeset.errors)}"
            )

            {:error, "Failed to update wallet balance"}
        end
    end
  end

  defp credit_wallet(wallet, amount) do
    Logger.info("Starting credit operation for wallet #{wallet.mobile_number}")
    Logger.info("Credit details - Amount: #{amount}, Current Balance: #{wallet.balance}")

    # Update wallet balance directly in database with pessimistic locking
    wallet_with_lock = WalletTransactionsContext.get_wallet_with_lock(wallet.id)

    cond do
      is_nil(wallet_with_lock) ->
        Logger.error("Credit failed - Wallet #{wallet.mobile_number} not found with lock")
        {:error, "Wallet not found"}

      is_nil(wallet_with_lock.balance) ->
        Logger.error("Credit failed - Wallet #{wallet.mobile_number} has invalid balance")
        {:error, "Invalid wallet balance"}

      true ->
        new_balance = Decimal.add(wallet_with_lock.balance, amount)

        Logger.info(
          "Attempting to update wallet balance from #{wallet_with_lock.balance} to #{new_balance}"
        )

        # TODO: add t24 transfer here
        # Add the transaction to input params

        case Repo.update(WalletUser.update_balance(wallet_with_lock, %{balance: new_balance})) do
          {:ok, updated_wallet} ->
            Logger.info(
              "Successfully updated wallet #{wallet.mobile_number} balance to #{updated_wallet.balance}"
            )

            {:ok, updated_wallet}

          {:error, changeset} ->
            Logger.error(
              "Failed to update wallet #{wallet.mobile_number} balance: #{inspect(changeset.errors)}"
            )

            {:error, "Failed to update wallet balance"}
        end
    end
  end

  defp update_transaction_error_details(transaction, error_attrs) do
    attrs = %{
      status: "failed",
      failure_reason: error_attrs.error_description,
      transaction_details: error_attrs.transaction_details
    }

    WalletTransactions.update(transaction, status: "failed")

    # case Repo.update(WalletTransactions.changeset(transaction, attrs)) do
    #   {:ok, updated} -> {:ok, updated}
    #   {:error, changeset} ->
    #     Logger.error("Failed to update wallet transaction error details: #{inspect(changeset.errors)}")
    #     {:error, "Failed to update transaction error details"}
    # end
  end

  @doc """
  Creates pre-transaction balance snapshots for the accounts involved in a wallet transaction.
  """
  defp create_pre_transaction_snapshots(transaction) do
    # Create snapshot for from_account if it exists
    if transaction.from_account_id do
      BalanceSnapshotService.create_pre_wallet_transaction_snapshot(
        transaction.from_account_id,
        transaction.id,
        "wallet_transaction_#{transaction.type}"
      )
    end

    # Create snapshot for to_account if it exists
    if transaction.to_account_id do
      BalanceSnapshotService.create_pre_wallet_transaction_snapshot(
        transaction.to_account_id,
        transaction.id,
        "wallet_transaction_#{transaction.type}"
      )
    end
  end

  @doc """
  Creates post-transaction balance snapshots for the accounts involved in a wallet transaction.
  """
  defp create_post_transaction_snapshots(transaction) do
    # Create snapshot for from_account if it exists
    if transaction.from_account_id do
      BalanceSnapshotService.create_post_wallet_transaction_snapshot(
        transaction.from_account_id,
        transaction.id,
        "wallet_transaction_#{transaction.type}"
      )
    end

    # Create snapshot for to_account if it exists
    if transaction.to_account_id do
      BalanceSnapshotService.create_post_wallet_transaction_snapshot(
        transaction.to_account_id,
        transaction.id,
        "wallet_transaction_#{transaction.type}"
      )
    end
  end

  defp update_transaction_status(transaction, status, reason \\ nil) do
    if status == "failed" do
      # Format wallet errors similar to T24 errors for consistency
      # error_attrs = TransactionErrorDecoder.format_error_for_storage({
      #   :wallet_error,
      #   %{
      #     code: "WALLET_TRANSACTION_ERROR",
      #     description: reason || "Unknown wallet error",
      #     details: %{
      #       transaction_type: transaction.type,
      #       amount: transaction.amount,
      #       from_account: transaction.from_account_id,
      #       to_account: transaction.to_account_id,
      #       timestamp: DateTime.utc_now()
      #     }
      #   }
      # })

      WalletTransactions.update(transaction, status: status)
      # update_transaction_error_details(transaction, error_attrs)
    else
      # attrs = %{status: status}

      WalletTransactions.update(transaction, status: status) |> IO.inspect(label: "WALLET-UPDATE")

      # case Repo.update(WalletTransactions.changeset(transaction, attrs)) do
      #   {:ok, updated} -> {:ok, updated}
      #   {:error, changeset} ->
      #     Logger.error("Failed to update wallet transaction status: #{inspect(changeset.errors)}")
      #     {:error, "Failed to update transaction status"}
      # end
    end
  end
end
