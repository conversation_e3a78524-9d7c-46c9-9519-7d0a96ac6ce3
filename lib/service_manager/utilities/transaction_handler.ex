defmodule ServiceManager.Utilities.TransactionHandler do
  @moduledoc """
  Provides utilities for handling Ecto transaction responses and formatting errors.
  This module standardizes error handling and success responses across the application.
  """

  require Logger

  @type transaction_result :: {:ok, any()} | {:error, any()} | {:error, any(), any(), any()}
  @type error_tuple :: {:error, String.t() | map()}
  @type success_tuple :: {:ok, any()}

  @doc """
  Handles the result of an Ecto transaction and formats the response.

  ## Examples

      iex> handle({:ok, %User{name: "<PERSON>"}})
      {:ok, %User{name: "<PERSON>"}}

      iex> handle({:error, %Ecto.Changeset{errors: [name: {"can't be blank", []}]}})
      {:error, "Name can't be blank"}

      iex> handle({:error, :not_found})
      {:error, "Resource not found"}
  """
  @spec handle(transaction_result()) :: success_tuple() | error_tuple()
  def handle({:ok, result}), do: {:ok, result}
  def handle({:error, %Ecto.Changeset{} = changeset}), do: format_changeset_errors(changeset)
  def handle({:error, :not_found}), do: {:error, "Resource not found"}
  def handle({:error, message}) when is_binary(message), do: {:error, message}
  def handle({:error, _, changeset, _}), do: format_changeset_errors(changeset)

  def handle(error) do
    Logger.error("Unexpected transaction error: #{inspect(error)}")
    {:error, "An unexpected error occurred"}
  end

  @doc """
  Formats changeset errors into a user-friendly message or structured error map.

  ## Examples

      iex> format_changeset_errors(%Ecto.Changeset{errors: [name: {"can't be blank", []}]})
      {:error, "Name can't be blank"}

      iex> format_changeset_errors(%Ecto.Changeset{errors: [
      ...>   name: {"can't be blank", []},
      ...>   email: {"is invalid", []}
      ...> ]}, :grouped)
      {:error, %{name: "Name can't be blank", email: "Email is invalid"}}
  """
  @spec format_changeset_errors(Ecto.Changeset.t(), :single | :grouped) :: error_tuple()
  def format_changeset_errors(changeset, format \\ :single) do
    errors =
      Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
        Enum.reduce(opts, msg, fn {key, value}, acc ->
          String.replace(acc, "%{#{key}}", to_string(value))
        end)
      end)

    case format do
      :single ->
        format_single_error(errors)

      :grouped ->
        {:error, format_grouped_errors(errors)}
    end
  end

  @doc """
  Wraps a function call in a transaction and handles its response.

  ## Examples

      iex> transaction_with(fn ->
      ...>   Repo.insert(%User{name: "John"})
      ...> end)
      {:ok, %User{name: "John"}}

      iex> transaction_with(fn ->
      ...>   Repo.insert(%User{})
      ...> end)
      {:error, "Name can't be blank"}
  """
  @spec transaction_with(function()) :: success_tuple() | error_tuple()
  def transaction_with(func) when is_function(func) do
    try do
      case ServiceManager.Repo.transaction(func) do
        {:ok, result} -> {:ok, result}
        {:error, reason} -> handle({:error, reason})
        error -> handle(error)
      end
    rescue
      e ->
        Logger.error("Transaction failed: #{inspect(e)}")
        {:error, "Transaction failed unexpectedly"}
    end
  end


  def transaction_response_parser(req_response) do
    case req_response do
      {:ok, response} ->
        {:ok, response}

      {:error, response} ->
        {:error, decode_recursive(response) |> atomize_keys()}
    end
  end

  defp decode_recursive(data) when is_binary(data) do
    case Jason.decode(data) do
      {:ok, decoded} -> decode_recursive(decoded)
      {:error, _} ->
        case extract_nested_json(data) do
          {:ok, extracted} -> decode_recursive(extracted) |> IO.inspect(label: "DECODED")
          {:error, _} -> data
        end
    end
  end

  defp decode_recursive(data) when is_map(data) do
    Enum.reduce(data, %{}, fn {key, value}, acc ->
      decoded_value = case {key, value} do
        {:error, "HTTP " <> _ = http_error} ->
          case extract_nested_json(http_error) do
            {:ok, extracted} -> decode_recursive(extracted) |> atomize_keys()
            {:error, _} -> decode_recursive(value)
          end
        _ ->
          decode_recursive(value)
      end
      Map.put(acc, key, decoded_value)
    end)
  end

  defp decode_recursive(data) when is_list(data) do
    Enum.map(data, &decode_recursive/1)
  end

  defp decode_recursive(data), do: data

  defp extract_nested_json(data) when is_binary(data) do
    case Regex.run(~r/HTTP \d+: (.+)$/, data) do
      [_, elixir_map_string] ->
        elixir_map_string
        |> String.replace("\\\"", "\"")
        |> convert_elixir_map_to_json()
        |> Jason.decode()
      _ ->
        {:error, :no_nested_json}
    end
  end

  defp convert_elixir_map_to_json(elixir_string) do
    elixir_string
    |> String.replace(~r/%\{/, "{")
    |> String.replace(~r/\}/, "}")
    |> String.replace(~r/(\w+)\s*=>\s*/, "\"\\1\": ")
    |> String.replace(~r/"(\w+)"\s*=>\s*/, "\"\\1\": ")
  end

  defp atomize_keys(map) when is_map(map) do
    Enum.reduce(map, %{}, fn {key, value}, acc ->
      atom_key = case key do
        key when is_binary(key) -> String.to_atom(key)
        key when is_atom(key) -> key
        key -> key
      end
      Map.put(acc, atom_key, atomize_keys(value))
    end)
  end

  defp atomize_keys(list) when is_list(list) do
    Enum.map(list, &atomize_keys/1)
  end

  defp atomize_keys(value), do: value

  # Private Functions

  defp format_single_error(errors) when errors == %{}, do: {:error, "Invalid input"}

  defp format_single_error(errors) do
    error_message =
      errors
      |> Map.to_list()
      |> Enum.map_join(", ", fn {field, message} ->
        "#{format_field(field)} #{format_message(message)}"
      end)

    {:error, error_message}
  end

  defp format_grouped_errors(errors) do
    errors
    |> Enum.map(fn {field, message} ->
      {field, "#{format_field(field)} #{format_message(message)}"}
    end)
    |> Map.new()
  end

  defp format_field(field) when is_atom(field),
    do: field |> Atom.to_string() |> String.capitalize()

  defp format_field(field) when is_binary(field), do: String.capitalize(field)

  defp format_message(message) when is_list(message), do: message |> List.first()
  defp format_message(message) when is_binary(message), do: message
end
