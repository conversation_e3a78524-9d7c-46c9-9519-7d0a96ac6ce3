defmodule ServiceManager.Utilities.Encrypted.Decimal do
  use Cloak.Ecto.Type, vault: ServiceManager.Vault

  def type, do: :binary

  # Casting - converts external input to internal Decimal
  def cast(value) do
    case convert_to_decimal(value) do
      {:ok, decimal} -> {:ok, decimal}
      _ -> :error
    end
  end

  # Dumping - converts internal Decimal to binary for database
  def dump(%Decimal{} = decimal) do
    {:ok, :erlang.term_to_binary(decimal)}
  end

  def dump(nil), do: {:ok, nil}
  def dump(_), do: :error

  # Loading - converts database binary back to Decimal
  def load(""), do: {:ok, nil}
  def load(nil), do: {:ok, nil}

  def load(value) when is_binary(value) do
    try do
      {:ok, :erlang.binary_to_term(value)}
    rescue
      _ -> :error
    end
  end

  def load(_), do: :error

  # Before encryption - converts Decimal to binary
  def before_encrypt(%Decimal{} = decimal) do
    {:ok, :erlang.term_to_binary(decimal)}
  end

  def before_encrypt(nil), do: {:ok, nil}

  # After decryption - converts binary back to Decimal
  def after_decrypt(nil), do: {:ok, nil}

  def after_decrypt(value) when is_binary(value) do
    try do
      {:ok, :erlang.binary_to_term(value)}
    rescue
      _ -> :error
    end
  end

  defp convert_to_decimal(%Decimal{} = decimal), do: {:ok, decimal}
  defp convert_to_decimal(nil), do: {:ok, nil}
  defp convert_to_decimal(value) when is_integer(value), do: {:ok, Decimal.new(value)}
  defp convert_to_decimal(value) when is_float(value), do: {:ok, Decimal.from_float(value)}

  defp convert_to_decimal(value) when is_binary(value) do
    case Decimal.parse(value) do
      {:ok, decimal} -> {:ok, decimal}
      _ -> :error
    end
  end

  defp convert_to_decimal(_), do: :error
end
