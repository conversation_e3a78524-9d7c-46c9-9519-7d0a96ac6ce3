defmodule ServiceManager.Utilities.Encrypted.String do
  @moduledoc """
  Encrypted string field for Ecto schemas.

  ## Configuration Options

  You can set these options in your schema:

      field :secret, ServiceManager.Utilities.Encrypted.String,
        default: nil,
        strict: true,          # if true, only binaries are accepted (default: false)
        trim: true,            # if true, trims whitespace (default: true)
        downcase: false,       # if true, converts to lowercase (default: false)
        empty_to_nil: true     # if true, empty strings become nil (default: true)
  """
  use Cloak.Ecto.Type, vault: ServiceManager.Vault

  @behaviour Cloak.Ecto.Type
  @behaviour Ecto.Type

  def type, do: :binary

  # Casting - accepts strings or values that can be converted to strings
  @impl Ecto.Type
  def cast(value) do
    options = get_options()

    case convert_to_string(value, options) do
      {:ok, string} -> {:ok, string}
      error -> error
    end
  end

  # Dumping - converts internal string to binary for database
  @impl Ecto.Type
  def dump(value) when is_binary(value), do: {:ok, :erlang.term_to_binary(value)}
  def dump(nil), do: {:ok, nil}
  def dump(_), do: :error

  # Loading - converts database binary back to string
  @impl Ecto.Type
  def load(""), do: {:ok, nil}
  def load(nil), do: {:ok, nil}

  def load(value) when is_binary(value) do
    try do
      {:ok, :erlang.binary_to_term(value)}
    rescue
      _ -> :error
    end
  end

  def load(_), do: :error

  # Cloak-specific callbacks (don't use @impl as they're not formal behaviours)
  def before_encrypt(value) when is_binary(value), do: {:ok, :erlang.term_to_binary(value)}
  def before_encrypt(nil), do: {:ok, nil}

  def after_decrypt(nil), do: {:ok, nil}

  def after_decrypt(value) when is_binary(value) do
    try do
      {:ok, :erlang.binary_to_term(value)}
    rescue
      _ -> :error
    end
  end

  # Helper functions

  defp convert_to_string(value, options) do
    cond do
      is_nil(value) -> {:ok, nil}
      options[:strict] && not is_binary(value) -> :error
      true -> do_convert(value, options)
    end
  end

  defp do_convert(value, options) when is_binary(value) do
    value
    |> maybe_trim(options)
    |> maybe_downcase(options)
    |> maybe_empty_to_nil(options)
    |> wrap_ok()
  end

  defp do_convert(value, options) when is_atom(value) do
    value
    |> Atom.to_string()
    |> maybe_downcase(options)
    |> wrap_ok()
  end

  defp do_convert(value, _options) when is_number(value) do
    {:ok, to_string(value)}
  end

  defp do_convert(%Decimal{} = value, _options) do
    {:ok, Decimal.to_string(value)}
  end

  defp do_convert(%Date{} = value, _options) do
    {:ok, Date.to_iso8601(value)}
  end

  defp do_convert(%DateTime{} = value, _options) do
    {:ok, DateTime.to_iso8601(value)}
  end

  defp do_convert(%NaiveDateTime{} = value, _options) do
    {:ok, NaiveDateTime.to_iso8601(value)}
  end

  defp do_convert(%Time{} = value, _options) do
    {:ok, Time.to_iso8601(value)}
  end

  defp do_convert(%{} = value, options) do
    if options[:inspect_maps] do
      {:ok, inspect(value)}
    else
      :error
    end
  end

  defp do_convert(value, options) when is_list(value) do
    if options[:inspect_lists] do
      {:ok, inspect(value)}
    else
      :error
    end
  end

  defp do_convert(_, _), do: :error

  defp maybe_trim(value, %{trim: true}), do: String.trim(value)
  defp maybe_trim(value, %{trim: false}), do: value

  defp maybe_trim(value, options),
    do: if(options[:trim] != false, do: String.trim(value), else: value)

  defp maybe_downcase(value, %{downcase: true}), do: String.downcase(value)
  defp maybe_downcase(value, %{downcase: false}), do: value

  defp maybe_downcase(value, options),
    do: if(options[:downcase], do: String.downcase(value), else: value)

  defp maybe_empty_to_nil("", %{empty_to_nil: true}), do: nil
  defp maybe_empty_to_nil("", %{empty_to_nil: false}), do: ""

  defp maybe_empty_to_nil(value, options),
    do: if(value == "" && options[:empty_to_nil] != false, do: nil, else: value)

  defp wrap_ok(nil), do: {:ok, nil}
  defp wrap_ok(value), do: {:ok, value}

  defp get_options do
    case Process.get(:cloak_ecto_options) do
      nil -> %{trim: true, empty_to_nil: true}
      options -> Map.merge(%{trim: true, empty_to_nil: true}, options)
    end
  end
end
