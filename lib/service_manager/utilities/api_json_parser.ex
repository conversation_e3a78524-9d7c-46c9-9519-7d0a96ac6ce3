defmodule ServiceManager.Utilities.ApiJsonParser do
  @moduledoc """
  Provides utilities for formatting API responses into a standardized JSON structure.

  This module helps transform Ecto structs and other data into a consistent JSON format
  with status and message fields for API responses.
  """

  # Remove this if you want the module to be included in documentation
  @moduledoc false

  @doc """
  Formats data into a standardized API response JSON structure.

  ## Parameters
    - `data`: The data to include in the response (typically an Ecto struct or map)
    - `status`: The status of the response (e.g., :success, :error)
    - `message`: A human-readable message describing the response
    - `exclude`: Optional list of keys to exclude from the data (default: [])

  ## Returns
    A map with the following structure:
    %{
      data: <processed data>,
      status: <status>,
      message: <message>
    }

  ## Examples
      iex> api_json_format(%User{name: "<PERSON>"}, :success, "User found")
      %{
        data: %{name: "<PERSON>"},
        status: :success,
        message: "User found"
      }
  """
  def api_json_format(data, status, message, exclude \\ []) do
    extract_data(data, exclude)
    |> generate(status, message)
  end

  def api_json_format(status, message) do
    generate(%{}, status, message)
  end

  @doc false
  defp generate(data, status, message) do
    %{
      data: data,
      status: status,
      message: message
    }
  end

  @doc """
  Extracts data from a struct while filtering out unwanted fields.

  ## Parameters
    - `struct`: The input struct to extract data from
    - `ignored_keys`: List of keys to exclude from the result

  ## Returns
    A map containing only the non-ignored fields with non-nil values,
    excluding Ecto metadata and unloaded associations.

  ## Processing Rules
    1. Removes :__meta__ and :__struct__ fields
    2. Excludes any keys in ignored_keys list
    3. Excludes Ecto.Association.NotLoaded values
    4. Excludes nil values
  """
  defp extract_data(%Scrivener.Page{} = struct, ignored_keys) when is_struct(struct) do
    map = generate_pagination_details(struct)

    entries =
      struct.entries
      |> Enum.map(fn struct ->
        extract_data(struct, ignored_keys)
      end)

    Map.put(map, :entries, entries)
  end

  defp extract_data(struct, ignored_keys) when is_struct(struct) do
    struct
    |> Map.from_struct()
    # Remove Ecto metadata fields
    |> Map.delete(:__meta__)
    |> Map.delete(:__struct__)
    |> Enum.reduce(%{}, fn
      {key, _value}, acc ->
        if key in ignored_keys do
          # Skip ignored keys
          acc
        else
          case {key, _value} do
            # Skip unloaded associations
            {_key, %struct_module{}} when struct_module == Ecto.Association.NotLoaded ->
              acc

            # Skip nil values
            {_key, nil} ->
              acc

            # Include all other non-nil values
            {key, value} ->
              Map.put(acc, key, value)
          end
        end
    end)
  end

  defp extract_data(list, ignored_keys) when is_list(list) do
    map = generate_pagination_details(list)

    entries =
      list
      |> Enum.map(fn struct ->
        extract_data(struct, ignored_keys)
      end)

    Map.put(map, :entries, entries)
  end

  defp extract_data(list, ignored_keys) do
    map = generate_pagination_details(list)

    Map.put(map, :entries, list)
  end

  defp generate_pagination_details(data) do
    try do
      %{
        page_number: data.page_number || 1,
        page_size: data.page_size || 1,
        total_entries: data.total_entries || 1,
        total_pages: data.total_pages || 1
      }
    rescue
      _ ->
        %{
          page_number: 1,
          page_size: 1,
          total_entries: 1,
          total_pages: 1
        }
    end
  end
end
