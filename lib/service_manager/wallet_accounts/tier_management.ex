defmodule ServiceManager.WalletAccounts.TierManagement do
  @moduledoc """
  The TierManagement context handles all operations related to wallet tiers,
  including creation, updates, and validation of tier requirements.
  """

  import Ecto.Query, warn: false
  import Ecto.Changeset
  alias ServiceManager.Repo
  alias ServiceManager.WalletAccounts.WalletTier
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.WalletAccounts.EmbeddedKyc.KycField
  alias ServiceManager.WalletAccounts.EmbeddedKyc.KycRule

  # Define available fields and rules constants
  @available_kyc_fields [
    "first_name",
    "last_name",
    "mobile_number",
    "email",
    "date_of_birth",
    "address",
    "city",
    "occupation",
    "employer_name",
    "source_of_funds",
    "id_number",
    "id_image"
  ]

  @available_kyc_rules [
    {"minimum_age", "Minimum Age (years)", :number},
    {"id_required", "ID Required", :boolean},
    {"address_required", "Address Required", :boolean},
    {"employment_verification", "Employment Verification", :boolean},
    {"source_of_funds_required", "Source of Funds Required", :boolean},
    {"nrb_verification", "NRB Verification", :boolean}
  ]

  @doc """
  Returns the list of wallet tiers ordered by position.
  """
  def list_tiers do
    WalletTier
    # |> exclude(:kyc_fields)
    # |> exclude(:validation_rules)
    |> order_by(:position)
    |> Repo.all()
    |> Enum.map(&ensure_embedded_schemas/1)
  end

  @doc """
  Gets a single wallet tier.
  Raises `Ecto.NoResultsError` if the tier does not exist.
  """
  def get_tier!(id), do: Repo.get!(WalletTier, id)

  @doc """
  Gets the default tier.
  """
  def get_default_tier do
    WalletTier
    |> where(is_default: true)
    |> Repo.one()
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking wallet tier changes.
  """
  def change_tier(tier, attrs \\ %{}) do
    # Ensure tier has the embedded structures initialized
    tier = ensure_embedded_schemas(tier)
    WalletTier.changeset(tier, attrs)
  end

  def ensure_embedded_schemas(tier) do
    tier
    |> ensure_kyc_fields()
    |> ensure_validation_rules()
  end

  defp ensure_kyc_fields(%{kyc_fields: fields} = tier)
       when is_list(fields) and length(fields) > 0,
       do: tier

  defp ensure_kyc_fields(tier) do
    kyc_fields =
      @available_kyc_fields
      |> Enum.map(fn field ->
        %KycField{
          field_name: field,
          enabled: field in (tier.required_kyc_fields || [])
        }
      end)

    %{tier | kyc_fields: kyc_fields}
  end

  def ensure_validation_rules(%{validation_rules: rules} = tier)
      when is_list(rules) and length(rules) > 0,
      do: tier

  def ensure_validation_rules(tier) do
    validation_rules =
      @available_kyc_rules
      |> Enum.map(fn {key, label, type} ->
        value =
          case Map.get(tier.kyc_rules || %{}, key) do
            nil -> if type == :number, do: "18", else: ""
            v when is_integer(v) -> Integer.to_string(v)
            v -> to_string(v)
          end

        enabled = Map.has_key?(tier.kyc_rules || %{}, key)

        %KycRule{
          rule_name: key,
          label: label,
          type: to_string(type),
          value: value,
          enabled: enabled
        }
      end)

    %{tier | validation_rules: validation_rules}
  end

  @doc """
  Creates a wallet tier.
  """
  def create_tier(attrs \\ %{}) do
    %WalletTier{}
    |> WalletTier.changeset(attrs)
    |> handle_default_tier()
    |> Repo.insert()
  end

  @doc """
  Updates a wallet tier.
  """
  def update_tier(%WalletTier{} = tier, attrs) do
    tier
    |> WalletTier.changeset(attrs)
    |> handle_default_tier()
    |> Repo.update()
  end

  @doc """
  Deletes a wallet tier.
  """
  def delete_tier(%WalletTier{} = tier) do
    # Don't allow deletion if users are assigned to this tier
    case Repo.exists?(from u in WalletUser, where: u.wallet_tier_id == ^tier.id) do
      true ->
        {:error, :users_assigned}

      false ->
        Repo.delete(tier)
    end
  end

  @doc """
  Returns a list of available tiers for upgrade based on completed KYC fields.
  """
  def available_upgrade_tiers(%WalletUser{} = user) do
    completed_fields = get_completed_kyc_fields(user)

    WalletTier
    |> where([t], t.id != ^user.wallet_tier_id)
    |> order_by(:position)
    |> Repo.all()
    |> Enum.filter(fn tier ->
      has_required_fields?(completed_fields, tier.required_kyc_fields)
    end)
  end

  @doc """
  Checks if a user meets the KYC requirements for a specific tier.
  """
  def meets_tier_requirements?(%WalletUser{} = user, %WalletTier{} = tier) do
    completed_fields = get_completed_kyc_fields(user)

    has_required_fields?(completed_fields, tier.required_kyc_fields) &&
      meets_kyc_rules?(user, tier.kyc_rules)
  end

  @doc """
  Reorders tiers by updating their positions.
  Expects a list of maps with id and position: [%{id: 1, position: 2}, ...]
  """
  def reorder_tiers(positions) do
    Repo.transaction(fn ->
      Enum.each(positions, fn %{id: id, position: position} ->
        from(t in WalletTier, where: t.id == ^id)
        |> Repo.update_all(set: [position: position])
      end)
    end)
  end

  # Private functions

  defp handle_default_tier(changeset) do
    if get_change(changeset, :is_default) do
      # If setting as default, unset any existing default
      from(t in WalletTier, where: t.is_default == true)
      |> Repo.update_all(set: [is_default: false])
    end

    changeset
  end

  defp get_completed_kyc_fields(user) do
    fields =
      ~w(first_name last_name mobile_number email date_of_birth address city occupation employer_name source_of_funds id_number id_image nrb_validation)a

    fields
    |> Enum.filter(fn field ->
      value = Map.get(user, field)
      not is_nil(value) and value != "" and value != false
    end)
    |> Enum.map(&Atom.to_string/1)
  end

  defp has_required_fields?(completed_fields, required_fields) do
    Enum.all?(required_fields, &(&1 in completed_fields))
  end

  defp meets_kyc_rules?(user, rules) do
    Enum.all?(rules, fn
      {"minimum_age", min_age} ->
        case user.date_of_birth do
          nil -> false
          dob -> Date.diff(Date.utc_today(), dob) >= min_age * 365
        end

      {"address_required", true} ->
        not is_nil(user.address) and user.address != ""

      {"employment_verification", true} ->
        not is_nil(user.employer_name) and user.employer_name != ""

      {"source_of_funds_required", true} ->
        not is_nil(user.source_of_funds) and user.source_of_funds != ""

      {"id_required", true} ->
        not is_nil(user.id_number) and user.id_number != ""

      _ ->
        true
    end)
  end
end