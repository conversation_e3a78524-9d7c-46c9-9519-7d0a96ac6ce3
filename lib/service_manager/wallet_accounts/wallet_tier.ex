defmodule ServiceManager.WalletAccounts.WalletTier do
  use Ecto.Schema
  import Ecto.Changeset

  alias ServiceManager.WalletAccounts.EmbeddedKyc.KycField
  alias ServiceManager.WalletAccounts.EmbeddedKyc.KycRule

  schema "wallet_tiers" do
    field :name, :string
    field :description, :string
    field :position, :integer
    field :is_default, :boolean, default: false

    # Balance Limits
    field :minimum_balance, :decimal, default: 0
    field :maximum_balance, :decimal

    # Transaction Limits
    field :min_transaction_amount, :decimal
    field :max_transaction_amount, :decimal
    field :maximum_credit_limit, :decimal, default: 0
    field :maximum_debt_limit, :decimal, default: 0
    field :daily_transaction_limit, :decimal
    field :monthly_transaction_limit, :decimal

    # Transaction Count Limits
    field :daily_transaction_count, :integer
    field :monthly_transaction_count, :integer

    # Dynamic KYC Requirements
    # Dynamic KYC Requirements (existing database fields)
    field :required_kyc_fields, {:array, :string}
    field :kyc_rules, :map, default: %{}

    # New embedded fields
    embeds_many :kyc_fields, KycField, on_replace: :delete
    embeds_many :validation_rules, KycRule, on_replace: :delete

    has_many :wallet_users, ServiceManager.WalletAccounts.WalletUser

    timestamps()
  end

  @doc """
  A changeset for creating or updating a wallet tier.
  """
  def changeset(wallet_tier, attrs) do
    wallet_tier
    |> cast(attrs, [
      :name,
      :description,
      :position,
      :is_default,
      :minimum_balance,
      :maximum_balance,
      :maximum_credit_limit,
      :maximum_debt_limit,
      :min_transaction_amount,
      :max_transaction_amount,
      :daily_transaction_limit,
      :monthly_transaction_limit,
      :daily_transaction_count,
      :monthly_transaction_count,
      :required_kyc_fields,
      :kyc_rules
    ])
    |> cast_embed(:kyc_fields)
    |> cast_embed(:validation_rules, with: &KycRule.changeset(&1, &2, validate_dob: date_of_birth_enabled?(wallet_tier)))
    |> validate_required([
      :name,
      :position,
      :minimum_balance,
      :maximum_balance,
      :maximum_debt_limit,
      :maximum_credit_limit,
      :min_transaction_amount,
      :max_transaction_amount,
      :daily_transaction_limit,
      :monthly_transaction_limit,
      :daily_transaction_count,
      :monthly_transaction_count
      # :required_kyc_fields
    ])
    |> validate_number(:position, greater_than: 0)
    |> validate_number(:minimum_balance, greater_than_or_equal_to: 0)
    |> validate_number(:maximum_balance, greater_than: 0)
    |> validate_number(:maximum_debt_limit, greater_than: 0)
    |> validate_number(:maximum_credit_limit, greater_than: 0)
    |> validate_number(:min_transaction_amount, greater_than: 0)
    |> validate_number(:max_transaction_amount, greater_than: 0)
    |> validate_number(:daily_transaction_limit, greater_than: 0)
    |> validate_number(:monthly_transaction_limit, greater_than: 0)
    |> validate_number(:daily_transaction_count, greater_than: 0)
    |> validate_number(:monthly_transaction_count, greater_than: 0)
    |> validate_limits()
    |> validate_min_age_when_dob_enabled()
    |> sync_legacy_fields()
  end

  defp sync_fields(changeset) do
    changeset
    |> sync_kyc_fields()
    |> sync_validation_rules()
  end

  defp sync_kyc_fields(changeset) do
    case get_change(changeset, :kyc_fields) do
      nil ->
        changeset

      kyc_fields ->
        required_fields =
          kyc_fields
          |> Enum.filter(&Map.get(&1, "enabled", false))
          |> Enum.map(&Map.get(&1, "field_name"))

        put_change(changeset, :required_kyc_fields, required_fields)
    end
  end

  defp sync_validation_rules(changeset) do
    case get_change(changeset, :validation_rules) do
      nil ->
        changeset

      rules ->
        kyc_rules =
          rules
          |> Enum.filter(&Map.get(&1, "enabled", false))
          |> Enum.map(fn rule ->
            value =
              if Map.get(rule, "type") == "number",
                do: String.to_integer(Map.get(rule, "value", "0")),
                else: true

            {Map.get(rule, "rule_name"), value}
          end)
          |> Map.new()

        put_change(changeset, :kyc_rules, kyc_rules)
    end
  end

  defp validate_limits(changeset) do
    changeset
    |> validate_min_max(:minimum_balance, :maximum_balance)
    |> validate_min_max(:min_transaction_amount, :max_transaction_amount)
    |> validate_daily_monthly_limit(:daily_transaction_limit, :monthly_transaction_limit)
    |> validate_daily_monthly_limit(:daily_transaction_count, :monthly_transaction_count)
    |> validate_credit_debt_limits(:maximum_credit_limit, :monthly_transaction_limit)
    |> validate_credit_debt_limits(:maximum_debt_limit, :monthly_transaction_limit)
  end

  defp validate_credit_debt_limits(changeset, limit_field, monthly_field) do
    limit_value = get_field(changeset, limit_field)
    monthly_value = get_field(changeset, monthly_field)

    if limit_value && monthly_value && Decimal.compare(limit_value, monthly_value) == :gt do
      add_error(changeset, limit_field, "must not be greater than monthly transaction limit")
    else
      changeset
    end
  end

  defp validate_min_max(changeset, min_field, max_field) do
    min_value = get_field(changeset, min_field)
    max_value = get_field(changeset, max_field)

    if min_value && max_value && Decimal.compare(min_value, max_value) == :gt do
      add_error(changeset, min_field, "must be less than or equal to maximum")
    else
      changeset
    end
  end

  defp validate_daily_monthly_limit(changeset, daily_field, monthly_field) do
    daily = get_field(changeset, daily_field)
    monthly = get_field(changeset, monthly_field)

    cond do
      is_nil(daily) or is_nil(monthly) ->
        changeset

      # For decimal fields
      match?(%Decimal{}, daily) && match?(%Decimal{}, monthly) ->
        if Decimal.compare(daily, monthly) == :gt do
          add_error(changeset, daily_field, "must be less than or equal to monthly limit")
        else
          changeset
        end

      # For integer fields
      is_integer(daily) && is_integer(monthly) ->
        if daily > monthly do
          add_error(changeset, daily_field, "must be less than or equal to monthly limit")
        else
          changeset
        end

      true ->
        changeset
    end
  end

  defp validate_date_of_birth_and_minimum_age_requirement(changeset) do
    kyc_fields = get_field(changeset, :kyc_fields) || []
    validation_rules = get_field(changeset, :validation_rules) || []

    has_date_of_birth =
      Enum.any?(kyc_fields, fn
        %{field_name: "date_of_birth", enabled: true} -> true
        _ -> false
      end)

    has_minimum_age =
      Enum.any?(validation_rules, fn
        %{rule_name: "minimum_age", enabled: true} -> true
        _ -> false
      end)

    cond do
      has_date_of_birth && !has_minimum_age ->
        changeset
        |> add_error(
          :validation_rules,
          "Minimum age rule is required when date of birth is enabled"
        )

      !has_date_of_birth && has_minimum_age ->
        changeset
        |> add_error(:validation_rules, "Minimum age rule requires date of birth to be enabled")

      true ->
        changeset
    end
  end

  # New function to sync embedded schemas with legacy fields
  defp sync_legacy_fields(changeset) do
    changeset
    |> sync_required_fields()
    |> sync_kyc_rules()
  end

  defp sync_required_fields(changeset) do
    case get_change(changeset, :kyc_fields) do
      nil ->
        changeset

      kyc_fields ->
        required =
          kyc_fields
          # Get the underlying data from changesets
          |> Enum.map(& &1.data)
          |> Enum.filter(& &1.enabled)
          |> Enum.map(& &1.field_name)

        put_change(changeset, :required_kyc_fields, required)
    end
  end

  defp sync_kyc_rules(changeset) do
    case get_change(changeset, :validation_rules) do
      nil ->
        changeset

      rules ->
        rule_map =
          rules
          # Get the underlying data from changesets
          |> Enum.map(& &1.data)
          |> Enum.filter(& &1.enabled)
          |> Enum.map(fn rule ->
            value = if rule.type == "number", do: String.to_integer(rule.value || "0"), else: true
            {rule.rule_name, value}
          end)
          |> Map.new()

        put_change(changeset, :kyc_rules, rule_map)
    end
  end


  defp validate_min_age_when_dob_enabled(changeset) do
    if date_of_birth_enabled?(changeset) do
      validation_rules = get_field(changeset, :validation_rules) || []

      min_age_rule = Enum.find(validation_rules, &(&1.rule_name == "minimum_age"))

      case min_age_rule do
        %{enabled: true, value: value} ->
          case Integer.parse(value || "0") do
            {num, _} when num > 0 -> changeset
            _ -> add_error(changeset, :validation_rules, "Minimum age must be greater than 0 when date of birth is required")
          end
        _ ->
          add_error(changeset, :validation_rules, "Minimum age rule must be enabled when date of birth is required")
      end
    else
      changeset
    end
  end

  defp date_of_birth_enabled?(%Ecto.Changeset{} = changeset) do
    (get_field(changeset, :kyc_fields) || [])
    |> Enum.any?(fn
      %{field_name: "date_of_birth", enabled: true} -> true
      _ -> false
    end)
  end

  defp date_of_birth_enabled?(%{kyc_fields: kyc_fields}) do
    Enum.any?(kyc_fields || [], fn
      %{field_name: "date_of_birth", enabled: true} -> true
      _ -> false
    end)
  end

  defp date_of_birth_enabled?(_), do: false


end
