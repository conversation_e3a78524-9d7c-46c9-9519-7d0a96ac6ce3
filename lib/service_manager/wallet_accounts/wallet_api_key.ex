defmodule ServiceManager.WalletAccounts.WalletApiKey do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset

  schema "wallet_api_key" do
    field :api_key, :string
    field :description, :string
    belongs_to :wallet_user, ServiceManager.WalletAccounts.WalletUser

    timestamps(type: :utc_datetime)
  end

  def changeset(wallet_api_key, attrs, opts \\ []) do
    wallet_api_key
    |> cast(attrs, [:api_key, :description])
    |> validate_required([:api_key, :description])
  end
end
