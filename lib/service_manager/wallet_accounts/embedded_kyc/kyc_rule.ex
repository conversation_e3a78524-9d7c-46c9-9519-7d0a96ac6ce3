defmodule ServiceManager.WalletAccounts.EmbeddedKyc.KycRule do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key false
  embedded_schema do
    field :rule_name, :string
    field :label, :string
    field :type, :string
    field :value, :string
    field :enabled, :boolean, default: false
  end

  def changeset(kyc_rule, attrs, opts \\ []) do
    validate_dob = Keyword.get(opts, :validate_dob, false)

    kyc_rule
    |> cast(attrs, [:rule_name, :label, :type, :value, :enabled])
    |> validate_required([:rule_name, :type])
    |> validate_minimum_age(validate_dob)
  end

  defp validate_minimum_age(changeset, validate_dob) do
    case {get_field(changeset, :rule_name), validate_dob} do
      {"minimum_age", true} ->
        changeset
        |> validate_required([:value])
        |> validate_change(:value, fn :value, value ->
          case Integer.parse(value || "0") do
            {num, _} when num > 0 -> []
            _ -> [value: "must be greater than 0"]
          end
        end)
      _ ->
        changeset
    end
  end
end