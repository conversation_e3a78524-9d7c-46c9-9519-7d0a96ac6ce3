defmodule ServiceManager.WalletAccounts.WalletUserNotifier do
  import Swoosh.Email

  alias ServiceManager.Mailer

  # Delivers the email using the application mailer.
  defp deliver(recipient, subject, body) do
    email =
      new()
      |> to(recipient)
      |> from({"ServiceManager", "<EMAIL>"})
      |> subject(subject)
      |> text_body(body)

    with {:ok, _metadata} <- Mailer.deliver(email) do
      {:ok, email}
    end
  end

  @doc """
  Deliver instructions to confirm account.
  """
  def deliver_confirmation_instructions(wallet_user, url) do
    deliver(wallet_user.email, "Confirmation instructions", """

    ==============================

    Hi #{wallet_user.email},

    You can confirm your account by visiting the URL below:

    #{url}

    If you didn't create an account with us, please ignore this.

    ==============================
    """)
  end

  @doc """
  Deliver instructions to reset a wallet_user password.
  """
  def deliver_reset_password_instructions(wallet_user, url) do
    deliver(wallet_user.email, "Reset password instructions", """

    ==============================

    Hi #{wallet_user.email},

    You can reset your password by visiting the URL below:

    #{url}

    If you didn't request this change, please ignore this.

    ==============================
    """)
  end

  @doc """
  Deliver instructions to update a wallet_user email.
  """
  def deliver_update_email_instructions(wallet_user, url) do
    deliver(wallet_user.email, "Update email instructions", """

    ==============================

    Hi #{wallet_user.email},

    You can change your email by visiting the URL below:

    #{url}

    If you didn't request this change, please ignore this.

    ==============================
    """)
  end
end
