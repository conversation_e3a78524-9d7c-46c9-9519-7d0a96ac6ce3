defmodule ServiceManager.WalletAccounts do
  @moduledoc """
  The WalletAccounts context.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Notifications.Sms.NotifySms

  alias ServiceManager.WalletAccounts.{WalletUser, WalletUserToken, WalletUserNotifier}
  alias ServiceManager.Accounts.OTP
  alias ServiceManager.WalletAccounts.WalletApiKey, as: APIKey
  alias ServiceManager.Queries.DefaultQueries
  alias ServiceManager.Queries.FilterQueries
  @query_params Application.compile_env(:service_manager, :query_params)

  def retrieve(params \\ @query_params) do
    WalletUser
    |> join(:left, [u], t in assoc(u, :wallet_tier))
    |> select([u, t], %{
      u
      | id: u.id,
        first_name: u.first_name,
        last_name: u.last_name,
        mobile_number: u.mobile_number,
        email: u.email,
        id_number: u.id_number,
        currency: u.currency,
        balance: u.balance,
        status: u.status,
        frozen: u.frozen,
        locked: u.locked,
        blocked: u.blocked,
        wallet_tier_id: u.wallet_tier_id,
        wallet_tier: t,
        inserted_at: u.inserted_at,
        updated_at: u.updated_at
    })
    |> DefaultQueries.sorting_query(params)
    |> search_filter(params)
    |> FilterQueries.filter_handler(params)
    |> DefaultQueries.pagination_query(params)
  end

  def search_filter(query, %{"search" => search}) when is_nil(search) or search == "", do: query

  def search_filter(query, %{"search" => search}) do
    query
    |> where(
      [u, t],
      fragment("lower(?) LIKE lower(?)", u.first_name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", u.last_name, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", u.mobile_number, ^"%#{search}%") or
        fragment("lower(?) LIKE lower(?)", u.email, ^"%#{search}%")
    )
  end

  def search_filter(query, _), do: query

  def verify_api_key(api_key) do
    case APIKey.find_by(api_key: api_key) do
      nil ->
        {:error, "Invalid API key"}

      api_key ->
        case WalletUser.find(api_key.wallet_user_id) do
          nil -> {:error, "API Not Linked"}
          user -> {:ok, user}
        end
    end
  end

  ## Database getters

  @doc """
  Generates an OTP for the given user.

  ## Examples

      iex> generate_otp_for_user(user)
      {:ok, "123456", ~U[2023-04-09 12:34:56Z]}

  """
  def generate_otp_for_user(%WalletUser{} = user) do
    OTP.generate_wallet_otp(user.id)
  end

  @doc """
  Validates the OTP for the given user.

  ## Examples

      iex> validate_otp_for_user(user, "123456")
      {:ok, :valid}

      iex> validate_otp_for_user(user, "invalid")
      {:error, :invalid_otp}

  """
  def validate_otp_for_user(%WalletUser{} = user, otp) do
    OTP.validate_wallet_otp(user.id, otp)
  end

  @doc """
  Gets a wallet_user by email.

  ## Examples

      iex> get_wallet_user_by_email("<EMAIL>")
      %WalletUser{}

      iex> get_wallet_user_by_email("<EMAIL>")
      nil

  """
  def get_wallet_user_by_email(email) when is_binary(email) do
    Repo.get_by(WalletUser, email: email)
  end

  @doc """
  Gets a wallet_user by email and password.

  ## Examples

      iex> get_wallet_user_by_email_and_password("<EMAIL>", "correct_password")
      %WalletUser{}

      iex> get_wallet_user_by_email_and_password("<EMAIL>", "invalid_password")
      nil

  """
  def get_wallet_user_by_email_and_password(email, password)
      when is_binary(email) and is_binary(password) do
    wallet_user = Repo.get_by(WalletUser, email: email)
    if WalletUser.valid_password?(wallet_user, password), do: wallet_user
  end

  def get_wallet_user_by_phone_and_password(mobile_number, password)
      when is_binary(mobile_number) and is_binary(password) do
    wallet_user = Repo.get_by(WalletUser, mobile_number: mobile_number) |> IO.inspect()
    if WalletUser.valid_password?(wallet_user, password), do: wallet_user
  end

  @doc """
  Gets a single wallet_user.

  Raises `Ecto.NoResultsError` if the WalletUser does not exist.

  ## Examples

      iex> get_wallet_user!(123)
      %WalletUser{}

      iex> get_wallet_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_wallet_user!(id, include_image \\ false) do
    base_query = from(u in WalletUser, where: u.id == ^id)

    query =
      if include_image do
        base_query
      else
        from u in base_query,
          left_join: t in assoc(u, :wallet_tier),
          select: %{
            u
            | id: u.id,
              session_id: u.session_id,
              first_name: u.first_name,
              last_name: u.last_name,
              mobile_number: u.mobile_number,
              email: u.email,
              id_number: u.id_number,
              currency: u.currency,
              balance: u.balance,
              status: u.status,
              frozen: u.frozen,
              locked: u.locked,
              blocked: u.blocked,
              wallet_tier_id: u.wallet_tier_id,
              wallet_tier: t,
              inserted_at: u.inserted_at,
              updated_at: u.updated_at
          }
      end

    query
    |> Repo.one!()
    |> Repo.preload(:wallet_tier)
  end

  @doc """
  Gets a list of wallet users by their IDs.
  Used by the online users feature to fetch wallet user details for presence tracking.
  """
  def list_wallet_users_by_ids(user_ids) when is_list(user_ids) do
    from(u in WalletUser,
      where: u.id in ^user_ids
    )
    |> Repo.all()
  end

  def list_wallet_users_by_ids(_), do: []

  @doc """
  Lists all wallet users with optional filtering and pagination.

  ## Examples

      iex> list_wallet_users()
      [%WalletUser{}, ...]

      iex> list_wallet_users(%{"page" => 1, "per_page" => 10})
      [%WalletUser{}, ...]

  """

  def list_wallet_users(params \\ %{}) do
    page = Map.get(params, "page", 1)
    per_page = Map.get(params, "per_page", 10)
    search = Map.get(params, "search", "")

    WalletUser
    |> join(:left, [u], t in assoc(u, :wallet_tier))
    |> select([u, t], %{
      u
      | id: u.id,
        first_name: u.first_name,
        last_name: u.last_name,
        mobile_number: u.mobile_number,
        email: u.email,
        id_number: u.id_number,
        currency: u.currency,
        balance: u.balance,
        status: u.status,
        frozen: u.frozen,
        locked: u.locked,
        blocked: u.blocked,
        wallet_tier_id: u.wallet_tier_id,
        wallet_tier: t,
        inserted_at: u.inserted_at,
        updated_at: u.updated_at
    })
    |> order_by([u], desc: u.inserted_at)
    |> filter_by_search(search)
    |> paginate(page, per_page)
    |> preload(:wallet_tier)
    |> Repo.all()
  end

  defp filter_by_search(query, ""), do: query

  defp filter_by_search(query, search) do
    search = "%#{search}%"

    from u in query,
      where:
        ilike(u.email, ^search) or
          ilike(u.first_name, ^search) or
          ilike(u.last_name, ^search) or
          ilike(u.mobile_number, ^search)
  end

  defp paginate(query, page, per_page) do
    offset = max(0, (page - 1) * per_page)

    from u in query,
      limit: ^per_page,
      offset: ^offset
  end

  @doc """
  Gets a wallet_user by mobile_number.

  ## Examples

      iex> get_wallet_user_by_mobile_number("**********")
      %WalletUser{}

      iex> ServiceManager.WalletAccounts.get_wallet_user_by_mobile_number("************")
      nil

  """
  def get_wallet_user_by_mobile_number(mobile_number) when is_binary(mobile_number) do
    Repo.get_by!(WalletUser, mobile_number: mobile_number)
  end

  ## Wallet user registration

  @doc """
  Registers a wallet_user.

  ## Examples

      iex> register_wallet_user(%{field: value})
      {:ok, %WalletUser{}}

      iex> register_wallet_user(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def register_wallet_user(attrs) do
    %WalletUser{}
    |> WalletUser.registration_changeset(attrs)
    |> Repo.insert()
  end

  def register_wallet_user(attrs, password) do
    %WalletUser{}
    |> WalletUser.registration_changeset(attrs)
    |> Repo.insert()
  end

  def register_wallet_user_with_message(attrs, password) do
    wallet =
      %WalletUser{}
      |> WalletUser.registration_changeset(attrs)
      |> Repo.insert()

    case wallet do
      nil ->
        {:error, wallet, %SMSMessage{}}

      wallet ->
        message_text =
          "Dear #{wallet.mobile_number}, Your new FDH wallet has been created successfully, your password is #{password}"

        message = SMSMessage.new() |> SMSMessage.new_message(message_text)
        {:ok, wallet, message}
    end
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking wallet_user changes.

  ## Examples

      iex> change_wallet_user_registration(wallet_user)
      %Ecto.Changeset{data: %WalletUser{}}

  """
  def change_wallet_user_registration(%WalletUser{} = wallet_user, attrs \\ %{}) do
    WalletUser.registration_changeset(wallet_user, attrs,
      hash_password: false,
      validate_email: false
    )
  end

  ## Settings

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the wallet_user email.

  ## Examples

      iex> change_wallet_user_email(wallet_user)
      %Ecto.Changeset{data: %WalletUser{}}

  """
  def change_wallet_user_email(wallet_user, attrs \\ %{}) do
    WalletUser.email_changeset(wallet_user, attrs, validate_email: false)
  end

  @doc """
  Emulates that the email will change without actually changing
  it in the database.

  ## Examples

      iex> apply_wallet_user_email(wallet_user, "valid password", %{email: ...})
      {:ok, %WalletUser{}}

      iex> apply_wallet_user_email(wallet_user, "invalid password", %{email: ...})
      {:error, %Ecto.Changeset{}}

  """
  def apply_wallet_user_email(wallet_user, password, attrs) do
    wallet_user
    |> WalletUser.email_changeset(attrs)
    |> WalletUser.validate_current_password(password)
    |> Ecto.Changeset.apply_action(:update)
  end

  @doc """
  Updates the wallet_user email using the given token.

  If the token matches, the wallet_user email is updated and the token is deleted.
  The confirmed_at date is also updated to the current time.
  """
  def update_wallet_user_email(wallet_user, token) do
    context = "change:#{wallet_user.email}"

    with {:ok, query} <- WalletUserToken.verify_change_email_token_query(token, context),
         %WalletUserToken{sent_to: email} <- Repo.one(query),
         {:ok, _} <- Repo.transaction(wallet_user_email_multi(wallet_user, email, context)) do
      :ok
    else
      _ -> :error
    end
  end

  defp wallet_user_email_multi(wallet_user, email, context) do
    changeset =
      wallet_user
      |> WalletUser.email_changeset(%{email: email})
      |> WalletUser.confirm_changeset()

    Ecto.Multi.new()
    |> Ecto.Multi.update(:wallet_user, changeset)
    |> Ecto.Multi.delete_all(
      :tokens,
      WalletUserToken.by_wallet_user_and_contexts_query(wallet_user, [context])
    )
  end

  @doc ~S"""
  Delivers the update email instructions to the given wallet_user.

  ## Examples

      iex> deliver_wallet_user_update_email_instructions(wallet_user, current_email, &url(~p"/walletusers/settings/confirm_email/#{&1}"))
      {:ok, %{to: ..., body: ...}}

  """
  def deliver_wallet_user_update_email_instructions(
        %WalletUser{} = wallet_user,
        current_email,
        update_email_url_fun
      )
      when is_function(update_email_url_fun, 1) do
    {encoded_token, wallet_user_token} =
      WalletUserToken.build_email_token(wallet_user, "change:#{current_email}")

    Repo.insert!(wallet_user_token)

    WalletUserNotifier.deliver_update_email_instructions(
      wallet_user,
      update_email_url_fun.(encoded_token)
    )
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the wallet_user password.

  ## Examples

      iex> change_wallet_user_password(wallet_user)
      %Ecto.Changeset{data: %WalletUser{}}

  """
  def change_wallet_user_password(wallet_user, attrs \\ %{}) do
    WalletUser.password_changeset(wallet_user, attrs, hash_password: false)
  end

  @doc """
  Updates the wallet_user password.

  ## Examples

      iex> update_wallet_user_password(wallet_user, "valid password", %{password: ...})
      {:ok, %WalletUser{}}

      iex> update_wallet_user_password(wallet_user, "invalid password", %{password: ...})
      {:error, %Ecto.Changeset{}}

  """
  def update_wallet_user_password(wallet_user, password, attrs) do
    changeset =
      wallet_user
      |> WalletUser.password_changeset(attrs)
      |> WalletUser.validate_current_password(password)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:wallet_user, changeset)
    |> Ecto.Multi.delete_all(
      :tokens,
      WalletUserToken.by_wallet_user_and_contexts_query(wallet_user, :all)
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{wallet_user: wallet_user}} -> {:ok, wallet_user}
      {:error, :wallet_user, changeset, _} -> {:error, changeset}
    end
  end

  ## Session

  @doc """
  Generates a session token.
  """
  def generate_wallet_user_session_token(wallet_user) do
    {token, wallet_user_token} = WalletUserToken.build_session_token(wallet_user)
    Repo.insert!(wallet_user_token)
    token
  end

  @doc """
  Gets the wallet_user with the given signed token.
  """
  def get_wallet_user_by_session_token(token) do
    {:ok, query} = WalletUserToken.verify_session_token_query(token)
    Repo.one(query)
  end

  @doc """
  Deletes the signed token with the given context.
  """
  def delete_wallet_user_session_token(token) do
    Repo.delete_all(WalletUserToken.by_token_and_context_query(token, "session"))
    :ok
  end

  ## Confirmation

  @doc ~S"""
  Delivers the confirmation email instructions to the given wallet_user.

  ## Examples

      iex> deliver_wallet_user_confirmation_instructions(wallet_user, &url(~p"/walletusers/confirm/#{&1}"))
      {:ok, %{to: ..., body: ...}}

      iex> deliver_wallet_user_confirmation_instructions(confirmed_wallet_user, &url(~p"/walletusers/confirm/#{&1}"))
      {:error, :already_confirmed}

  """
  def deliver_wallet_user_confirmation_instructions(
        %WalletUser{} = wallet_user,
        confirmation_url_fun
      )
      when is_function(confirmation_url_fun, 1) do
    if wallet_user.confirmed_at do
      {:error, :already_confirmed}
    else
      {encoded_token, wallet_user_token} =
        WalletUserToken.build_email_token(wallet_user, "confirm")

      Repo.insert!(wallet_user_token)

      WalletUserNotifier.deliver_confirmation_instructions(
        wallet_user,
        confirmation_url_fun.(encoded_token)
      )
    end
  end

  @doc """
  Confirms a wallet_user by the given token.

  If the token matches, the wallet_user account is marked as confirmed
  and the token is deleted.
  """
  def confirm_wallet_user(token) do
    with {:ok, query} <- WalletUserToken.verify_email_token_query(token, "confirm"),
         %WalletUser{} = wallet_user <- Repo.one(query),
         {:ok, %{wallet_user: wallet_user}} <-
           Repo.transaction(confirm_wallet_user_multi(wallet_user)) do
      {:ok, wallet_user}
    else
      _ -> :error
    end
  end

  defp confirm_wallet_user_multi(wallet_user) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:wallet_user, WalletUser.confirm_changeset(wallet_user))
    |> Ecto.Multi.delete_all(
      :tokens,
      WalletUserToken.by_wallet_user_and_contexts_query(wallet_user, ["confirm"])
    )
  end

  ## Reset password

  @doc ~S"""
  Delivers the reset password email to the given wallet_user.

  ## Examples

      iex> deliver_wallet_user_reset_password_instructions(wallet_user, &url(~p"/walletusers/reset_password/#{&1}"))
      {:ok, %{to: ..., body: ...}}

  """
  def deliver_wallet_user_reset_password_instructions(
        %WalletUser{} = wallet_user,
        reset_password_url_fun
      )
      when is_function(reset_password_url_fun, 1) do
    {encoded_token, wallet_user_token} =
      WalletUserToken.build_email_token(wallet_user, "reset_password")

    Repo.insert!(wallet_user_token)

    WalletUserNotifier.deliver_reset_password_instructions(
      wallet_user,
      reset_password_url_fun.(encoded_token)
    )
  end

  @doc """
  Gets the wallet_user by reset password token.

  ## Examples

      iex> get_wallet_user_by_reset_password_token("validtoken")
      %WalletUser{}

      iex> get_wallet_user_by_reset_password_token("invalidtoken")
      nil

  """
  def get_wallet_user_by_reset_password_token(token) do
    with {:ok, query} <- WalletUserToken.verify_email_token_query(token, "reset_password"),
         %WalletUser{} = wallet_user <- Repo.one(query) do
      wallet_user
    else
      _ -> nil
    end
  end

  @doc """
  Resets the wallet_user password.

  ## Examples

      iex> reset_wallet_user_password(wallet_user, %{password: "new long password", password_confirmation: "new long password"})
      {:ok, %WalletUser{}}

      iex> reset_wallet_user_password(wallet_user, %{password: "valid", password_confirmation: "not the same"})
      {:error, %Ecto.Changeset{}} accounts

  """
  def reset_wallet_user_password(wallet_user, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:wallet_user, WalletUser.password_changeset(wallet_user, attrs))
    |> Ecto.Multi.delete_all(
      :tokens,
      WalletUserToken.by_wallet_user_and_contexts_query(wallet_user, :all)
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{wallet_user: wallet_user}} -> {:ok, wallet_user}
      {:error, :wallet_user, changeset, _} -> {:error, changeset}
    end
  end

  @doc """
  Updates a wallet user's information, including KYC and tier information.
  Used primarily for wallet upgrades.

  ## Examples

      iex> update_wallet_user(wallet_user, %{field: new_value})
      {:ok, %WalletUser{}}

      iex> update_wallet_user(wallet_user, %{field: bad_value})
      {:error, %Ecto.Changeset{}}
  """
  def update_wallet_user(%WalletUser{} = wallet_user, attrs) do
    wallet_user
    |> WalletUser.upgrade_changeset(attrs)
    |> Repo.update()
  end
end
