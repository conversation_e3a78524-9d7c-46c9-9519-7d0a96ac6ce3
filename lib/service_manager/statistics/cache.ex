defmodule ServiceManager.Statistics.Cache do
  use GenServer
  alias ServiceManager.Repo
  import Ecto.Query

  @refresh_interval :timer.seconds(5)

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  def init(state) do
    schedule_refresh()
    {:ok, state}
  end

  def get_statistics do
    GenServer.call(__MODULE__, :get_statistics)
  end

  def handle_call(:get_statistics, _from, state) do
    {:reply, state, state}
  end

  def handle_info(:refresh, _state) do
    new_state = fetch_statistics()
    schedule_refresh()
    {:noreply, new_state}
  end

  defp schedule_refresh do
    Process.send_after(self(), :refresh, @refresh_interval)
  end

  defp fetch_statistics do
    query =
      from s in "statistics",
        select: %{
          total_users: s.total_users,
          total_accounts: s.total_accounts,
          total_cards: s.total_cards,
          total_transactions: s.total_transactions,
          total_bank_accounts: s.total_bank_accounts,
          total_beneficiaries: s.total_beneficiaries,
          total_fund_accounts: s.total_fund_accounts,
          total_transfers: s.total_transfers,
          total_tokens: s.total_tokens,
          updated_at: s.updated_at
        },
        order_by: [desc: s.updated_at],
        limit: 1

    case Repo.one(query) do
      nil ->
        %{
          total_users: 0,
          total_accounts: 0,
          total_cards: 0,
          total_transactions: 0,
          total_bank_accounts: 0,
          total_beneficiaries: 0,
          total_fund_accounts: 0,
          total_transfers: 0,
          total_tokens: 0,
          updated_at: nil
        }

      stats ->
        stats
    end
  end
end
