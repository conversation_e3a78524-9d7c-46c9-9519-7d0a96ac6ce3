defmodule ServiceManager.Notifications do
  import Ecto.Query
  alias ServiceManager.Repo

  alias ServiceManager.Notifications.{
    EmailNotification,
    SMSNotification,
    PushNotification,
    Dispatcher
  }

  alias ServiceManager.Accounts.User

  def create_email_notification(attrs \\ %{}) do
    %EmailNotification{}
    |> EmailNotification.changeset(attrs)
    |> Repo.insert()
    |> dispatch_notification(:email)
  end

  def create_sms_notification(attrs \\ %{}) do
    %SMSNotification{}
    |> SMSNotification.changeset(attrs)
    |> Repo.insert()
    |> dispatch_notification(:sms)
  end

  def create_push_notification(attrs \\ %{}) do
    %PushNotification{}
    |> PushNotification.changeset(attrs)
    |> Repo.insert()
    |> dispatch_notification(:push)
  end

  def send_notification(user_id, type, content) do
    user = Repo.get(User, user_id)

    case type do
      :email when user.email_notifications ->
        create_email_notification(Map.put(content, :user_id, user_id))

      :sms when user.sms_notifications ->
        create_sms_notification(Map.put(content, :user_id, user_id))

      :push when user.push_notifications ->
        create_push_notification(Map.put(content, :user_id, user_id))

      _ ->
        {:error, :notification_type_disabled}
    end
  end

  defp dispatch_notification({:ok, notification}, type) do
    Dispatcher.dispatch(%{notification | type: type})
    {:ok, notification}
  end

  defp dispatch_notification(error, _type), do: error

  def update_email_notification(%EmailNotification{} = notification, attrs) do
    notification
    |> EmailNotification.changeset(attrs)
    |> Repo.update()
  end

  def update_sms_notification(%SMSNotification{} = notification, attrs) do
    notification
    |> SMSNotification.changeset(attrs)
    |> Repo.update()
  end

  def update_push_notification(%PushNotification{} = notification, attrs) do
    notification
    |> PushNotification.changeset(attrs)
    |> Repo.update()
  end

  # Add more functions as needed for querying and managing notifications
end
