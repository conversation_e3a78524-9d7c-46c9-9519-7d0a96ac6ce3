defmodule ServiceManager.Schemas.Billers.BillerConfig do
  @moduledoc """
  Schema for biller configurations storing API endpoints, credentials, and settings
  """
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :biller_type,
             :biller_name,
             :display_name,
             :is_active,
             :base_url,
             :endpoints,
             :authentication,
             :default_currency,
             :supported_currencies,
             :timeout_ms,
             :retry_attempts,
             :features,
             :validation_rules,
             :description,
             :inserted_at,
             :updated_at
           ]}

  schema "biller_configs" do
    # Biller Identity
    field :biller_type, :string
    field :biller_name, :string
    field :display_name, :string
    field :description, :string
    field :is_active, :boolean, default: true

    # API Configuration
    field :base_url, :string
    field :endpoints, :map, default: %{}
    field :authentication, :map, default: %{}
    
    # Transaction Settings
    field :default_currency, :string, default: "MWK"
    field :supported_currencies, {:array, :string}, default: ["MWK"]
    field :timeout_ms, :integer, default: 30000
    field :retry_attempts, :integer, default: 3
    
    # Features and Capabilities
    field :features, :map, default: %{}
    field :validation_rules, :map, default: %{}

    timestamps()
  end

  @doc false
  def changeset(biller_config, attrs) do
    biller_config
    |> cast(attrs, [
      :biller_type,
      :biller_name,
      :display_name,
      :description,
      :is_active,
      :base_url,
      :endpoints,
      :authentication,
      :default_currency,
      :supported_currencies,
      :timeout_ms,
      :retry_attempts,
      :features,
      :validation_rules
    ])
    |> validate_required([
      :biller_type,
      :biller_name,
      :display_name,
      :base_url,
      :endpoints
    ])
    |> validate_inclusion(:biller_type, [
      "register_general",
      "bwb_postpaid",
      "lwb_postpaid",
      "srwb_postpaid",
      "srwb_prepaid",
      "masm",
      "airtel_validation",
      "tnm_bundles"
    ])
    |> validate_inclusion(:default_currency, ["MWK", "USD", "EUR", "GBP", "ZAR"])
    |> validate_number(:timeout_ms, greater_than: 0, less_than: 300000)
    |> validate_number(:retry_attempts, greater_than_or_equal_to: 0, less_than: 10)
    |> validate_format(:base_url, ~r/^https?:\/\//)
    |> unique_constraint(:biller_type)
    |> unique_constraint(:biller_name)
  end

  @doc """
  Create a new biller configuration
  """
  def create_changeset(attrs) do
    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Update an existing biller configuration
  """
  def update_changeset(biller_config, attrs) do
    biller_config
    |> changeset(attrs)
  end

  @doc """
  Activate a biller
  """
  def activate_changeset(biller_config) do
    biller_config
    |> changeset(%{is_active: true})
  end

  @doc """
  Deactivate a biller
  """
  def deactivate_changeset(biller_config) do
    biller_config
    |> changeset(%{is_active: false})
  end

  @doc """
  Default configurations for each biller type
  """
  def default_configs do
    %{
      "register_general" => %{
        biller_type: "register_general",
        biller_name: "Register General",
        display_name: "Register General",
        description: "General registration and invoice services",
        endpoints: %{
          "get_invoice" => "/api/billers/register-general/v1/accounts/{account_number}",
          "confirm_invoice" => "/api/billers/register-general/v1/transactions"
        },
        authentication: %{
          "type" => "basic",
          "username" => "admin",
          "password" => "admin"
        },
        features: %{
          "supports_invoice" => true,
          "supports_payment" => true,
          "requires_confirmation" => true
        },
        validation_rules: %{
          "account_number" => %{
            "required" => true,
            "min_length" => 3,
            "max_length" => 20
          }
        }
      },
      "bwb_postpaid" => %{
        biller_type: "bwb_postpaid",
        biller_name: "BWB Postpaid",
        display_name: "Blantyre Water Board - Postpaid",
        description: "Blantyre Water Board postpaid services",
        endpoints: %{
          "account_details" => "/esb/api/bwb-postpaid-test/v1/accounts/{account_number}",
          "post_transaction" => "/esb/api/bwb-postpaid-test/v1/transactions"
        },
        authentication: %{
          "type" => "basic",
          "username" => "admin",
          "password" => "admin"
        },
        features: %{
          "supports_account_lookup" => true,
          "supports_payment" => true,
          "postpaid" => true
        },
        validation_rules: %{
          "account_number" => %{
            "required" => true,
            "pattern" => "^[0-9]{8,12}$"
          }
        }
      },
      "lwb_postpaid" => %{
        biller_type: "lwb_postpaid",
        biller_name: "LWB Postpaid",
        display_name: "Lilongwe Water Board - Postpaid",
        description: "Lilongwe Water Board postpaid services",
        endpoints: %{
          "account_details" => "/esb/api/lwb-postpaid-test/v1/accounts/{account_number}",
          "post_transaction" => "/esb/api/lwb-postpaid-test/v1/accounts/{account_number}"
        },
        authentication: %{
          "type" => "basic",
          "username" => "admin",
          "password" => "admin"
        },
        features: %{
          "supports_account_lookup" => true,
          "supports_payment" => true,
          "postpaid" => true
        },
        validation_rules: %{
          "account_number" => %{
            "required" => true,
            "pattern" => "^[0-9]{6,10}$"
          }
        }
      },
      "srwb_postpaid" => %{
        biller_type: "srwb_postpaid",
        biller_name: "SRWB Postpaid",
        display_name: "Southern Region Water Board - Postpaid",
        description: "Southern Region Water Board postpaid services",
        endpoints: %{
          "account_details" => "/esb/api/lwb-postpaid-test/v1/accounts/{account_number}",
          "post_transaction" => "/esb/api/srwb-postpaid-test/v1/transactions"
        },
        authentication: %{
          "type" => "basic",
          "username" => "admin",
          "password" => "admin"
        },
        features: %{
          "supports_account_lookup" => true,
          "supports_payment" => true,
          "postpaid" => true
        },
        validation_rules: %{
          "account_number" => %{
            "required" => true,
            "pattern" => "^[A-Z0-9]{6,12}$"
          }
        }
      },
      "srwb_prepaid" => %{
        biller_type: "srwb_prepaid",
        biller_name: "SRWB Prepaid",
        display_name: "Southern Region Water Board - Prepaid",
        description: "Southern Region Water Board prepaid services",
        endpoints: %{
          "get_invoice" => "/esb/api/srwb-prepaid-test/v1/accounts/{account_number}",
          "confirm_invoice" => "/esb/api/srwb-prepaid-test/v1/transactions"
        },
        authentication: %{
          "type" => "basic",
          "username" => "admin",
          "password" => "admin"
        },
        features: %{
          "supports_invoice" => true,
          "supports_payment" => true,
          "prepaid" => true,
          "requires_confirmation" => true
        },
        validation_rules: %{
          "account_number" => %{
            "required" => true,
            "pattern" => "^[0-9]{10,15}$"
          }
        }
      },
      "masm" => %{
        biller_type: "masm",
        biller_name: "MASM",
        display_name: "Malawi Savings Bank",
        description: "Malawi Savings Bank services",
        endpoints: %{
          "account_details" => "/esb/api/masm-test/v1/accounts/{account_number}",
          "confirm_invoice" => "/esb/api/masm-test/v1/transactions"
        },
        authentication: %{
          "type" => "basic",
          "username" => "admin",
          "password" => "admin"
        },
        features: %{
          "supports_account_lookup" => true,
          "supports_payment" => true,
          "requires_type_parameter" => true
        },
        validation_rules: %{
          "account_number" => %{
            "required" => true,
            "pattern" => "^[0-9]{8,12}$"
          },
          "type" => %{
            "required" => true,
            "allowed_values" => ["M", "S", "C"]
          }
        }
      },
      "airtel_validation" => %{
        biller_type: "airtel_validation",
        biller_name: "Airtel Validation",
        display_name: "Airtel Money Validation",
        description: "Airtel Money account validation services",
        endpoints: %{
          "validation" => "/esb/api/airtel-validation/v1/accounts/{account_number}"
        },
        authentication: %{
          "type" => "basic",
          "username" => "admin",
          "password" => "admin"
        },
        features: %{
          "supports_validation" => true,
          "validation_only" => true
        },
        validation_rules: %{
          "account_number" => %{
            "required" => true,
            "pattern" => "^[0-9]{9,10}$"
          }
        }
      },
      "tnm_bundles" => %{
        biller_type: "tnm_bundles",
        biller_name: "TNM Bundles",
        display_name: "TNM Internet Bundles",
        description: "TNM Internet bundle services",
        endpoints: %{
          "bundle_details" => "/esb/api/internetbundles/v1/{bundle_id}",
          "confirm_bundle" => "/esb/api/internetbundles/v1/{transaction_id}/{phone_number}/{bundle_id}"
        },
        authentication: %{
          "type" => "basic",
          "username" => "admin",
          "password" => "admin"
        },
        features: %{
          "supports_bundles" => true,
          "requires_phone_number" => true,
          "requires_transaction_id" => true
        },
        validation_rules: %{
          "bundle_id" => %{
            "required" => true,
            "pattern" => "^[0-9]{3,10}$"
          },
          "phone_number" => %{
            "required" => true,
            "pattern" => "^[0-9]{9,10}$"
          }
        }
      }
    }
  end

  @doc """
  Get endpoint URL for a specific biller and operation
  """
  def get_endpoint_url(biller_config, operation, params \\ %{}) do
    endpoint_template = get_in(biller_config.endpoints, [operation])
    
    if endpoint_template do
      url = biller_config.base_url <> endpoint_template
      replace_url_params(url, params)
    else
      nil
    end
  end

  defp replace_url_params(url, params) do
    Enum.reduce(params, url, fn {key, value}, acc ->
      String.replace(acc, "{#{key}}", to_string(value))
    end)
  end

  @doc """
  Check if a biller supports a specific feature
  """
  def supports_feature?(biller_config, feature) do
    get_in(biller_config.features, [feature]) == true
  end

  @doc """
  Validate account number according to biller rules
  """
  def validate_account_number(biller_config, account_number) do
    rules = get_in(biller_config.validation_rules, ["account_number"])
    
    cond do
      is_nil(rules) -> :ok
      !rules["required"] && is_nil(account_number) -> :ok
      rules["required"] && (is_nil(account_number) || account_number == "") -> {:error, "Account number is required"}
      rules["pattern"] && !Regex.match?(~r/#{rules["pattern"]}/, account_number) -> {:error, "Invalid account number format"}
      rules["min_length"] && String.length(account_number) < rules["min_length"] -> {:error, "Account number too short"}
      rules["max_length"] && String.length(account_number) > rules["max_length"] -> {:error, "Account number too long"}
      true -> :ok
    end
  end
end