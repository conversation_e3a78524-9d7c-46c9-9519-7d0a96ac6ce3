defmodule ServiceManager.Billers.BillersService do
  @moduledoc """
  Service for managing biller transactions and operations
  """
  
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Billers.{BillerTransaction, BillerConfig}
  alias ServiceManager.Pool.RequestPool
  alias Ecto.Multi
  
  require Logger

  # Transaction Types
  @account_details "account_details"
  @post_transaction "post_transaction"
  @get_invoice "get_invoice"
  @confirm_invoice "confirm_invoice"
  @bundle_details "bundle_details"
  @confirm_bundle "confirm_bundle"
  @validation "validation"

  @doc """
  Create a new biller transaction record
  """
  def create_transaction(attrs) do
    %BillerTransaction{}
    |> BillerTransaction.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Get biller transaction by ID
  """
  def get_transaction(id) do
    Repo.get(BillerTransaction, id)
  end

  @doc """
  Get biller transaction by our_transaction_id
  """
  def get_transaction_by_reference(our_transaction_id) do
    Repo.get_by(BillerTransaction, our_transaction_id: our_transaction_id)
  end

  @doc """
  Update biller transaction
  """
  def update_transaction(transaction, attrs) do
    transaction
    |> BillerTransaction.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Mark transaction as completed
  """
  def complete_transaction(transaction, response_payload) do
    transaction
    |> BillerTransaction.complete_transaction(response_payload)
    |> Repo.update()
  end

  @doc """
  Mark transaction as failed
  """
  def fail_transaction(transaction, error_message, response_payload \\ nil) do
    transaction
    |> BillerTransaction.fail_transaction(error_message, response_payload)
    |> Repo.update()
  end

  @doc """
  Process account details request for any biller
  """
  def process_account_details(biller_type, account_number, opts \\ []) do
    our_transaction_id = generate_transaction_id()
    
    # Convert MASM account type to valid constraint value
    account_type = case opts[:account_type] do
      "M" -> "account"
      type -> type
    end
    
    transaction_attrs = %{
      biller_type: biller_type,
      biller_name: get_biller_name(biller_type),
      account_number: account_number,
      our_transaction_id: our_transaction_id,
      transaction_type: @account_details,
      status: "pending",
      account_type: account_type # For MASM
    }

    Multi.new()
    |> Multi.insert(:transaction, BillerTransaction.account_details_changeset(%BillerTransaction{}, transaction_attrs))
    |> Multi.run(:process, fn _repo, %{transaction: transaction} ->
      case make_account_details_request(transaction) do
        {:ok, response} ->
          complete_transaction(transaction, response)
        {:error, error} ->
          fail_transaction(transaction, error)
      end
    end)
    |> Repo.transaction()
  end

  @doc """
  Process payment transaction for any biller
  """
  def process_payment(biller_type, payment_attrs) do
    our_transaction_id = generate_transaction_id()
    
    transaction_attrs = Map.merge(payment_attrs, %{
      biller_type: biller_type,
      biller_name: get_biller_name(biller_type),
      our_transaction_id: our_transaction_id,
      transaction_type: @post_transaction,
      status: "pending"
    })

    Multi.new()
    |> Multi.insert(:transaction, BillerTransaction.payment_changeset(%BillerTransaction{}, transaction_attrs))
    |> Multi.run(:process, fn _repo, %{transaction: transaction} ->
      case make_payment_request(transaction) do
        {:ok, response} ->
          complete_transaction(transaction, response)
        {:error, error} ->
          fail_transaction(transaction, error)
      end
    end)
    |> Repo.transaction()
  end

  @doc """
  Process invoice request (Register General, SRWB Prepaid)
  """
  def process_invoice_request(biller_type, account_number) do
    our_transaction_id = generate_transaction_id()
    
    transaction_attrs = %{
      biller_type: biller_type,
      biller_name: get_biller_name(biller_type),
      account_number: account_number,
      our_transaction_id: our_transaction_id,
      transaction_type: @get_invoice,
      status: "pending"
    }

    Multi.new()
    |> Multi.insert(:transaction, BillerTransaction.invoice_changeset(%BillerTransaction{}, transaction_attrs))
    |> Multi.run(:process, fn _repo, %{transaction: transaction} ->
      case make_invoice_request(transaction) do
        {:ok, response} ->
          complete_transaction(transaction, response)
        {:error, error} ->
          fail_transaction(transaction, error)
      end
    end)
    |> Repo.transaction()
  end

  @doc """
  Process invoice confirmation (Register General, SRWB Prepaid)
  """
  def process_invoice_confirmation(biller_type, confirmation_attrs) do
    our_transaction_id = generate_transaction_id()
    
    transaction_attrs = Map.merge(confirmation_attrs, %{
      biller_type: biller_type,
      biller_name: get_biller_name(biller_type),
      our_transaction_id: our_transaction_id,
      transaction_type: @confirm_invoice,
      status: "pending"
    })

    Multi.new()
    |> Multi.insert(:transaction, BillerTransaction.invoice_changeset(%BillerTransaction{}, transaction_attrs))
    |> Multi.run(:process, fn _repo, %{transaction: transaction} ->
      case make_invoice_confirmation_request(transaction) do
        {:ok, response} ->
          complete_transaction(transaction, response)
        {:error, error} ->
          fail_transaction(transaction, error)
      end
    end)
    |> Repo.transaction()
  end

  @doc """
  Process bundle details request (TNM Bundles)
  """
  def process_bundle_details(bundle_id) do
    our_transaction_id = generate_transaction_id()
    
    transaction_attrs = %{
      biller_type: "tnm_bundles",
      biller_name: "TNM Bundles",
      bundle_id: bundle_id,
      our_transaction_id: our_transaction_id,
      transaction_type: @bundle_details,
      status: "pending"
    }

    Multi.new()
    |> Multi.insert(:transaction, BillerTransaction.bundle_changeset(%BillerTransaction{}, transaction_attrs))
    |> Multi.run(:process, fn _repo, %{transaction: transaction} ->
      case make_bundle_details_request(transaction) do
        {:ok, response} ->
          complete_transaction(transaction, response)
        {:error, error} ->
          fail_transaction(transaction, error)
      end
    end)
    |> Repo.transaction()
  end

  @doc """
  Process bundle purchase confirmation (TNM Bundles)
  """
  def process_bundle_confirmation(bundle_attrs) do
    our_transaction_id = generate_transaction_id()
    
    transaction_attrs = Map.merge(bundle_attrs, %{
      biller_type: "tnm_bundles",
      biller_name: "TNM Bundles",
      our_transaction_id: our_transaction_id,
      transaction_type: @confirm_bundle,
      status: "pending"
    })

    Multi.new()
    |> Multi.insert(:transaction, BillerTransaction.bundle_changeset(%BillerTransaction{}, transaction_attrs))
    |> Multi.run(:process, fn _repo, %{transaction: transaction} ->
      case make_bundle_confirmation_request(transaction) do
        {:ok, response} ->
          complete_transaction(transaction, response)
        {:error, error} ->
          fail_transaction(transaction, error)
      end
    end)
    |> Repo.transaction()
  end

  @doc """
  Process validation request (Airtel Money)
  """
  def process_validation(account_number) do
    our_transaction_id = generate_transaction_id()
    
    transaction_attrs = %{
      biller_type: "airtel_validation",
      biller_name: "Airtel Validation",
      account_number: account_number,
      our_transaction_id: our_transaction_id,
      transaction_type: @validation,
      status: "pending"
    }

    Multi.new()
    |> Multi.insert(:transaction, BillerTransaction.changeset(%BillerTransaction{}, transaction_attrs))
    |> Multi.run(:process, fn _repo, %{transaction: transaction} ->
      case make_validation_request(transaction) do
        {:ok, response} ->
          complete_transaction(transaction, response)
        {:error, error} ->
          fail_transaction(transaction, error)
      end
    end)
    |> Repo.transaction()
  end

  @doc """
  Get all transactions for a specific biller
  """
  def get_transactions_by_biller(biller_type, opts \\ []) do
    limit = opts[:limit] || 50
    offset = opts[:offset] || 0
    
    import Ecto.Query
    
    BillerTransaction
    |> where(biller_type: ^biller_type)
    |> order_by(desc: :inserted_at)
    |> limit(^limit)
    |> offset(^offset)
    |> Repo.all()
  end

  @doc """
  Get transactions by status
  """
  def get_transactions_by_status(status, opts \\ []) do
    limit = opts[:limit] || 50
    offset = opts[:offset] || 0
    
    import Ecto.Query
    
    BillerTransaction
    |> where(status: ^status)
    |> order_by(desc: :inserted_at)
    |> limit(^limit)
    |> offset(^offset)
    |> Repo.all()
  end

  @doc """
  Get transactions by account number
  """
  def get_transactions_by_account(account_number, opts \\ []) do
    limit = opts[:limit] || 50
    offset = opts[:offset] || 0
    
    import Ecto.Query
    
    BillerTransaction
    |> where(account_number: ^account_number)
    |> order_by(desc: :inserted_at)
    |> limit(^limit)
    |> offset(^offset)
    |> Repo.all()
  end

  @doc """
  Get all transactions with pagination
  """
  def get_all_transactions(opts \\ []) do
    limit = opts[:limit] || 50
    offset = opts[:offset] || 0
    
    import Ecto.Query
    
    BillerTransaction
    |> order_by(desc: :inserted_at)
    |> limit(^limit)
    |> offset(^offset)
    |> Repo.all()
  end

  @doc """
  Retry a failed transaction
  """
  def retry_transaction(transaction_id) do
    case get_transaction(transaction_id) do
      nil ->
        {:error, "Transaction not found"}
      
      transaction ->
        case transaction.status do
          "failed" ->
            # Reset status to pending and retry
            transaction
            |> BillerTransaction.changeset(%{
              status: "pending",
              processed_at: nil,
              error_message: nil
            })
            |> Repo.update()
            |> case do
              {:ok, updated_transaction} ->
                # Re-process based on transaction type
                retry_based_on_type(updated_transaction)
              error ->
                error
            end
          
          _ ->
            {:error, "Only failed transactions can be retried"}
        end
    end
  end

  # Private functions

  defp generate_transaction_id do
    timestamp = DateTime.utc_now()
    |> DateTime.to_unix(:millisecond)
    |> Integer.to_string()
    |> String.slice(-10..-1)
    
    random_part = :crypto.strong_rand_bytes(4)
    |> Base.encode32()
    |> String.slice(0..3)
    
    "TT" <> timestamp <> random_part
  end

  defp get_biller_name(biller_type) do
    case biller_type do
      "register_general" -> "Register General"
      "bwb_postpaid" -> "BWB Postpaid"
      "lwb_postpaid" -> "LWB Postpaid"
      "srwb_postpaid" -> "SRWB Postpaid"
      "srwb_prepaid" -> "SRWB Prepaid"
      "masm" -> "MASM"
      "airtel_validation" -> "Airtel Validation"
      "tnm_bundles" -> "TNM Bundles"
      _ -> String.replace(biller_type, "_", " ") |> String.capitalize()
    end
  end

  defp make_account_details_request(transaction) do
    Logger.info("Making account details request for #{transaction.biller_type} - #{transaction.account_number}")
    
    with {:ok, config} <- get_biller_config(transaction.biller_type),
         {:ok, url} <- build_endpoint_url(config, "account_details", transaction.account_number),
         {:ok, headers} <- build_headers(config),
         request_payload <- build_account_details_payload(transaction) do
      
      # Update transaction with request details
      update_transaction(transaction, %{
        request_payload: request_payload,
        api_endpoint: url,
        status: "processing"
      })
      
      # Make actual HTTP request using RequestPool
      case RequestPool.create_request(%{
        method: "GET",
        url: url,
        headers: headers,
        body: request_payload,
        name: "biller_account_details",
        reference: "account_details_#{transaction.our_transaction_id}"
      }) do
        {:ok, request} ->
          wait_for_request_completion(request, transaction)
        
        error ->
          Logger.error("Failed to create account details request: #{inspect(error)}")
          {:error, "Failed to create request"}
      end
    else
      error ->
        Logger.error("Error preparing account details request: #{inspect(error)}")
        {:error, "Failed to prepare request"}
    end
  end

  defp make_payment_request(transaction) do
    Logger.info("Making payment request for #{transaction.biller_type} - #{transaction.account_number}")
    
    with {:ok, config} <- get_biller_config(transaction.biller_type),
         {:ok, url} <- build_endpoint_url(config, "post_transaction", transaction.account_number),
         {:ok, headers} <- build_headers(config),
         request_payload <- build_payment_payload(transaction) do
      
      # Update transaction with request details
      update_transaction(transaction, %{
        request_payload: request_payload,
        api_endpoint: url,
        status: "processing"
      })
      
      # Make actual HTTP request using RequestPool
      case RequestPool.create_request(%{
        method: "POST",
        url: url,
        headers: headers,
        body: request_payload,
        name: "biller_payment",
        reference: "payment_#{transaction.our_transaction_id}"
      }) do
        {:ok, request} ->
          wait_for_request_completion(request, transaction)
        
        error ->
          Logger.error("Failed to create payment request: #{inspect(error)}")
          {:error, "Failed to create request"}
      end
    else
      error ->
        Logger.error("Error preparing payment request: #{inspect(error)}")
        {:error, "Failed to prepare request"}
    end
  end

  defp make_invoice_request(transaction) do
    Logger.info("Making invoice request for #{transaction.biller_type} - #{transaction.account_number}")
    
    request_payload = build_invoice_payload(transaction)
    update_transaction(transaction, %{
      request_payload: request_payload,
      status: "processing"
    })
    
    simulate_api_response(transaction)
  end

  defp make_invoice_confirmation_request(transaction) do
    Logger.info("Making invoice confirmation for #{transaction.biller_type} - #{transaction.account_number}")
    
    request_payload = build_invoice_confirmation_payload(transaction)
    update_transaction(transaction, %{
      request_payload: request_payload,
      status: "processing"
    })
    
    simulate_api_response(transaction)
  end

  defp make_bundle_details_request(transaction) do
    Logger.info("Making bundle details request for bundle #{transaction.bundle_id}")
    
    request_payload = build_bundle_details_payload(transaction)
    update_transaction(transaction, %{
      request_payload: request_payload,
      status: "processing"
    })
    
    simulate_api_response(transaction)
  end

  defp make_bundle_confirmation_request(transaction) do
    Logger.info("Making bundle confirmation for bundle #{transaction.bundle_id}")
    
    request_payload = build_bundle_confirmation_payload(transaction)
    update_transaction(transaction, %{
      request_payload: request_payload,
      status: "processing"
    })
    
    simulate_api_response(transaction)
  end

  defp make_validation_request(transaction) do
    Logger.info("Making validation request for #{transaction.account_number}")
    
    with {:ok, config} <- get_biller_config(transaction.biller_type),
         {:ok, url} <- build_endpoint_url(config, "validation", transaction.account_number),
         {:ok, headers} <- build_headers(config),
         request_payload <- build_validation_payload(transaction) do
      
      # Update transaction with request details
      update_transaction(transaction, %{
        request_payload: request_payload,
        api_endpoint: url,
        status: "processing"
      })
      
      # Make actual HTTP request using RequestPool
      case RequestPool.create_request(%{
        method: "GET",
        url: url,
        headers: headers,
        body: request_payload,
        name: "biller_validation",
        reference: "validation_#{transaction.our_transaction_id}"
      }) do
        {:ok, request} ->
          wait_for_request_completion(request, transaction)
        
        error ->
          Logger.error("Failed to create validation request: #{inspect(error)}")
          {:error, "Failed to create request"}
      end
    else
      error ->
        Logger.error("Error preparing validation request: #{inspect(error)}")
        {:error, "Failed to prepare request"}
    end
  end

  defp build_account_details_payload(transaction) do
    %{
      "endpoint" => get_endpoint_for_transaction(transaction),
      "method" => "GET",
      "headers" => %{
        "Accept" => "application/xml",
        "Authorization" => "Basic YWRtaW46YWRtaW4="
      },
      "account_number" => transaction.account_number,
      "account_type" => transaction.account_type
    }
  end

  defp build_payment_payload(transaction) do
    %{
      "endpoint" => get_endpoint_for_transaction(transaction),
      "method" => "POST",
      "headers" => %{
        "Content-Type" => "application/xml",
        "Authorization" => "Basic YWRtaW46YWRtaW4="
      },
      "body" => build_payment_xml(transaction)
    }
  end

  defp build_invoice_payload(transaction) do
    %{
      "endpoint" => get_endpoint_for_transaction(transaction),
      "method" => "GET",
      "headers" => %{
        "Accept" => "application/xml",
        "Authorization" => "Basic YWRtaW46YWRtaW4="
      },
      "account_number" => transaction.account_number
    }
  end

  defp build_invoice_confirmation_payload(transaction) do
    %{
      "endpoint" => get_endpoint_for_transaction(transaction),
      "method" => "POST",
      "headers" => %{
        "Content-Type" => "application/xml",
        "Authorization" => "Basic YWRtaW46YWRtaW4="
      },
      "body" => build_payment_xml(transaction)
    }
  end

  defp build_bundle_details_payload(transaction) do
    %{
      "endpoint" => get_endpoint_for_transaction(transaction),
      "method" => "GET",
      "headers" => %{
        "Authorization" => "Basic YWRtaW46YWRtaW4="
      },
      "bundle_id" => transaction.bundle_id
    }
  end

  defp build_bundle_confirmation_payload(transaction) do
    %{
      "endpoint" => get_endpoint_for_transaction(transaction),
      "method" => "GET",
      "headers" => %{
        "Authorization" => "Basic YWRtaW46YWRtaW4="
      },
      "bundle_id" => transaction.bundle_id,
      "phone_number" => transaction.account_number,
      "transaction_id" => transaction.our_transaction_id
    }
  end

  defp build_validation_payload(transaction) do
    %{
      "endpoint" => get_endpoint_for_transaction(transaction),
      "method" => "GET",
      "headers" => %{
        "Accept" => "application/xml",
        "Authorization" => "Basic YWRtaW46YWRtaW4="
      },
      "account_number" => transaction.account_number
    }
  end

  defp build_payment_xml(transaction) do
    """
    <?xml version="1.0"?>
    <post-payment-request>
        <our-transaction-id>#{transaction.our_transaction_id}</our-transaction-id>
        <account-number>#{transaction.account_number}</account-number>
        <amount>#{transaction.amount}</amount>
        <currency>#{transaction.currency}</currency>
        <credit-account>#{transaction.credit_account}</credit-account>
        <credit-account-type>#{transaction.credit_account_type}</credit-account-type>
        <debit-account>#{transaction.debit_account}</debit-account>
        <debit-account-type>#{transaction.debit_account_type}</debit-account-type>
        <customer-account-number>#{transaction.customer_account_number}</customer-account-number>
        <customer-account-name>#{transaction.customer_account_name}</customer-account-name>
        #{if transaction.account_type, do: "<type>#{transaction.account_type}</type>", else: ""}
    </post-payment-request>
    """
  end

  defp get_endpoint_for_transaction(transaction) do
    base_endpoints = %{
      "register_general" => %{
        "get_invoice" => "/api/billers/register-general/v1/accounts/#{transaction.account_number}",
        "confirm_invoice" => "/api/billers/register-general/v1/transactions"
      },
      "bwb_postpaid" => %{
        "account_details" => "/esb/api/bwb-postpaid-test/v1/accounts/#{transaction.account_number}",
        "post_transaction" => "/esb/api/bwb-postpaid-test/v1/transactions"
      },
      "lwb_postpaid" => %{
        "account_details" => "/esb/api/lwb-postpaid-test/v1/accounts/#{transaction.account_number}",
        "post_transaction" => "/esb/api/lwb-postpaid-test/v1/accounts/#{transaction.account_number}"
      },
      "srwb_postpaid" => %{
        "account_details" => "/esb/api/lwb-postpaid-test/v1/accounts/#{transaction.account_number}",
        "post_transaction" => "/esb/api/srwb-postpaid-test/v1/transactions"
      },
      "srwb_prepaid" => %{
        "get_invoice" => "/esb/api/srwb-prepaid-test/v1/accounts/#{transaction.account_number}",
        "confirm_invoice" => "/esb/api/srwb-prepaid-test/v1/transactions"
      },
      "masm" => %{
        "account_details" => "/esb/api/masm-test/v1/accounts/#{transaction.account_number}?type=#{transaction.account_type}",
        "confirm_invoice" => "/esb/api/masm-test/v1/transactions"
      },
      "airtel_validation" => %{
        "validation" => "/esb/api/airtel-validation/v1/accounts/#{transaction.account_number}"
      },
      "tnm_bundles" => %{
        "bundle_details" => "/esb/api/internetbundles/v1/#{transaction.bundle_id}",
        "confirm_bundle" => "/esb/api/internetbundles/v1/#{transaction.our_transaction_id}/#{transaction.account_number}/#{transaction.bundle_id}"
      }
    }

    get_in(base_endpoints, [transaction.biller_type, transaction.transaction_type])
  end

  defp simulate_api_response(transaction) do
    # Simulate different response scenarios
    case :rand.uniform(10) do
      n when n <= 7 ->
        # 70% success rate
        {:ok, %{
          "status" => "success",
          "transaction_id" => transaction.our_transaction_id,
          "account_number" => transaction.account_number,
          "timestamp" => DateTime.utc_now() |> DateTime.to_iso8601()
        }}
      
      n when n <= 9 ->
        # 20% temporary failure
        {:error, "Temporary service unavailable"}
      
      _ ->
        # 10% permanent failure
        {:error, "Invalid account number"}
    end
  end

  defp retry_based_on_type(transaction) do
    case transaction.transaction_type do
      @account_details -> make_account_details_request(transaction)
      @post_transaction -> make_payment_request(transaction)
      @get_invoice -> make_invoice_request(transaction)
      @confirm_invoice -> make_invoice_confirmation_request(transaction)
      @bundle_details -> make_bundle_details_request(transaction)
      @confirm_bundle -> make_bundle_confirmation_request(transaction)
      @validation -> make_validation_request(transaction)
      _ -> {:error, "Unknown transaction type"}
    end
  end

  # RequestPool helper functions

  defp get_biller_config(biller_type) do
    case Repo.get_by(BillerConfig, biller_type: biller_type, is_active: true) do
      nil -> {:error, "Biller configuration not found"}
      config -> {:ok, config}
    end
  end

  defp build_endpoint_url(config, endpoint_key, account_number) do
    case get_in(config.endpoints, [endpoint_key]) do
      nil -> {:error, "Endpoint not configured"}
      endpoint_template ->
        url = config.base_url <> String.replace(endpoint_template, "{account_number}", account_number)
        {:ok, url}
    end
  end

  defp build_headers(config) do
    headers = %{"Content-Type" => "application/json"}
    
    case config.authentication do
      %{"type" => "basic", "username" => username, "password" => password} ->
        auth_header = Base.encode64("#{username}:#{password}")
        {:ok, Map.put(headers, "Authorization", "Basic #{auth_header}")}
      
      %{"type" => "bearer", "token" => token} ->
        {:ok, Map.put(headers, "Authorization", "Bearer #{token}")}
      
      _ ->
        {:ok, headers}
    end
  end

  defp wait_for_request_completion(request, transaction, attempts \\ 0) do
    max_attempts = 30
    check_interval = 2000

    if attempts >= max_attempts do
      Logger.error("Request timeout after #{attempts} attempts for transaction #{transaction.our_transaction_id}")
      {:error, "Request timeout"}
    else
      Logger.info("Checking request status (attempt #{attempts + 1}/#{max_attempts}) for transaction #{transaction.our_transaction_id}")

      case RequestPool.get_request(request.id) do
        {:ok, req} ->
          case req.status do
            "completed" ->
              Logger.info("Request completed successfully for transaction #{transaction.our_transaction_id}")
              {:ok, req.response}

            "failed" ->
              error_message = req.response["error"] || "Request failed"
              Logger.error("Request failed for transaction #{transaction.our_transaction_id}: #{error_message}")
              {:error, error_message}

            _ ->
              Logger.info("Request still processing for transaction #{transaction.our_transaction_id}, waiting #{check_interval}ms")
              Process.sleep(check_interval)
              wait_for_request_completion(request, transaction, attempts + 1)
          end

        error ->
          Logger.error("Failed to get request status for transaction #{transaction.our_transaction_id}: #{inspect(error)}")
          error
      end
    end
  end
end