defmodule SMSMessage do
  # Define the struct with default values
  defstruct receiver: nil, message: "", completed: false

  alias __MODULE__

  def new_message(
        %__MODULE__{completed: completed, receiver: receiver, message: message} = sms_message,
        appendix
      ) do
    %SMSMessage{sms_message | message: "#{sms_message.message} #{appendix}"}
  end

  def new(%__MODULE__{receiver: receiver} = sms_message \\ %__MODULE__{}) do
    %__MODULE__{sms_message | receiver: receiver}
  end

  def send_message(
        %__MODULE__{completed: completed, receiver: receiver, message: message} = sms_message
      ) do
    case completed do
      true ->
        ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
          msisdn: receiver,
          message: message
        })

        :sent

      false ->
        :not_sent
    end
  end

  def complete_message(
        %__MODULE__{completed: completed, receiver: receiver, message: message} = sms_message
      ) do
    %__MODULE__{sms_message | completed: true}
  end
end
