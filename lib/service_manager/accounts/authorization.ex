defmodule ServiceManager.Accounts.Authorization do
  alias ServiceManager.Accounts.{User, Role, Permission}
  alias ServiceManager.Repo
  import Ecto.Query

  def has_role?(user, role_name) when is_binary(role_name) do
    user = user |> Repo.preload(:roles)
    Enum.any?(user.roles, &(&1.name == role_name))
  end

  def has_permission?(user, permission_name) when is_binary(permission_name) do
    user = user |> Repo.preload(roles: :permissions)

    Enum.any?(user.roles, fn role ->
      Enum.any?(role.permissions, &(&1.name == permission_name))
    end)
  end

  def assign_role(user, role_name) when is_binary(role_name) do
    role = Repo.get_by(Role, name: role_name)
    user = user |> Repo.preload(:roles)

    user
    |> Ecto.Changeset.change()
    |> Ecto.Changeset.put_assoc(:roles, [role | user.roles])
    |> Repo.update()
  end

  def remove_role(user, role_name) when is_binary(role_name) do
    user = user |> Repo.preload(:roles)
    updated_roles = Enum.reject(user.roles, &(&1.name == role_name))

    user
    |> Ecto.Changeset.change()
    |> Ecto.Changeset.put_assoc(:roles, updated_roles)
    |> Repo.update()
  end

  def list_user_permissions(user) do
    user = user |> Repo.preload(roles: :permissions)

    user.roles
    |> Enum.flat_map(& &1.permissions)
    |> Enum.uniq_by(& &1.name)
    |> Enum.map(& &1.name)
  end
end
