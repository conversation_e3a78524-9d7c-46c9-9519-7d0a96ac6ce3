defmodule ServiceManager.Accounts.OTP do
  alias ServiceManager.Accounts.User
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.Repo

  @otp_length 6
  @otp_expiry_minutes 5

  def generate_otp(user_id) do
    otp =
      :rand.uniform(:math.pow(10, @otp_length) |> round())
      |> Integer.to_string()
      |> String.pad_leading(@otp_length, "0")

    expires_at = DateTime.utc_now() |> DateTime.add(@otp_expiry_minutes * 60, :second)

    user = Repo.get(User, user_id)

    User.update_user(user, %{otp: otp})
    |> Repo.update()

    {:ok, otp, expires_at}
  end

  def validate_otp(user_id, otp) do
    user = Repo.get(User, user_id)

    case user do
      nil ->
        {:error, :user_not_found}

      user ->
        IO.inspect(user.otp)
        IO.inspect(otp)

        if user.otp == otp do
          User.update_user(user, %{otp: nil})
          |> Repo.update()

          {:ok, :valid}
        else
          {:error, :invalid_otp}
        end
    end
  end

  def generate_wallet_otp(user_id) do
    otp =
      :rand.uniform(:math.pow(10, @otp_length) |> round())
      |> Integer.to_string()
      |> String.pad_leading(@otp_length, "0")

    expires_at = DateTime.utc_now() |> DateTime.add(@otp_expiry_minutes * 60, :second)

    user = Repo.get(WalletUser, user_id)

    WalletUser.update_wallet(user, %{otp: otp})
    |> Repo.update()

    {:ok, otp, expires_at}
  end

  def validate_wallet_otp(user_id, otp) do
    user = Repo.get(WalletUser, user_id)

    case user do
      nil ->
        {:error, :user_not_found}

      user ->
        IO.inspect(user.otp)
        IO.inspect(otp)

        if user.otp == otp do
          WalletUser.update_wallet(user, %{otp: nil})
          |> Repo.update()

          {:ok, :valid}
        else
          {:error, :invalid_otp}
        end
    end
  end
end
