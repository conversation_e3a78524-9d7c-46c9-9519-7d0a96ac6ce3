defmodule ServiceManager.Accounts.User do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :sync_status,
             :last_sync_at,
             :sync_error,
             :username,
             :customer_no,
             :updated_at,
             :created_at,
             :name,
             :nickname,
             :email,
             :account_number,
             :confirmed_at,
             :first_name,
             :last_name,
             :phone_number,
             :date_of_birth,
             :address,
             :approved,
             :city,
             :state,
             :zip,
             :country,
             :account_balance,
             :working_balance,
             :clearing_balance,
             :first_time_login,
             :email_notifications,
             :sms_notifications,
             :push_notifications,
             :disabled,
             :session_id,
             :allow_cheque_request_upload,
             :allow_multi_session,
             :disabled_reason,
             :approval_reason,
             :deregistration_reason,
             :primary_device_id
           ]}
  schema "accounts_users" do
    field :username, :string, default: ""
    field :disabled_reason, :string
    field :approval_reason, :string
    field :deregistration_reason, :string
    field :email, :string, default: ""
    field :name, :string, virtual: true, default: ""
    field :customer_no, :string, default: ""
    field :nickname, :string, default: ""
    field :password, :string, virtual: true, redact: true, default: ""
    field :hashed_password, :string, redact: true, default: ""
    field :current_password, :string, virtual: true, redact: true, default: ""
    field :confirmed_at, :utc_datetime, default: nil
    field :first_name, :string, default: ""
    field :last_name, :string, default: ""
    field :phone_number, :string, default: ""
    field :date_of_birth, :date, default: nil
    field :address, :string, default: ""
    field :city, :string, default: ""
    field :state, :string, default: ""
    field :zip, :string, default: ""
    field :country, :string, default: ""
    field :approved, :boolean, default: false
    field :account_balance, :float, default: 0.0
    field :working_balance, :float, default: 0.0
    field :clearing_balance, :float, default: 0.0
    field :account_number, :string, default: ""
    field :first_time_login, :boolean, default: true
    field :created_at, :utc_datetime, virtual: true, default: nil
    field :currency, :string, virtual: true, default: "MWK"
    field :account_type, :string, virtual: true, default: "CURRENT"
    field :notifications, :string, virtual: true, default: ""
    field :email_notifications, :boolean, default: true
    field :sms_notifications, :boolean, default: true
    field :push_notifications, :boolean, default: true
    field :disabled, :boolean, default: false
    field :memorable_word, :string, default: ""
    field :otp, :string, default: ""
    field :deletion_status, :boolean, default: false
    field :session_id, Ecto.UUID
    field :allow_cheque_request_upload, :boolean, default: false
    field :allow_multi_session, :boolean, default: true
    field :profile_name, :string, default: ""
    field :company, :string, default: ""
    field :inputter, :string, default: ""
    field :authoriser, :string, default: ""
    field :customer_number, :string, default: ""
    field :account_officer, :string, default: ""
    field :profile_type, :string, default: ""

    # Core Banking Integration Fields
    field :sync_status, :string, default: "pending"
    field :last_sync_at, :utc_datetime
    field :sync_error, :string

    field :primary_device_id, :string

    has_many :accounts, ServiceManager.Accounts.FundAccounts
    has_many :cheque_book_requests, ServiceManager.Schemas.Accounts.Cheques.ChequeBookRequest
    many_to_many :roles, ServiceManager.Accounts.Role, join_through: "users_roles"

    belongs_to :user_permissions, ServiceManager.Schemas.RolesAndPermission,
      foreign_key: :roles_and_permission_id,
      type: :id

    timestamps(type: :utc_datetime)
  end

  @doc """
  A user changeset for registration.

  It is important to validate the length of both email and password.
  Otherwise, databases may truncate the email without warnings, which
  could lead to unpredictable or insecure behaviour. Long passwords may
  also be very expensive to hash for certain algorithms.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.

    * `:validate_email` - Validates the uniqueness of the email, in case
      you don't want to validate the uniqueness of the email (like when
      using this changeset for validations on a LiveView form before
      submitting the form), this option can be set to `false`.
      Defaults to `true`.
  """

  def changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :username,
      :nickname,
      :deletion_status,
      :email,
      :currency,
      :account_type,
      :roles_and_permission_id,
      :notifications,
      :password,
      :first_name,
      :last_name,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_number,
      :account_balance,
      :working_balance,
      :clearing_balance,
      :email_notifications,
      :sms_notifications,
      :push_notifications,
      :otp,
      :customer_no,
      :approved,
      :first_time_login,
      :memorable_word,
      :hashed_password,
      :sync_status,
      :last_sync_at,
      :sync_error,
      :disabled,
      :session_id,
      :allow_cheque_request_upload,
      :allow_multi_session,
      :primary_device_id
    ])
    |> validate_required([])
    #    |> validate_email(validate_email: false)
    |> validate_password(opts)
    |> validate_phone_number()
    # |> validate_memorable_word()
    |> maybe_generate_customer_no()
  end

  def update_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :customer_no,
      :disabled,
      :deletion_status,
      :disabled_reason,
      :approval_reason,
      :deregistration_reason,
      :nickname,
      :email,
      :password,
      :first_name,
      :roles_and_permission_id,
      :last_name,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_number,
      :account_balance,
      :working_balance,
      :clearing_balance,
      :email_notifications,
      :sms_notifications,
      :push_notifications,
      :otp,
      :approved,
      :first_time_login,
      :memorable_word,
      :session_id,
      :allow_cheque_request_upload,
      :allow_multi_session,
      :sync_status,
      :last_sync_at
    ])
    |> validate_required([])

    # |> validate_email(opts)
    # |> validate_phone_number()
  end

  def update_changeset_ui(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :nickname,
      :deletion_status,
      :email,
      :password,
      :first_name,
      :roles_and_permission_id,
      :last_name,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_number,
      :account_balance,
      :email_notifications,
      :sms_notifications,
      :push_notifications,
      :otp,
      :approved,
      :first_time_login,
      :memorable_word,
      :session_id,
      :allow_cheque_request_upload,
      :allow_multi_session
    ])
    #    |> validate_required([:first_name, :last_name])
    #    |> validate_email(opts)
    |> validate_phone_number()
  end

  def deletion_changeset(user, attrs) do
    user
    |> cast(attrs, [
      :nickname,
      :deletion_status,
      :email,
      :password,
      :first_name,
      :roles_and_permission_id,
      :last_name,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_number,
      :account_balance,
      :email_notifications,
      :sms_notifications,
      :push_notifications,
      :otp,
      :approved,
      :first_time_login,
      :memorable_word,
      :session_id,
      :allow_cheque_request_upload,
      :allow_multi_session
    ])
    |> put_change(:deletion_status, true)
  end

  def activate_update_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :deletion_status,
      :password
    ])
    |> validate_password(opts)
  end

  def registration_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :username,
      :nickname,
      :email,
      :password,
      :first_name,
      :last_name,
      :roles_and_permission_id,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_number,
      :account_balance,
      :profile_name,
      :company,
      :inputter, 
      :authoriser,
      :customer_number,
      :account_officer,
      :profile_type
    ])
    # |> validate_email(opts)
    |> validate_password(opts)
  end

  def device_binding_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :primary_device_id
    ])
  end

  def update_password_changeset(user, attrs) do
    user
    |> cast(attrs, [:password, :first_time_login])
    |> validate_required([:password])
    |> validate_length(:password, min: 6, max: 32)
    |> maybe_hash_password(hash_password: true)
  end

  def update_user(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [
      :nickname,
      :account_number,
      :first_name,
      :deletion_status,
      :last_name,
      :phone_number,
      :date_of_birth,
      :address,
      :city,
      :state,
      :zip,
      :country,
      :account_balance,
      :approved,
      :first_time_login,
      :memorable_word,
      :otp,
      :disabled,
      :session_id,
      :allow_cheque_request_upload,
      :allow_multi_session
    ])
  end

  defp maybe_generate_customer_no(changeset) do
    case get_field(changeset, :customer_no) do
      nil -> put_change(changeset, :customer_no, generate_customer_no())
      _ -> changeset
    end
  end

  defp generate_customer_no do
    "CUS" <> (Enum.random(100_000..999_999) |> Integer.to_string())
  end

  defp validate_email(changeset, opts) do
    changeset
    |> validate_required([:email])
    |> validate_format(:email, ~r/^[^\s]+@[^\s]+$/, message: "must have the @ sign and no spaces")
    |> validate_length(:email, max: 160)
    |> maybe_validate_unique_email(opts)
  end

  defp validate_password(changeset, opts) do
    changeset
    |> validate_required([:password])
    |> validate_length(:password, min: 6, max: 72)
    # Examples of additional password validation:
    # |> validate_format(:password, ~r/[a-z]/, message: "at least one lower case character")
    # |> validate_format(:password, ~r/[A-Z]/, message: "at least one upper case character")
    # |> validate_format(:password, ~r/[!?@#$%^&*_0-9]/, message: "at least one digit or punctuation character")
    |> maybe_hash_password(opts)
  end

  defp validate_phone_number(changeset) do
    changeset
    |> validate_required([:phone_number])
    |> validate_length(:phone_number, max: 25)
    |> unsafe_validate_unique(:phone_number, ServiceManager.Repo)

    #    |> unique_constraint(:phone_number)
  end

  defp maybe_hash_password(changeset, opts) do
    hash_password? = Keyword.get(opts, :hash_password, true)
    password = get_change(changeset, :password)

    if hash_password? && password && changeset.valid? do
      changeset
      # If using Bcrypt, then further validate it is at most 72 bytes long
      |> validate_length(:password, max: 72, count: :bytes)
      # Hashing could be done with `Ecto.Changeset.prepare_changes/2`, but that
      # would keep the database transaction open longer and hurt performance.
      |> put_change(:hashed_password, Bcrypt.hash_pwd_salt(password))
      # |> put_change(:hashed_password, Pbkdf2.hash_pwd_salt(password))
      |> delete_change(:password)
    else
      changeset
    end
  end

  defp maybe_validate_unique_email(changeset, opts) do
    if Keyword.get(opts, :validate_email, true) do
      changeset
      |> unsafe_validate_unique(:email, ServiceManager.Repo)
      |> unique_constraint(:email)
    else
      changeset
    end
  end

  defp validate_memorable_word(changeset) do
    changeset
    |> validate_required([:memorable_word])
    |> validate_length(:memorable_word, min: 6, max: 50)
  end

  def notification_settings_changeset(user, attrs) do
    user
    |> cast(attrs, [:email_notifications, :sms_notifications, :push_notifications])
    |> validate_required([:email_notifications, :sms_notifications, :push_notifications])
  end

  @doc """
  A user changeset for changing the email.

  It requires the email to change otherwise an error is added.
  """
  def email_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:email])
    |> validate_email(opts)
    |> case do
      %{changes: %{email: _}} = changeset -> changeset
      %{} = changeset -> add_error(changeset, :email, "did not change")
    end
  end

  @doc """
  A user changeset for changing the password.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.
  """
  def password_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:password])
    |> validate_confirmation(:password, message: "does not match password")
    |> validate_password(opts)
  end

  @doc """
  Confirms the account by setting `confirmed_at`.
  """
  def confirm_changeset(user) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)
    change(user, confirmed_at: now)
  end

  @doc """
  Verifies the password.

  If there is no user or the user doesn't have a password, we call
  `Bcrypt.no_user_verify/0` to avoid timing attacks.
  """
  def valid_password?(%ServiceManager.Accounts.User{hashed_password: hashed_password}, password)
      when is_binary(hashed_password) and byte_size(password) > 0 do
    # Pbkdf2.verify_pass(password, hashed_password)
    Bcrypt.verify_pass(password, hashed_password)
  end

  def valid_password?(_, _) do
    Bcrypt.no_user_verify()
    # Pbkdf2.no_user_verify()
    false
  end

  @doc """
  Validates the current password otherwise adds an error to the changeset.
  """
  def validate_current_password(changeset, password) do
    changeset = cast(changeset, %{current_password: password}, [:current_password])

    if valid_password?(changeset.data, password) do
      changeset
    else
      add_error(changeset, :current_password, "is not valid")
    end
  end
end
