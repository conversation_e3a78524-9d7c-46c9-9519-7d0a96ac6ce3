defmodule ServiceManager.Cache.RouteCache do
  use GenServer
  require <PERSON><PERSON>
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Route

  @table_name :routes_cache
  @refresh_interval :timer.minutes(5)

  # Client API

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, [], name: __MODULE__)
  end

  def get_route(name) do
    case :ets.lookup(@table_name, name) do
      [{^name, route}] -> {:ok, route}
      [] -> {:error, :not_found}
    end
  end

  def get_route!(name) do
    case get_route(name) do
      {:ok, route} -> route
      {:error, :not_found} -> raise "Route not found: #{name}"
    end
  end

  def get_full_url(name) do
    case get_route(name) do
      {:ok, %{host: host, path: path}} -> {:ok, "#{host}#{path}"}
      error -> error
    end
  end

  def get_full_url!(name) do
    case get_full_url(name) do
      {:ok, url} -> url
      {:error, :not_found} -> raise "Route not found: #{name}"
    end
  end

  def get_route_info(name) do
    case get_route(name) do
      {:ok, %{host: host, path: path, method: method}} ->
        {:ok, %{url: "#{host}#{path}", method: method}}

      error ->
        error
    end
  end

  def get_route_info!(name) do
    case get_route_info(name) do
      {:ok, info} -> info
      {:error, :not_found} -> raise "Route not found: #{name}"
    end
  end

  def refresh do
    GenServer.cast(__MODULE__, :refresh)
  end

  # Server Callbacks

  @impl true
  def init(_) do
    Logger.info("Starting Route Cache")
    table = :ets.new(@table_name, [:set, :protected, :named_table])
    schedule_refresh()
    {:ok, %{table: table}, {:continue, :initial_load}}
  end

  @impl true
  def handle_continue(:initial_load, state) do
    load_routes()
    {:noreply, state}
  end

  @impl true
  def handle_cast(:refresh, state) do
    load_routes()
    {:noreply, state}
  end

  @impl true
  def handle_info(:refresh, state) do
    load_routes()
    schedule_refresh()
    {:noreply, state}
  end

  # Private Functions

  defp schedule_refresh do
    Process.send_after(self(), :refresh, @refresh_interval)
  end

  defp load_routes do
    Logger.info("Loading routes into cache")
    routes = Repo.all(Route)

    :ets.delete_all_objects(@table_name)

    for route <- routes do
      :ets.insert(
        @table_name,
        {route.name,
         %{
           host: route.host,
           path: route.path,
           method: route.method
         }}
      )
    end

    Logger.info("Loaded #{length(routes)} routes into cache")
  end
end
