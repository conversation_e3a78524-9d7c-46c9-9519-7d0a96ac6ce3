defmodule ServiceManager.Cache.RouteHelper do
  @moduledoc """
  Helper module to demonstrate usage of the RouteCache.
  Provides convenient functions for working with cached routes.
  """

  alias ServiceManager.Cache.RouteCache

  @doc """
  Gets the full URL and method for a route by name.
  Raises if the route is not found.

  ## Examples

      iex> get_route_details("get_account_balance")
      %{url: "**************/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************", method: "GET"}

      iex> get_route_details("create_account")
      %{url: "fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************", method: "POST"}
  """
  def get_route_details(name) do
    RouteCache.get_route_info!(name)
  end

  @doc """
  Gets just the full URL for a route by name.
  Raises if the route is not found.

  ## Examples

      iex> get_url("get_account_balance")
      "**************/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************"
  """
  def get_url(name) do
    RouteCache.get_full_url!(name)
  end

  @doc """
  Gets the HTTP method for a route by name.
  Raises if the route is not found.

  ## Examples

      iex> get_method("create_account")
      "POST"
  """
  def get_method(name) do
    route = RouteCache.get_route!(name)
    route.method
  end

  @doc """
  Builds a complete URL with protocol.
  Defaults to https:// unless the host is an IP address.

  ## Examples

      iex> build_url("get_account_balance")
      "http://**************/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************"

      iex> build_url("create_account")
      "https://fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************"
  """
  def build_url(name) do
    route = RouteCache.get_route!(name)

    protocol =
      if String.match?(route.host, ~r/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/),
        do: "http://",
        else: "https://"

    protocol <> route.host <> route.path
  end

  @doc """
  Safe version of get_route_details that returns a tuple.

  ## Examples

      iex> get_route_details_safe("get_account_balance")
      {:ok, %{url: "**************/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************", method: "GET"}}

      iex> get_route_details_safe("non_existent")
      {:error, :not_found}
  """
  def get_route_details_safe(name) do
    RouteCache.get_route_info(name)
  end

  @doc """
  Safe version of get_url that returns a tuple.

  ## Examples

      iex> get_url_safe("get_account_balance")
      {:ok, "**************/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************"}

      iex> get_url_safe("non_existent")
      {:error, :not_found}
  """
  def get_url_safe(name) do
    RouteCache.get_full_url(name)
  end

  @doc """
  Safe version of get_method that returns a tuple.

  ## Examples

      iex> get_method_safe("create_account")
      {:ok, "POST"}

      iex> get_method_safe("non_existent")
      {:error, :not_found}
  """
  def get_method_safe(name) do
    case RouteCache.get_route(name) do
      {:ok, route} -> {:ok, route.method}
      error -> error
    end
  end
end
