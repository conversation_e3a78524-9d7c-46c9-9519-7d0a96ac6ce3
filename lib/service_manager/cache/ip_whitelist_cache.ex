defmodule ServiceManager.Cache.IpWhitelistCache do
  use GenServer
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.IpWhitelist

  @table_name :ip_whitelist_cache

  def start_link(_) do
    GenServer.start_link(__MODULE__, [], name: __MODULE__)
  end

  def init(_) do
    :ets.new(@table_name, [:set, :named_table, :public])
    load_ips()
    {:ok, %{}}
  end

  def get_ip(ip_address) do
    case :ets.lookup(@table_name, ip_address) do
      [{^ip_address, entry}] -> {:ok, entry}
      [] -> {:error, :not_found}
    end
  end

  @spec register_ip(any()) :: {:error, any()} | {:ok, any()}
  def register_ip(ip_address) do
    # Create new IP entry with default values
    attrs = %{
      ip_address: ip_address,
      status: :active,
      risk_level: :low,
      environment: :production,
      access_count: 0,
      description: "Auto-registered IP",
      last_accessed_at: DateTime.utc_now() |> DateTime.truncate(:second)
    }

    case Repo.insert(IpWhitelist.changeset(%IpWhitelist{}, attrs)) do
      {:ok, entry} ->
        # Update cache with new entry
        :ets.insert(@table_name, {ip_address, entry})
        {:ok, entry}

      {:error, _changeset} = error ->
        error
    end
  end

  def reload_cache do
    load_ips()
  end

  defp load_ips do
    # Clear existing entries
    :ets.delete_all_objects(@table_name)

    # Load all IPs from database
    IpWhitelist
    |> Repo.all()
    |> Enum.each(fn entry ->
      :ets.insert(@table_name, {entry.ip_address, entry})
    end)
  end
end
