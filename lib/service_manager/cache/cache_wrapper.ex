defmodule ServiceManager.Cache.CacheWrapper do
  use GenServer
  require Logger

  def start_link(opts) do
    name = opts[:name]
    Logger.info("Starting cache wrapper for: #{name}")
    GenServer.start_link(__MODULE__, opts)
  end

  def init(name: name) do
    Logger.info("Initializing cache: #{name}")

    case Supervisor.start_child(
           ServiceManager.DynamicSupervisor,
           Supervisor.child_spec({Cachex, name: name}, id: name)
         ) do
      {:ok, pid} ->
        Logger.info("Successfully started cache: #{name} with PID: #{inspect(pid)}")
        {:ok, %{name: name, pid: pid}}

      error ->
        Logger.error("Failed to start cache #{name}: #{inspect(error)}")
        error
    end
  end

  def child_spec(opts) do
    name = opts[:name]

    %{
      id: name,
      start: {__MODULE__, :start_link, [opts]},
      type: :worker,
      restart: :permanent,
      shutdown: 5000
    }
  end

  def terminate(_reason, %{name: name, pid: pid}) do
    Logger.info("Terminating cache: #{name} with PID: #{inspect(pid)}")
    :ok
  end
end

# String representation for the module itself
defimpl String.Chars, for: Tuple do
  def to_string({ServiceManager.Cache.CacheWrapper, opts}) when is_list(opts) do
    name = opts[:name]
    "Cachex(#{name})"
  end

  def to_string(tuple), do: inspect(tuple)
end

# String representation for the state
defimpl String.Chars, for: ServiceManager.Cache.CacheWrapper do
  def to_string(%{name: name}), do: "Cachex(#{name})"
end
