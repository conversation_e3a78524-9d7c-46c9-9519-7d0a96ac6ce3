defmodule ServiceManager.Cache.ConfigCache do
  @moduledoc """
  GenServer responsible for caching configuration settings using ETS.
  """
  use GenServer
  require Logger
  alias ServiceManager.Schemas.Settings.Config
  alias ServiceManager.Repo
  import Ecto.Query

  @table_name :config_cache

  # Client API

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, [], name: __MODULE__)
  end

  @doc """
  Retrieves a configuration value by key.
  Returns {:ok, value} if found, {:error, :not_found} otherwise.
  """
  def get(key) when is_binary(key) do
    case :ets.lookup(@table_name, key) do
      [{^key, value}] -> {:ok, value}
      [] -> {:error, :not_found}
    end
  end

  @doc """
  Sets a configuration value.
  Returns {:ok, value} on success, {:error, reason} on failure.
  """
  def set(key, value) when is_binary(key) do
    GenServer.call(__MODULE__, {:set, key, value})
  end

  @doc """
  Reloads all configurations from the database.
  """
  def reload do
    GenServer.call(__MODULE__, :reload)
  end

  @doc """
  Reloads a specific configuration by key from the database.
  Returns {:ok, value} if config was found and reloaded, {:error, :not_found} otherwise.
  """
  def reload_key(key) when is_binary(key) do
    GenServer.call(__MODULE__, {:reload_key, key})
  end

  @doc """
  Gets all cached configurations.
  """
  def get_all do
    :ets.tab2list(@table_name)
  end

  # Server Callbacks

  @impl true
  def init(_) do
    Logger.info("Starting ConfigCache...")
    table = :ets.new(@table_name, [:set, :protected, :named_table])
    Logger.info("Created ETS table: #{@table_name}")
    {:ok, table, {:continue, :load_initial_data}}
  end

  @impl true
  def handle_continue(:load_initial_data, table) do
    Logger.info("Loading initial configuration data...")
    count = load_configs()
    Logger.info("Loaded #{count} configurations into cache")
    schedule_reload()
    {:noreply, table}
  end

  @impl true
  def handle_info(:reload_config, table) do
    Logger.info("Auto-reloading configurations after delay...")
    count = load_configs()
    Logger.info("Reloaded #{count} configurations")
    schedule_reload()
    {:noreply, table}
  end

  defp schedule_reload do
    # 10 seconds
    Process.send_after(self(), :reload_config, 10_000)
  end

  @impl true
  def handle_call({:set, key, value}, _from, table) do
    Logger.debug("Setting config: #{key}")
    # Update database first
    result =
      case Repo.get_by(Config, key: key) do
        nil ->
          Logger.debug("Creating new config: #{key}")

          %Config{}
          |> Config.changeset(%{key: key, value: value})
          |> Repo.insert()

        config ->
          Logger.debug("Updating existing config: #{key}")

          config
          |> Config.changeset(%{value: value})
          |> Repo.update()
      end

    case result do
      {:ok, config} ->
        true = :ets.insert(table, {config.key, config.value})
        Logger.info("Successfully set config: #{key}")
        {:reply, {:ok, config.value}, table}

      {:error, changeset} ->
        Logger.error("Failed to set config: #{key}. Errors: #{inspect(changeset.errors)}")
        {:reply, {:error, changeset}, table}
    end
  end

  @impl true
  def handle_call(:reload, _from, table) do
    Logger.info("Reloading all configurations...")
    count = load_configs()
    Logger.info("Reloaded #{count} configurations")
    {:reply, :ok, table}
  end

  @impl true
  def handle_call({:reload_key, key}, _from, table) do
    Logger.debug("Reloading config: #{key}")

    case load_config(key) do
      {:ok, value} ->
        Logger.info("Successfully reloaded config: #{key}")
        {:reply, {:ok, value}, table}

      {:error, :not_found} ->
        Logger.warn("Config not found for reload: #{key}")
        {:reply, {:error, :not_found}, table}
    end
  end

  # Private Functions

  defp load_configs do
    configs =
      Config
      |> where([c], c.status == "active")
      |> Repo.all()

    Enum.each(configs, fn config ->
      :ets.insert(@table_name, {config.key, config.value})
    end)

    length(configs)
  end

  defp load_config(key) do
    case Config
         |> where([c], c.status == "active" and c.key == ^key)
         |> Repo.one() do
      nil ->
        Logger.debug("Config not found in database: #{key}")
        {:error, :not_found}

      config ->
        Logger.debug("Loading config from database: #{key}")
        :ets.insert(@table_name, {config.key, config.value})
        {:ok, config.value}
    end
  end
end
