defmodule ServiceManager.Versions do
  @moduledoc """
  The Versions context handles all business logic related to version control,
  including version entries, changelog items, issues, Q&A, and their associated
  messages.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManager.Versions.{Version, ChangelogItem, Issue, QAndA, Message, Label, IssueLabel}

  # Version-related functions

  @doc """
  Returns the list of versions.

  ## Examples

      iex> list_versions()
      [%Version{}, ...]

  """
  def list_versions do
    Version
    |> order_by([v], desc: v.release_date)
    |> Repo.all()
    |> Repo.preload([:changelog_items])
  end

  @doc """
  Gets the latest version based on release date.

  ## Examples

      iex> get_latest_version()
      %Version{}

  """
  def get_latest_version do
    Version
    |> order_by([v], desc: v.release_date)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Gets a single version.

  Raises `Ecto.NoResultsError` if the Version does not exist.

  ## Examples

      iex> get_version!(123)
      %Version{}

      iex> get_version!(456)
      ** (Ecto.NoResultsError)

  """
  def get_version!(id) do
    Version
    |> Repo.get!(id)
    |> Repo.preload(
      changelog_items: [messages: [user: [], replies: [user: []]]],
      issues: [creator: [], assignee: [], labels: [], messages: [user: [], replies: [user: []]]],
      q_and_as: [creator: [], messages: [user: [], replies: [user: []]]]
    )
  end

  @doc """
  Creates a version.

  ## Examples

      iex> create_version(%{field: value})
      {:ok, %Version{}}

      iex> create_version(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_version(attrs \\ %{}) do
    %Version{}
    |> Version.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a version.

  ## Examples

      iex> update_version(version, %{field: new_value})
      {:ok, %Version{}}

      iex> update_version(version, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_version(%Version{} = version, attrs) do
    version
    |> Version.changeset(attrs)
    |> Repo.update()
  end

  # Changelog-related functions

  @doc """
  Creates a changelog item for a version.

  ## Examples

      iex> create_changelog_item(%{field: value})
      {:ok, %ChangelogItem{}}

  """
  def create_changelog_item(attrs \\ %{}) do
    %ChangelogItem{}
    |> ChangelogItem.changeset(attrs)
    |> Repo.insert()
  end

  # Issue-related functions

  @doc """
  Returns the list of issues for a version.

  ## Examples

      iex> list_version_issues(version_id)
      [%Issue{}, ...]

  """
  def list_version_issues(version_id) do
    Issue
    |> where([i], i.version_id == ^version_id)
    |> order_by([i], desc: i.inserted_at)
    |> Repo.all()
    |> Repo.preload([:assignee, :creator, :labels])
  end

  @doc """
  Gets a single issue.

  Raises `Ecto.NoResultsError` if the Issue does not exist.

  ## Examples

      iex> get_issue!(123)
      %Issue{}

  """
  def get_issue!(id) do
    Issue
    |> Repo.get!(id)
    |> Repo.preload([:assignee, :creator, :labels, messages: [:user, :replies]])
  end

  @doc """
  Creates an issue.

  ## Examples

      iex> create_issue(%{field: value})
      {:ok, %Issue{}}

  """
  def create_issue(attrs \\ %{}) do
    %Issue{}
    |> Issue.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates an issue.

  ## Examples

      iex> update_issue(issue, %{field: new_value})
      {:ok, %Issue{}}

  """
  def update_issue(%Issue{} = issue, attrs) do
    issue
    |> Issue.changeset(attrs)
    |> Repo.update()
  end

  # Q&A-related functions

  @doc """
  Returns the list of Q&As for a version.

  ## Examples

      iex> list_version_qas(version_id)
      [%QAndA{}, ...]

  """
  def list_version_qas(version_id) do
    QAndA
    |> where([q], q.version_id == ^version_id)
    |> order_by([q], desc: q.votes_count, desc: q.inserted_at)
    |> Repo.all()
    |> Repo.preload([:creator])
  end

  @doc """
  Gets a single Q&A entry.

  Raises `Ecto.NoResultsError` if the Q&A does not exist.

  ## Examples

      iex> get_qa!(123)
      %QAndA{}

  """
  def get_qa!(id) do
    QAndA
    |> Repo.get!(id)
    |> Repo.preload([:creator, messages: [:user, :replies]])
  end

  @doc """
  Creates a Q&A entry.

  ## Examples

      iex> create_qa(%{field: value})
      {:ok, %QAndA{}}

  """
  def create_qa(attrs \\ %{}) do
    %QAndA{}
    |> QAndA.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a Q&A entry.

  ## Examples

      iex> update_qa(qa, %{field: new_value})
      {:ok, %QAndA{}}

  """
  def update_qa(%QAndA{} = qa, attrs) do
    qa
    |> QAndA.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Upvotes a Q&A entry.

  ## Examples

      iex> upvote_qa(qa)
      {:ok, %QAndA{}}

  """
  def upvote_qa(%QAndA{} = qa) do
    qa
    |> QAndA.changeset(%{votes_count: qa.votes_count + 1})
    |> Repo.update()
  end

  # Message-related functions

  @doc """
  Creates a message.

  ## Examples

      iex> create_message(%{field: value})
      {:ok, %Message{}}

  """
  def create_message(attrs \\ %{}) do
    type =
      attrs["type"] ||
        case attrs["messageable_type"] do
          "ChangelogItem" -> "changelog"
          "Issue" -> "issue"
          "QAndA" -> "qa"
          _ -> raise "Invalid messageable_type: #{inspect(attrs["messageable_type"])}"
        end

    attrs =
      Map.merge(attrs, %{
        "type" => type,
        "messageable_id" => to_string(attrs["messageable_id"]) |> String.to_integer()
      })

    %Message{}
    |> Message.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Lists all messages for a changelog item.

  ## Examples

      iex> list_changelog_messages(123)
      [%Message{}, ...]

  """
  def list_changelog_messages(item_id) do
    Message
    |> where([m], m.messageable_id == ^item_id and m.type == "changelog")
    |> where([m], is_nil(m.parent_id))
    |> order_by([m], asc: m.inserted_at)
    |> Repo.all()
    |> Repo.preload([:user, replies: [:user, replies: [:user]]])
  end

  @doc """
  Lists all messages for an issue.

  ## Examples

      iex> list_issue_messages(123)
      [%Message{}, ...]

  """
  def list_issue_messages(issue_id) do
    Message
    |> where([m], m.messageable_id == ^issue_id and m.type == "issue")
    |> where([m], is_nil(m.parent_id))
    |> order_by([m], asc: m.inserted_at)
    |> Repo.all()
    |> Repo.preload([:user, replies: [:user, replies: [:user]]])
  end

  @doc """
  Lists all messages for a Q&A entry.

  ## Examples

      iex> list_qa_messages(123)
      [%Message{}, ...]

  """
  def list_qa_messages(qa_id) do
    Message
    |> where([m], m.messageable_id == ^qa_id and m.type == "qa")
    |> where([m], is_nil(m.parent_id))
    |> order_by([m], asc: m.inserted_at)
    |> Repo.all()
    |> Repo.preload([:user, replies: [:user, replies: [:user]]])
  end

  # Label-related functions

  @doc """
  Returns the list of labels.

  ## Examples

      iex> list_labels()
      [%Label{}, ...]

  """
  def list_labels do
    Repo.all(Label)
  end

  @doc """
  Creates a label.

  ## Examples

      iex> create_label(%{field: value})
      {:ok, %Label{}}

  """
  def create_label(attrs \\ %{}) do
    %Label{}
    |> Label.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Adds a label to an issue.

  ## Examples

      iex> add_label_to_issue(issue, label)
      {:ok, %IssueLabel{}}

  """
  def add_label_to_issue(%Issue{} = issue, %Label{} = label) do
    %IssueLabel{}
    |> IssueLabel.changeset(%{issue_id: issue.id, label_id: label.id})
    |> Repo.insert()
  end

  @doc """
  Removes a label from an issue.

  ## Examples

      iex> remove_label_from_issue(issue, label)
      {:ok, %IssueLabel{}}

  """
  def remove_label_from_issue(%Issue{} = issue, %Label{} = label) do
    IssueLabel
    |> where([il], il.issue_id == ^issue.id and il.label_id == ^label.id)
    |> Repo.delete_all()
  end
end
