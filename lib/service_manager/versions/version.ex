defmodule ServiceManager.Versions.Version do
  use Ecto.Schema
  import Ecto.Changeset

  @version_types ~w(major minor patch)
  @status_types ~w(draft published)

  schema "versions" do
    field :status, :string
    field :description, :string
    field :title, :string
    field :version_number, :string
    field :release_date, :utc_datetime
    field :version_type, :string

    has_many :changelog_items, ServiceManager.Versions.ChangelogItem
    has_many :issues, ServiceManager.Versions.Issue
    has_many :q_and_as, ServiceManager.Versions.QAndA

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for a version.

  ## Parameters
    - version: The version struct to create a changeset for
    - attrs: The attributes to create the changeset with

  ## Examples
      iex> changeset(%Version{}, %{title: "v1.0.0", version_type: "major"})
  """
  def changeset(version, attrs) do
    version
    |> cast(attrs, [:version_number, :release_date, :title, :description, :version_type, :status])
    |> validate_required([
      :version_number,
      :release_date,
      :title,
      :description,
      :version_type,
      :status
    ])
    |> validate_inclusion(:version_type, @version_types,
      message: "must be one of: #{Enum.join(@version_types, ", ")}"
    )
    |> validate_inclusion(:status, @status_types,
      message: "must be one of: #{Enum.join(@status_types, ", ")}"
    )
    |> validate_format(:version_number, ~r/^\d+\.\d+\.\d+$/, message: "must be in format x.y.z")
    |> unique_constraint(:version_number)
  end
end
