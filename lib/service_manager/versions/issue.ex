defmodule ServiceManager.Versions.Issue do
  use Ecto.Schema
  import Ecto.Changeset

  alias ServiceManager.Versions.{Version, Message, Label, IssueLabel}
  alias ServiceManager.Schemas.AdminUsers

  @status_types ~w(wip open closed)
  @priority_types ~w(low medium high critical)

  @derive {Jason.Encoder,
           only: [:id, :title, :description, :status, :priority, :messages, :creator, :assignee]}

  schema "issues" do
    field :priority, :string
    field :status, :string
    field :description, :string
    field :title, :string

    belongs_to :version, Version
    belongs_to :assignee, AdminUsers, foreign_key: :assignee_id
    belongs_to :creator, AdminUsers, foreign_key: :created_by

    has_many :messages, Message,
      foreign_key: :messageable_id,
      where: [messageable_type: "Issue"],
      preload_order: [asc: :inserted_at]

    many_to_many :labels, Label, join_through: IssueLabel

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for an issue.

  ## Parameters
    - issue: The issue struct to create a changeset for
    - attrs: The attributes to create the changeset with

  ## Examples
      iex> changeset(%Issue{}, %{title: "Bug Report", status: "open"})
  """
  def changeset(issue, attrs) do
    issue
    |> cast(attrs, [
      :title,
      :description,
      :status,
      :priority,
      :version_id,
      :assignee_id,
      :created_by
    ])
    |> validate_required([:title, :description, :status, :priority, :version_id, :created_by])
    |> validate_inclusion(:status, @status_types,
      message: "must be one of: #{Enum.join(@status_types, ", ")}"
    )
    |> validate_inclusion(:priority, @priority_types,
      message: "must be one of: #{Enum.join(@priority_types, ", ")}"
    )
    |> validate_length(:title, min: 5, max: 100)
    |> validate_length(:description, min: 10)
    |> foreign_key_constraint(:version_id)
    |> foreign_key_constraint(:assignee_id)
    |> foreign_key_constraint(:created_by)
  end
end
