defmodule ServiceManager.Versions.ChangelogItem do
  use Ecto.Schema
  import Ecto.Changeset

  alias ServiceManager.Versions.{Version, Message}

  @item_types ~w(feature enhancement bugfix security)

  @derive {Jason.Encoder, only: [:id, :title, :description, :item_type, :messages]}

  schema "changelog_items" do
    field :description, :string
    field :title, :string
    field :item_type, :string

    belongs_to :version, Version

    has_many :messages, Message,
      foreign_key: :messageable_id,
      where: [messageable_type: "ChangelogItem"],
      preload_order: [asc: :inserted_at]

    timestamps()
  end

  @doc """
  Creates a changeset for a changelog item.

  ## Parameters
    - changelog_item: The changelog item struct to create a changeset for
    - attrs: The attributes to create the changeset with

  ## Examples
      iex> changeset(%ChangelogItem{}, %{title: "New Feature", item_type: "feature"})
  """
  def changeset(changelog_item, attrs) do
    changelog_item
    |> cast(attrs, [:item_type, :title, :description, :version_id])
    |> validate_required([:item_type, :title, :description, :version_id])
    |> validate_inclusion(:item_type, @item_types,
      message: "must be one of: #{Enum.join(@item_types, ", ")}"
    )
    |> validate_length(:title, min: 5, max: 100)
    |> validate_length(:description, min: 10)
    |> foreign_key_constraint(:version_id)
  end
end
