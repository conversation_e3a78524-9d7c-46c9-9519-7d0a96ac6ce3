defmodule ServiceManager.Versions.IssueLabel do
  use Ecto.Schema
  import Ecto.Changeset

  schema "issue_labels" do
    belongs_to :issue, ServiceManager.Versions.Issue
    belongs_to :label, ServiceManager.Versions.Label

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for an issue-label association.

  ## Parameters
    - issue_label: The issue_label struct to create a changeset for
    - attrs: The attributes to create the changeset with

  ## Examples
      iex> changeset(%IssueLabel{}, %{issue_id: 1, label_id: 2})
  """
  def changeset(issue_label, attrs) do
    issue_label
    |> cast(attrs, [:issue_id, :label_id])
    |> validate_required([:issue_id, :label_id])
    |> foreign_key_constraint(:issue_id)
    |> foreign_key_constraint(:label_id)
    |> unique_constraint([:issue_id, :label_id],
      message: "label already assigned to this issue"
    )
  end
end
