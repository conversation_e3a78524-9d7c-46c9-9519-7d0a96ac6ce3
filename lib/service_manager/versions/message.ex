defmodule ServiceManager.Versions.Message do
  use Ecto.Schema
  import Ecto.Changeset

  alias __MODULE__, as: Message
  alias ServiceManager.Schemas.AdminUsers

  @messageable_types ~w(ChangelogItem Issue QAndA)

  schema "messages" do
    field :content, :string
    field :messageable_type, :string
    field :messageable_id, :integer
    field :type, :string

    belongs_to :user, Ad<PERSON>U<PERSON><PERSON>
    belongs_to :parent, Message
    has_many :replies, Message, foreign_key: :parent_id

    timestamps()
  end

  @message_types ~w(changelog issue qa)

  @doc """
  Creates a changeset for a message.

  ## Parameters
    - message: The message struct to create a changeset for
    - attrs: The attributes to create the changeset with

  ## Examples
      iex> changeset(%Message{}, %{content: "This is a reply", messageable_type: "Issue"})
  """
  def changeset(message, attrs) do
    message
    |> cast(attrs, [:content, :messageable_type, :messageable_id, :user_id, :parent_id, :type])
    |> validate_required([:content, :messageable_type, :messageable_id, :user_id, :type])
    |> validate_inclusion(:type, @message_types)
    |> validate_inclusion(:messageable_type, @messageable_types,
      message: "must be one of: #{Enum.join(@messageable_types, ", ")}"
    )
    |> validate_length(:content, min: 2, max: 10_000)
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:parent_id)
    |> check_constraint(:messageable_type,
      name: "valid_messageable_type",
      message: "must be one of: ChangelogItem, Issue, QAndA"
    )
    |> validate_parent_message()
  end

  @doc """
  Validates that a message's parent belongs to the same thread.
  """
  def validate_parent_message(changeset) do
    case {get_field(changeset, :parent_id), get_field(changeset, :messageable_id)} do
      {nil, _} ->
        changeset

      {parent_id, messageable_id} ->
        # This will be validated at the context level to ensure parent message
        # belongs to the same messageable (issue or Q&A)
        check_constraint(changeset, :parent_id,
          name: "parent_message_belongs_to_same_thread",
          message: "parent message must belong to the same thread"
        )
    end
  end
end
