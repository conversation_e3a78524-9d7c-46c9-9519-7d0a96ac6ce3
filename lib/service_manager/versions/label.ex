defmodule ServiceManager.Versions.Label do
  use Ecto.Schema
  import Ecto.Changeset

  @default_colors ~w(#FF0000 #00FF00 #0000FF #FFFF00 #FF00FF #00FFFF)

  schema "labels" do
    field :name, :string
    field :color, :string

    many_to_many :issues, ServiceManager.Versions.Issue,
      join_through: ServiceManager.Versions.IssueLabel,
      on_replace: :delete

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for a label.

  ## Parameters
    - label: The label struct to create a changeset for
    - attrs: The attributes to create the changeset with

  ## Examples
      iex> changeset(%Label{}, %{name: "bug", color: "#FF0000"})
  """
  def changeset(label, attrs) do
    label
    |> cast(attrs, [:name, :color])
    |> validate_required([:name, :color])
    |> validate_length(:name, min: 2, max: 20)
    |> validate_format(:color, ~r/^#[0-9A-Fa-f]{6}$/,
      message: "must be a valid hex color code (e.g., #FF0000)"
    )
    |> unique_constraint(:name)
    |> update_change(:name, &String.downcase/1)
  end

  @doc """
  Returns a list of default color options for labels.
  """
  def default_colors, do: @default_colors
end
