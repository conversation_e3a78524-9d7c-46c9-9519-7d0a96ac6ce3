defmodule ServiceManager.Versions.QAndA do
  use Ecto.Schema
  import Ecto.Changeset

  alias ServiceManager.Versions.{Version, Message}
  alias ServiceManager.Schemas.AdminUsers

  @status_types ~w(open answered)

  @derive {Jason.Encoder,
           only: [:id, :title, :question, :status, :votes_count, :messages, :creator]}

  schema "q_and_as" do
    field :status, :string
    field :title, :string
    field :question, :string
    field :votes_count, :integer, default: 0

    belongs_to :version, Version
    belongs_to :creator, AdminUsers, foreign_key: :created_by

    has_many :messages, Message,
      foreign_key: :messageable_id,
      where: [messageable_type: "QAndA"],
      preload_order: [asc: :inserted_at]

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for a Q&A entry.

  ## Parameters
    - qa: The Q&A struct to create a changeset for
    - attrs: The attributes to create the changeset with

  ## Examples
      iex> changeset(%QAndA{}, %{title: "How to...", status: "open"})
  """
  def changeset(qa, attrs) do
    qa
    |> cast(attrs, [:title, :question, :status, :votes_count, :version_id, :created_by])
    |> validate_required([:title, :question, :status, :version_id, :created_by])
    |> validate_inclusion(:status, @status_types,
      message: "must be one of: #{Enum.join(@status_types, ", ")}"
    )
    |> validate_length(:title, min: 5, max: 100)
    |> validate_length(:question, min: 10)
    |> validate_number(:votes_count, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:version_id)
    |> foreign_key_constraint(:created_by)
  end
end
