defmodule ServiceManager.Monitoring.EndpointConfig do
  @moduledoc """
  Handles parsing and loading of endpoint configurations from a text file.
  Each line in the file represents an endpoint to monitor with the format:

  GET|https://example.com/api/health|200
  POST|https://example.com/api/check|{"test":"data"}|{"status":"ok"}

  Format: METHOD|URL|[REQUEST_BODY]|[EXPECTED_RESPONSE]
  """

  @doc """
  Loads endpoint configurations from the specified file.
  Returns a list of endpoint configuration maps.
  """
  @spec load_endpoints(String.t()) :: {:ok, list(map())} | {:error, term()}
  def load_endpoints(file_path) do
    case File.read(file_path) do
      {:ok, content} ->
        endpoints =
          content
          |> String.split("\n", trim: true)
          |> Enum.map(&String.trim/1)
          # Skip comment lines
          |> Enum.reject(&String.starts_with?(&1, "#"))
          |> Enum.map(&parse_endpoint/1)
          |> Enum.reject(&is_nil/1)

        {:ok, endpoints}

      {:error, reason} ->
        {:error, "Failed to read endpoints file: #{inspect(reason)}"}
    end
  end

  @doc """
  Parses a single endpoint configuration line.
  """
  @spec parse_endpoint(String.t()) :: map() | nil
  def parse_endpoint(line) do
    parts = String.split(line, "|", trim: true)

    case parts do
      # GET request with expected status code
      [method, url, expected] when method in ["GET", "get"] ->
        %{
          method: :get,
          url: url,
          body: nil,
          expected_response: expected,
          id: url
        }

      # POST request with body and expected response
      [method, url, body, expected] when method in ["POST", "post"] ->
        %{
          method: :post,
          url: url,
          body: body,
          expected_response: expected,
          id: url
        }

      # Invalid format
      _ ->
        require Logger
        Logger.warn("Invalid endpoint configuration: #{line}")
        nil
    end
  end
end
