defmodule ServiceManager.Monitoring.HttpChecker do
  @moduledoc """
  Handles HTTP requests to check endpoint status and validate responses.
  """

  require Logger

  @doc """
  Checks an endpoint by making an HTTP request and validating the response.
  Returns :ok if the endpoint is up and meets the expected criteria,
  or {:error, reason} if the endpoint is down or doesn't meet the criteria.
  """
  @spec check_endpoint(map()) :: :ok | {:error, String.t()}
  def check_endpoint(endpoint) do
    %{
      method: method,
      url: url,
      body: body,
      expected_response: expected
    } = endpoint

    Logger.debug("Checking endpoint: #{method} #{url}")

    try do
      case make_request(method, url, body) do
        {:ok, %HTTPoison.Response{status_code: status, body: response_body}} ->
          validate_response(status, response_body, expected)

        {:error, %HTTPoison.Error{reason: reason}} ->
          {:error, "HTTP request failed: #{inspect(reason)}"}
      end
    rescue
      e ->
        {:error, "Exception during HTTP check: #{inspect(e)}"}
    catch
      kind, reason ->
        {:error, "Error during HTTP check: #{inspect(kind)} #{inspect(reason)}"}
    end
  end

  # Makes an HTTP request based on the method
  defp make_request(:get, url, _body) do
    HTTPoison.get(url, [{"Accept", "application/json"}], recv_timeout: 10_000)
  end

  defp make_request(:post, url, body) do
    headers = [{"Content-Type", "application/json"}, {"Accept", "application/json"}]
    HTTPoison.post(url, body || "{}", headers, recv_timeout: 10_000)
  end

  # Validates the response against expected criteria
  defp validate_response(status, body, expected) do
    cond do
      # If expected is a status code (as string)
      expected =~ ~r/^\d+$/ ->
        expected_status = String.to_integer(expected)

        if status == expected_status do
          :ok
        else
          {:error, "Status code mismatch: expected #{expected_status}, got #{status}"}
        end

      # If expected is a JSON pattern
      String.starts_with?(expected, "{") ->
        validate_json_response(body, expected)

      # If expected is a string pattern
      true ->
        if String.contains?(body, expected) do
          :ok
        else
          {:error, "Response body doesn't contain expected pattern: #{expected}"}
        end
    end
  end

  # Validates a JSON response against an expected JSON pattern
  defp validate_json_response(body, expected_json) do
    with {:ok, body_json} <- Jason.decode(body),
         {:ok, expected} <- Jason.decode(expected_json) do
      # Check if all expected keys and values are in the response
      missing = find_missing_keys_and_values(expected, body_json)

      if Enum.empty?(missing) do
        :ok
      else
        {:error, "JSON response missing expected values: #{inspect(missing)}"}
      end
    else
      {:error, %Jason.DecodeError{}} ->
        {:error, "Invalid JSON in response or expected pattern"}

      err ->
        {:error, "JSON validation error: #{inspect(err)}"}
    end
  end

  # Recursively finds missing keys and values in the actual JSON compared to expected
  defp find_missing_keys_and_values(expected, actual, path \\ [])

  defp find_missing_keys_and_values(%{} = expected, %{} = actual, path) do
    Enum.flat_map(expected, fn {k, v} ->
      current_path = path ++ [k]

      if Map.has_key?(actual, k) do
        find_missing_keys_and_values(v, actual[k], current_path)
      else
        [current_path]
      end
    end)
  end

  defp find_missing_keys_and_values(expected, actual, path)
       when is_list(expected) and is_list(actual) do
    # For lists, we just check if any item in expected matches each item
    # This is a simplified approach - could be enhanced for more complex matching
    if Enum.all?(expected, fn exp_item ->
         Enum.any?(actual, fn act_item ->
           Enum.empty?(find_missing_keys_and_values(exp_item, act_item, path))
         end)
       end) do
      []
    else
      [path]
    end
  end

  defp find_missing_keys_and_values(expected, actual, path) do
    if expected == actual do
      []
    else
      [path]
    end
  end
end
