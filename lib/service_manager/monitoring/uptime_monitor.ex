defmodule ServiceManager.Monitoring.UptimeMonitor do
  @moduledoc """
  GenServer that monitors API endpoints and URLs for uptime.

  Features:
  - Reads endpoints from a text file
  - Periodically checks if each endpoint is up
  - Maintains in-memory state of service status
  - Sends Telegram alerts when services go down
  - Avoids duplicate alerts for continued downtime
  - Sends recovery notifications when services come back up
  """

  use GenServer
  require Logger

  alias ServiceManager.Monitoring.EndpointConfig
  alias ServiceManager.Monitoring.HttpChecker

  @check_message :check_endpoints

  # Client API

  @doc """
  Starts the uptime monitor GenServer.
  """
  def start_link(opts \\ []) do
    name = Keyword.get(opts, :name, __MODULE__)
    GenServer.start_link(__MODULE__, opts, name: name)
  end

  @doc """
  Forces an immediate check of all endpoints.
  """
  def check_now do
    GenServer.cast(__MODULE__, @check_message)
  end

  @doc """
  Returns the current status of all monitored endpoints.
  """
  def get_status do
    GenServer.call(__MODULE__, :get_status)
  end

  @doc """
  Reloads endpoint configurations from the file.
  """
  def reload_endpoints do
    GenServer.call(__MODULE__, :reload_endpoints)
  end

  # Server Callbacks

  @impl true
  def init(opts) do
    # Get configuration
    endpoints_file = get_config(:endpoints_file, "endpoints.txt")
    # Default: 1 minute
    check_interval = get_config(:check_interval, 60_000)

    # Initialize state
    state = %{
      endpoints: [],
      status: %{},
      check_interval: check_interval,
      endpoints_file: endpoints_file
    }

    # Load endpoints and schedule first check
    {:ok, state, {:continue, :load_endpoints}}
  end

  @impl true
  def handle_continue(:load_endpoints, state) do
    # Load endpoints from file
    case EndpointConfig.load_endpoints(state.endpoints_file) do
      {:ok, endpoints} ->
        Logger.info("Loaded #{length(endpoints)} endpoints for monitoring")

        # Initialize status map for all endpoints
        status = initialize_status(endpoints, state.status)

        # Schedule first check
        # Immediate first check
        schedule_check(0)

        {:noreply, %{state | endpoints: endpoints, status: status}}

      {:error, reason} ->
        Logger.error("Failed to load endpoints: #{reason}")

        # Schedule retry
        # Retry in 10 seconds
        schedule_check(10_000)

        {:noreply, state}
    end
  end

  @impl true
  def handle_call(:get_status, _from, state) do
    {:reply, state.status, state}
  end

  @impl true
  def handle_call(:reload_endpoints, _from, state) do
    case EndpointConfig.load_endpoints(state.endpoints_file) do
      {:ok, endpoints} ->
        Logger.info("Reloaded #{length(endpoints)} endpoints for monitoring")

        # Update status map for all endpoints, preserving existing status
        status = initialize_status(endpoints, state.status)

        {:reply, :ok, %{state | endpoints: endpoints, status: status}}

      {:error, reason} ->
        Logger.error("Failed to reload endpoints: #{reason}")
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_cast(@check_message, state) do
    # Check all endpoints
    new_status = check_all_endpoints(state.endpoints, state.status)

    # Schedule next check
    schedule_check(state.check_interval)

    {:noreply, %{state | status: new_status}}
  end

  @impl true
  def handle_info(@check_message, state) do
    # Same as handle_cast
    new_status = check_all_endpoints(state.endpoints, state.status)

    # Schedule next check
    schedule_check(state.check_interval)

    {:noreply, %{state | status: new_status}}
  end

  # Helper functions

  # Initialize status map for all endpoints
  defp initialize_status(endpoints, existing_status) do
    Enum.reduce(endpoints, %{}, fn endpoint, acc ->
      id = endpoint.id

      # Use existing status if available, otherwise initialize as :unknown
      status =
        if Map.has_key?(existing_status, id) do
          existing_status[id]
        else
          %{
            status: :unknown,
            last_checked: nil,
            last_status_change: nil,
            consecutive_failures: 0,
            endpoint: endpoint
          }
        end

      Map.put(acc, id, status)
    end)
  end

  # Check all endpoints and update status
  defp check_all_endpoints(endpoints, status) do
    Enum.reduce(endpoints, status, fn endpoint, acc ->
      # Get current status
      current = Map.get(acc, endpoint.id)

      # Check endpoint
      new_status = check_endpoint(endpoint, current)

      # Update status map
      Map.put(acc, endpoint.id, new_status)
    end)
  end

  # Check a single endpoint and update its status
  defp check_endpoint(endpoint, current_status) do
    result = HttpChecker.check_endpoint(endpoint)
    now = DateTime.utc_now()

    case result do
      :ok ->
        handle_success(endpoint, current_status, now)

      {:error, reason} ->
        handle_failure(endpoint, current_status, reason, now)
    end
  end

  # Handle successful endpoint check
  defp handle_success(endpoint, current_status, now) do
    # If previously down or unknown, send recovery notification
    if current_status.status in [:down, :unknown] do
      send_recovery_notification(endpoint)

      %{
        status: :up,
        last_checked: now,
        last_status_change: now,
        consecutive_failures: 0,
        endpoint: endpoint
      }
    else
      # Already up, just update timestamp
      %{current_status | last_checked: now, consecutive_failures: 0}
    end
  end

  # Handle failed endpoint check
  defp handle_failure(endpoint, current_status, reason, now) do
    failures = current_status.consecutive_failures + 1

    # If first failure or previously up, send down notification
    if current_status.status in [:up, :unknown] do
      send_down_notification(endpoint, reason)

      %{
        status: :down,
        last_checked: now,
        last_status_change: now,
        consecutive_failures: failures,
        endpoint: endpoint,
        last_error: reason
      }
    else
      # Already down, just update timestamp and failure count
      %{current_status | last_checked: now, consecutive_failures: failures, last_error: reason}
    end
  end

  # Send notification when an endpoint goes down
  defp send_down_notification(endpoint, reason) do
    message = "🔴 SERVICE DOWN: #{endpoint.url} - #{reason}"
    Logger.warn(message)
    send_telegram_notification(message)
  end

  # Send notification when an endpoint recovers
  defp send_recovery_notification(endpoint) do
    message = "🟢 SERVICE RESTORED: #{endpoint.url}"
    Logger.info(message)
    send_telegram_notification(message)
  end

  # Send notification to Telegram
  defp send_telegram_notification(message) do
    telegram_channel_id = Application.get_env(:service_manager, :telegram_channel_id)

    if telegram_channel_id do
      Task.start(fn ->
        try do
          Telegex.send_message(telegram_channel_id, message)
        rescue
          e ->
            Logger.error("Failed to send Telegram notification: #{inspect(e)}")
        catch
          kind, reason ->
            Logger.error(
              "Error sending Telegram notification: #{inspect(kind)} #{inspect(reason)}"
            )
        end
      end)
    else
      Logger.warn("Telegram channel ID not configured, skipping notification")
    end
  end

  # Schedule next check
  defp schedule_check(interval) do
    Logger.warn("Uptime polling")
    Process.send_after(self(), @check_message, interval)
  end

  # Get configuration with fallback
  defp get_config(key, default) do
    Application.get_env(:service_manager, __MODULE__, [])
    |> Keyword.get(key, default)
  end
end
