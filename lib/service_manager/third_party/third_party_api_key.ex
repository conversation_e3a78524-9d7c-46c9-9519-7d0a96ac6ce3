defmodule ServiceManager.ThirdParty.ThirdPartyApiKey do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset
  alias ServiceManager.Repo

  schema "third_party_api_key" do
    field :api_key, :string
    field :description, :string
    belongs_to :user, ServiceManager.Schemas.AdminUsers

    timestamps(type: :utc_datetime)
  end

  def changeset(api_key, attrs, opts \\ []) do
    api_key
    |> cast(attrs, [:api_key, :description])
    |> validate_required([:api_key, :description])
  end

  @doc """
  Validates if an API key exists and is active.
  Returns {:ok, api_key} if valid, {:error, reason} if not.
  """
  def validate_api_key(api_key) do
    case Repo.get_by(__MODULE__, api_key: api_key) do
      nil -> {:error, :invalid_api_key}
      api_key -> {:ok, api_key}
    end
  end
end
