defmodule ServiceManager.Broadcasters.LoggerBroadcaster do
  @moduledoc """
  Helper module for broadcasting log messages through the logger channel.
  """

  require Logger

  @doc """
  Broadcasts an info level log message.
  """
  def info(message) do
    broadcast("info", message)
    Logger.info(message)
  end

  @spec error(any()) :: :ok
  @doc """
  Broadcasts an error level log message.
  """
  def error(message) do
    # broadcast("error", message)
    # Logger.error(message)
  end

  @doc """
  Broadcasts a debug level log message.
  """
  def debug(message) do
    broadcast("debug", message)
    Logger.debug(message)
  end

  @doc """
  Broadcasts a warning level log message.
  """
  def warn(message) do
    broadcast("warn", message)
    Logger.warn(message)
  end

  @doc """
  Broadcasts a custom event with payload.
  """
  def broadcast_event(event, payload) do
    Phoenix.PubSub.broadcast(
      ServiceManager.PubSub,
      "logger:logs",
      {:custom_event, event, payload}
    )
  end

  defp broadcast(level, message) do
    payload = %{
      "level" => level,
      "message" => message,
      "timestamp" => DateTime.utc_now() |> to_string()
    }

    Phoenix.PubSub.broadcast(ServiceManager.PubSub, "logger:logs", {:log, payload})
  end
end
