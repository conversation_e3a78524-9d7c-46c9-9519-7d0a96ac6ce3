defmodule ServiceManager.LoanMgt.Helpers.ApiHelpers do
  import Plug.Conn
  import Phoenix.Controller, only: [json: 2]

  def unauthorized(conn, data, message \\ nil) do
    conn
    |> put_status(401)
    |> json(%{data: data, message: message})
  end

  def success(conn, data, message \\ nil) do
    conn
    |> put_status(200)
    |> json(%{message: message, data: data})
  end

  def onlysuccess(conn, message \\ nil) do
    conn
    |> put_status(200)
    |> json(%{message: message})
  end

  def only_success(conn, message \\ nil) do
    conn
    |> put_status(200)
    |> json(%{message: message})
  end

  def no_content(conn, message \\ "No Data Found") do
    conn
    |> put_status(204)
    |> json(%{data: nil, message: message})
  end

  def created(conn, message \\ "Data Created", data \\ nil) do
    conn
    |> put_status(201)
    |> json(%{message: message, data: data})
  end

  def updated(conn, message \\ "Data Updated", data \\ nil) do
    conn
    |> put_status(202)
    |> json(%{message: message, data: data})
  end

  def deleted(conn, message \\ "Data Deleted", data \\ nil) do
    conn
    |> put_status(202)
    |> json(%{data: data, message: message})
  end

  def error(conn, message \\ "Something Went Wrong", data \\ nil) do
    conn
    |> put_status(400)
    |> json(%{data: data, message: message})
  end

  def validation(conn, message \\ "Invalid Submission", data \\ nil) do
    conn
    |> put_status(422)
    |> json(%{data: data, message: message})
  end
end
