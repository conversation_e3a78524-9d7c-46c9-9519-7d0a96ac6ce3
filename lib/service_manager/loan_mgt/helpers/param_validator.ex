defmodule ServiceManager.LoanMgt.Helpers.ParamsValidator do
  import Plug.Conn
  alias <PERSON><PERSON><PERSON>

  def validate(params, schema) do
    params
    |> Skooma.valid?(schema)
    |> case do
      {:error, message} -> {:error, message}
      :ok -> :ok
    end
  end

  def validate(conn, params, schema) do
    params
    |> Skooma.valid?(schema)
    |> case do
      {:error, message} ->
        {:error, put_status(conn, :bad_request), message |> Enum.at(0)}

      :ok ->
        {:ok, conn}
    end
  end

  # =========== Loan Validation =============
  def loans_validation(params, :eligible) do
    %{product_id: [:int]} |> handle_validation(params)
  end

  def loans_validation(params, :apply) do
    %{amount: [:number], elegibility_id: [:int]} |> handle_validation(params)
  end

  def loans_validation(params, :confirm) do
    %{terms_id: [:int], elegibility_id: [:int]} |> handle_validation(params)
  end

  def loans_validation(params, :repay) do
    %{
      loan_id: [:int],
      amount: [:number],
      pay_full: [:bool],
      account_number: [
        :string,
        Skooma.Validators.min_length(10),
        Skooma.Validators.max_length(16)
      ]
    }
    |> handle_validation(params)
  end

  defp handle_validation(schema, params) do
    params
    |> Skooma.valid?(schema)
    |> case do
      {:error, message} -> {:error, message}
      :ok -> {:ok, "Successfully validated"}
    end
  end
end
