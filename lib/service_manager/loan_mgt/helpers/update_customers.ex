defmodule ServiceManager.LoanMgt.Helpers.UpdateCustomers do
  alias ServiceManager.Repo
  alias ServiceManager.Accounts
  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Schemas.Customer

  # ServiceManager.LoanMgt.Helpers.UpdateCustomers.update_customers()
  def update_customers() do
    customers = Repo.all(Customer)

    Enum.each(customers, fn customer ->
      usern = Accounts.user_by_account_number(customer.account_number)

      customer
      |> LoanMgt.update_loan_customer(%{customer_id: usern.id})
    end)
  end
end
