defmodule ServiceManager.LoanMgt.Helpers.ISearchComponent do
  @moduledoc false
  use ServiceManagerWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="row mb-3">
      <div class="col-sm-12 col-md-6 d-flex align-items-center justify-content-start">
        <form class="dataTables_filter" phx-change="iSearch">
          <div class="relative">
            <input
              type="search"
              class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={@params["filter"]["isearch"]}
              name="isearch"
              placeholder="Search"
              aria-describedby="basic-addon3"
            />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-gray-400 absolute left-3 top-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </form>
      </div>
    </div>
    """
  end

  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)}
  end

  #  @impl true
  #  def handle_event("iSearch", params, socket)do
  #    live_patch "iSearch", to: "?"<>querystring params
  #    {:noreply, socket}
  #  end
end
