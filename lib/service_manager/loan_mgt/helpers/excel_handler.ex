defmodule ServiceManager.LoanMgt.Helpers.ExcelHandler do
  alias Elixlsx.{Workbook, Sheet}

  alias ServiceManager.Accounts
  alias NimbleCSV.RFC4180, as: CSV
  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Helpers.Utils

  def process_uploaded_file(uploads, product_id, partner_id) do
    with {:ok, entry} <- get_first_upload(uploads),
         {:ok, path} <- get_path(entry),
         {:ok, extension} <- validate_extension(path),
         {:ok, processed_data} <- process_file(path, extension, product_id, partner_id) do
      {:ok, processed_data}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp get_first_upload(uploads) do
    case List.first(uploads.uploaded_file.entries) do
      nil -> {:error, "No file uploaded"}
      entry -> {:ok, entry}
    end
  end

  def get_path(entry) do
    upload_dir = Path.join(:code.priv_dir(:service_manager), "static/uploads/loans")
    path = Path.join(upload_dir, entry.client_name)
    {:ok, path}
  end

  defp validate_extension(path) do
    case Path.extname(path) do
      ".csv" -> {:ok, ".csv"}
      ".xlsx" -> {:ok, ".xlsx"}
      ext -> {:error, "Unsupported file format: #{ext}"}
    end
  end

  defp process_file(path, ".csv", product_id, partner_id) do
    if File.exists?(path) do
      process_csv_file(path, product_id, partner_id)
    else
      {:error, "File not found: #{path}"}
    end
  end

  defp process_file(path, ".xlsx", product_id, partner_id) do
    if File.exists?(path) do
      process_xlsx_file(path, product_id, partner_id)
    else
      {:error, "File not found: #{path}"}
    end
  end

  defp process_csv_file(path, product_id, partner_id) do
    try do
      result =
        path
        |> File.read!()
        |> String.split("\n")
        |> Enum.reject(&(String.trim(&1) == ""))
        |> Enum.map(&format_ps(&1))
        |> Enum.map(&String.split(&1, ","))
        |> Enum.map(&remove_empties(&1))
        |> Enum.reject(&(&1 == []))
        |> Enum.drop(1)
        |> Enum.map(fn row ->
          %{
            first_name: Enum.at(row, 2),
            last_name: Enum.at(row, 3),
            phone: Enum.at(row, 4),
            account_number: Enum.at(row, 0),
            account_type: Enum.at(row, 1),
            max_loan_amount: Decimal.new(Enum.at(row, 5))
          }
        end)
        |> Enum.map(&phin(&1, product_id, partner_id))
        |> Enum.dedup()

      {:ok, result}
    rescue
      e -> {:error, "Error processing CSV: #{inspect(e)}"}
    end
  end

  def format_ps(data) do
    data
    |> String.trim()
    |> String.split(",")
    |> Enum.take(6)
    |> Enum.join(",")
  end

  defp remove_empties(row) do
    count = Enum.count(row)
    last = List.last(row) |> String.replace("\r", "")
    row = List.replace_at(row, count - 1, last)

    case Enum.join(row) do
      "" ->
        []

      any ->
        case String.length(any) > 10 do
          true -> Enum.take(row, 6)
          false -> []
        end
    end
  end

  @map ~w(account_number account_type first_name last_name phone max_loan_amount)

  def process_xlsx_file(path, product_id, partner_id) do
    case Xlsxir.multi_extract(path, 0, false, extract_to: :memory) do
      {:ok, id} ->
        items =
          Xlsxir.get_list(id)
          # Drop the first two rows (header and possibly empty row)
          |> Enum.drop(2)
          |> Enum.reject(&Enum.empty?/1)
          |> Enum.reject(fn row -> Enum.all?(row, &is_nil/1) end)
          |> Enum.map(fn row -> Enum.zip(@map, Enum.map(row, &strgfy_term/1)) end)
          |> Enum.map(&Enum.into(&1, %{}))
          |> Enum.reject(&(Enum.join(Map.values(&1)) == ""))
          |> Enum.map(&phin(Utils.to_atomic_map(&1), product_id, partner_id))

        Xlsxir.close(id)
        {:ok, items}

      {:error, reason} ->
        {:error, "Error extracting XLSX: #{inspect(reason)}"}

      unexpected ->
        {:error, "Unexpected result from Xlsxir.extract: #{inspect(unexpected)}"}
    end
  end

  defp strgfy_term(term) when is_tuple(term), do: Enum.join(Tuple.to_list(term))

  defp strgfy_term(term) when not is_tuple(term),
    do: String.trim("#{term}") |> String.replace("'", "")

  defp phin(p, product_id, partner_id) do
    {valid?, reason} =
      valid_entry?(p.account_number, p.account_type, p.max_loan_amount, product_id, partner_id)

    Map.merge(
      p,
      %{
        max_loan_amount: Decimal.new(p.max_loan_amount),
        product_id: String.to_integer(product_id),
        valid?: valid?,
        reason: reason
      }
    )
  end

  defp valid_entry?(account_number, account_type, max_loan_amount, product_id, partner_id) do
    product = LoanMgt.get_loan_product!(product_id)

    cond do
      !is_binary(account_number) || account_number == "" ->
        {false, "Invalid account number"}

      !is_binary(account_type) || account_type == "" ->
        {false, "Invalid account type"}

      account_type not in ["WALLET", "BANK_ACCOUNT"] ->
        {false, "Account type must be either WALLET or BANK_ACCOUNT"}

      max_loan_amount == 0 ->
        {false, "Invalid max loan amount"}

      valid_amounts?(max_loan_amount, product) == false ->
        {false, "Amount must be between #{product.min_amount} and #{product.max_amount}"}

      account_exists?(account_number, account_type) == false ->
        {false, "Account number does not exist"}

      account_has_active_loan?(account_number, product_id) == true ->
        {false, "Account already has an active loan"}

      has_active_record?(account_number, product_id) == true ->
        {false, "There is an active customer record for this product."}

      true ->
        {true, nil}
    end
  end

  defp account_exists?(account_number, "BANK_ACCOUNT") do
    case Accounts.user_by_account_number(account_number) do
      nil -> false
      _ -> true
    end
  end

  defp account_exists?(account_number, "WALLET"), do: false

  defp account_has_active_loan?(account_number, product_id) do
    case LoanMgt.active_loans_by_product_and_acc_num(product_id, account_number) do
      [] -> false
      _ -> true
    end
  end

  def valid_amounts?(max_loan_amount, product) do
    min = product.min_amount
    max = product.max_amount

    case Decimal.compare(max_loan_amount, min) do
      :lt ->
        false

      _ ->
        case Decimal.compare(max_loan_amount, max) do
          :gt -> false
          _ -> true
        end
    end
  end

  def has_active_record?(account_number, product_id) do
    case LoanMgt.eligible_by_product_and_acc_num(product_id, account_number) do
      nil -> false
      _ -> true
    end
  end

  # =============== LOAN PRODUCTS UPLOADS =================
  def process_loan_products_file(uploads) do
    with {:ok, entry} <- get_first_upload(uploads),
         {:ok, path} <- get_path(entry),
         {:ok, extension} <- validate_extension(path),
         {:ok, processed_data} <- process_loan_product_file(path, extension) do
      {:ok, processed_data}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp process_loan_product_file(path, ".csv") do
    if File.exists?(path) do
      process_loan_product_csv(path)
    else
      {:error, "File not found: #{path}"}
    end
  end

  defp process_loan_product_file(path, ".xlsx") do
    if File.exists?(path) do
      process_loan_product_xlsx(path)
    else
      {:error, "File not found: #{path}"}
    end
  end

  defp process_loan_product_csv(path) do
    try do
      products =
        path
        |> File.stream!()
        |> CSV.parse_stream()
        # Drop the header row
        |> Enum.drop(0)
        |> Enum.map(fn row ->
          %{
            name: to_string(Enum.at(row, 0)),
            description: to_string(Enum.at(row, 1)),
            min_amount: parse_decimal(Enum.at(row, 2)),
            max_amount: parse_decimal(Enum.at(row, 3)),
            interest_rate: parse_decimal(Enum.at(row, 4)),
            default_duration: parse_integer(Enum.at(row, 5))
          }
        end)
        |> Enum.map(&parse_loan_product/1)
        |> Enum.filter(&(&1 != nil))

      {:ok, products}
    rescue
      e -> {:error, "Error processing CSV: #{inspect(e)}"}
    end
  end

  @product_map ~w(name description min_amount max_amount interest_rate default_duration)

  defp process_loan_product_xlsx(path) do
    case Xlsxir.multi_extract(path, 0, false, extract_to: :memory) do
      {:ok, id} ->
        items =
          Xlsxir.get_list(id)
          # Drop the first two rows (header and possibly empty row)
          |> Enum.drop(2)
          |> Enum.reject(&Enum.empty?/1)
          |> Enum.reject(fn row -> Enum.all?(row, &is_nil/1) end)
          |> Enum.map(fn row -> Enum.zip(@product_map, Enum.map(row, &strgfy_term/1)) end)
          |> Enum.map(&Enum.into(&1, %{}))
          |> Enum.map(fn row ->
            row = Utils.to_atomic_map(row)

            %{
              name: to_string(row.name),
              description: to_string(row.description),
              min_amount: parse_decimal(row.min_amount),
              max_amount: parse_decimal(row.max_amount),
              interest_rate: parse_decimal(row.interest_rate),
              default_duration: parse_integer(row.default_duration)
            }
          end)
          |> Enum.reject(&(Enum.join(Map.values(&1)) == ""))
          |> Enum.map(&parse_loan_product(&1))

        Xlsxir.close(id)
        {:ok, items}

      {:error, reason} ->
        {:error, "Error extracting XLSX: #{inspect(reason)}"}

      unexpected ->
        {:error, "Unexpected result from Xlsxir.extract: #{inspect(unexpected)}"}
    end
  end

  defp parse_loan_product(row) do
    {valid?, reason} =
      valid_loan_product?(row.min_amount, row.max_amount, row.interest_rate, row.default_duration)

    Map.merge(
      row,
      %{
        is_active: true,
        valid?: valid?,
        reason: reason
      }
    )
  end

  defp valid_loan_product?(min_amount, max_amount, interest_rate, default_duration) do
    cond do
      min_amount == nil or max_amount == nil or interest_rate == nil or default_duration == nil ->
        {false, "Invalid values provided"}

      Decimal.compare(min_amount, Decimal.new(0)) == :lt or
          Decimal.compare(max_amount, Decimal.new(0)) == :lt ->
        {false, "Amounts must be greater than zero"}

      Decimal.compare(min_amount, max_amount) == :gt ->
        {false, "Min amount cannot be greater than max amount"}

      Decimal.compare(interest_rate, Decimal.new(0)) == :lt ->
        {false, "Interest rate must be greater than zero"}

      Decimal.compare(Decimal.new(default_duration), Decimal.new(0)) == :lt ->
        {false, "Default duration must be greater than zero"}

      true ->
        {true, nil}
    end
  end

  def parse_decimal(value) when is_binary(value) do
    case Decimal.parse(value) do
      {:ok, decimal} -> decimal
      {value, _} -> value
      :error -> nil
    end
  end

  def parse_decimal(value) when is_number(value), do: Decimal.new(value)
  def parse_decimal(_), do: nil

  def parse_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, _} -> int
      :error -> nil
    end
  end

  def parse_integer(value) when is_integer(value), do: value
  def parse_integer(_), do: nil
end
