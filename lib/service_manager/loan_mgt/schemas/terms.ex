defmodule ServiceManager.LoanMgt.Schemas.Terms do
  use Ecto.Schema
  import Ecto.Changeset

  @columns ~w(id amount charge_amount disbursed_amount interest_rate duration total_repayment product_id customer_id maker_id checker_id inserted_at updated_at)a
  @derive {Jason.Encoder, only: @columns}

  schema "loan_terms" do
    field :amount, :decimal
    field :charge_amount, :decimal
    field :disbursed_amount, :decimal
    field :interest_rate, :decimal
    field :duration, :integer
    field :total_repayment, :decimal
    belongs_to :customer, ServiceManager.Accounts.User, foreign_key: :customer_id, type: :id
    belongs_to :maker, ServiceManager.Schemas.AdminUsers, foreign_key: :maker_id, type: :id
    belongs_to :checker, ServiceManager.Schemas.AdminUsers, foreign_key: :checker_id, type: :id

    belongs_to :product, ServiceManager.LoanMgt.Schemas.Product,
      foreign_key: :product_id,
      type: :id

    timestamps()
  end

  def changeset(loan_terms, attrs) do
    loan_terms
    |> cast(attrs, @columns)
    |> validate_required([
      :amount,
      :charge_amount,
      :disbursed_amount,
      :interest_rate,
      :duration,
      :total_repayment,
      :product_id,
      :customer_id
    ])
    |> validate_number(:amount, greater_than: 0)
    |> validate_number(:interest_rate, greater_than_or_equal_to: 0)
    |> validate_number(:duration, greater_than: 0)
    |> validate_number(:total_repayment, greater_than: 0)
    |> validate_total_repayment()
    |> foreign_key_constraint(:product_id)
  end

  defp validate_total_repayment(changeset) do
    amount = get_field(changeset, :amount)
    total_repayment = get_field(changeset, :total_repayment)

    if amount && total_repayment && Decimal.cmp(total_repayment, amount) != :gt do
      add_error(changeset, :total_repayment, "must be greater than the loan amount")
    else
      changeset
    end
  end
end
