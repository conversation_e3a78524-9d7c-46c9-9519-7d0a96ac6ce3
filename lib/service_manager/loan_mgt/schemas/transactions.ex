defmodule ServiceManager.LoanMgt.Schemas.Transactions do
  use Ecto.Schema
  import Ecto.Changeset

  alias ServiceManager.LoanMgt.Schemas.Loan

  @columns ~w(id type amount opening_balance closing_balance description status status_description reference value_date
    transaction_date payment_method external_reference debit_account credit_account loan_id inserted_at updated_at
  )a
  @derive {Jason.Encoder, only: @columns}
  schema "loan_transactions" do
    field :type, :string
    field :amount, :decimal
    field :opening_balance, :decimal
    field :closing_balance, :decimal
    field :description, :string
    field :status, :string
    field :status_description, :string
    field :reference, :string
    field :value_date, :date
    field :transaction_date, :utc_datetime
    field :payment_method, :string
    field :external_reference, :string
    field :debit_account, :string
    field :credit_account, :string
    belongs_to :loan, Loan

    timestamps()
  end

  @doc false
  def changeset(transaction, attrs) do
    transaction
    |> cast(attrs, @columns)
    |> validate_required([
      :type,
      :amount,
      :status,
      :value_date,
      :transaction_date,
      :debit_account,
      :credit_account,
      :loan_id
    ])
    |> validate_inclusion(:type, ["DISBURSEMENT", "REPAYMENT", "ADJUSTMENT"])
    |> validate_inclusion(:status, ["PENDING", "COMPLETED", "FAILED", "REVERSED"])
    |> validate_number(:amount, greater_than: 0)
    |> foreign_key_constraint(:loan_id)
  end
end
