defmodule ServiceManager.LoanMgt.Schemas.Application do
  use Ecto.Schema
  import Ecto.Changeset

  schema "loan_applications" do
    field :amount, :decimal
    field :duration, :integer
    field :status, :string
    belongs_to :user, ServiceManager.Accounts.User
    belongs_to :loan_product, ServiceManager.LoanMgt.Schemas.Product

    timestamps()
  end

  def changeset(loan_application, attrs) do
    loan_application
    |> cast(attrs, [:amount, :duration, :status, :user_id, :product_id])
    |> validate_required([:amount, :duration, :status, :user_id, :product_id])
    |> validate_number(:amount, greater_than: 0)
    |> validate_number(:duration, greater_than: 0)
    |> validate_inclusion(:status, ["PENDING", "APPROVED", "REJECTED"])
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:product_id)
  end
end
