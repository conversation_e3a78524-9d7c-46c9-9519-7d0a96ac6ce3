defmodule ServiceManager.LoanMgt.Schemas.Product do
  use Ecto.Schema
  import Ecto.Changeset

  @columns ~w(id name description min_amount max_amount interest_rate default_duration loan_account collection_account is_active charge_account charge_type charge_rate
    maker_id checker_id inserted_at updated_at)a

  @derive {Jason.Encoder, only: @columns}

  schema "loan_products" do
    field :name, :string
    field :charge_rate, :decimal
    field :description, :string
    field :min_amount, :decimal
    field :max_amount, :decimal
    field :interest_rate, :decimal, default: 0
    field :default_duration, :integer
    field :loan_account, :string
    field :charge_account, :string
    field :collection_account, :string
    field :is_active, :boolean, default: true
    field :charge_type, :string, default: "NONE"
    belongs_to :maker, ServiceManager.Schemas.AdminUsers, foreign_key: :maker_id, type: :id
    belongs_to :checker, ServiceManager.Schemas.AdminUsers, foreign_key: :checker_id, type: :id

    timestamps()
  end

  def changeset(loan_product, attrs) do
    loan_product
    |> cast(attrs, @columns)
    |> validate_required([
      :name,
      :min_amount,
      :max_amount,
      :interest_rate,
      :default_duration,
      :charge_type,
      :charge_account,
      :charge_rate
    ])
    |> validate_number(:min_amount, greater_than: 0)
    |> validate_number(:max_amount, greater_than: 0)
    |> validate_number(:interest_rate, greater_than_or_equal_to: 0)
    |> validate_number(:default_duration, greater_than: 0)
    |> validate_inclusion(:charge_type, ["PERCENTAGE", "ACTUAL", "NONE"],
      message: "must be either PERCENTAGE, NONE or ACTUAL"
    )
    |> validate_max_amount_greater_than_min_amount()
  end

  defp validate_max_amount_greater_than_min_amount(changeset) do
    min_amount = get_field(changeset, :min_amount)
    max_amount = get_field(changeset, :max_amount)

    if min_amount && max_amount && Decimal.cmp(max_amount, min_amount) != :gt do
      add_error(changeset, :max_amount, "must be greater than min_amount")
    else
      changeset
    end
  end
end
