defmodule ServiceManager.LoanMgt.Schemas.LoanCharges do
  use Ecto.Schema
  import Ecto.Changeset

  alias ServiceManager.LoanMgt.Schemas.Loan
  alias ServiceManager.LoanMgt.Schemas.Product

  @columns ~w(id amount description status status_description reference value_date
    transaction_date payment_method external_reference debit_account credit_account loan_id product_id inserted_at updated_at
  )a
  @derive {Jason.Encoder, only: @columns}
  schema "loan_charges" do
    field :amount, :decimal
    field :description, :string
    field :status, :string, default: "BLOCKED"
    field :status_description, :string
    field :reference, :string
    field :value_date, :date
    field :transaction_date, :utc_datetime
    field :payment_method, :string
    field :external_reference, :string
    field :debit_account, :string
    field :credit_account, :string
    belongs_to :loan, Loan, foreign_key: :loan_id, type: :id
    belongs_to :product, Product, foreign_key: :product_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(transaction, attrs) do
    transaction
    |> cast(attrs, @columns)
    |> validate_required([
      :amount,
      :status,
      :debit_account,
      :credit_account,
      :loan_id,
      :product_id
    ])
    |> validate_inclusion(:status, ["BLOCKED", "PENDING", "COMPLETED", "FAILED"])
    |> validate_number(:amount, greater_than: 0)
    |> foreign_key_constraint(:loan_id)
    |> foreign_key_constraint(:product_id)
  end
end
