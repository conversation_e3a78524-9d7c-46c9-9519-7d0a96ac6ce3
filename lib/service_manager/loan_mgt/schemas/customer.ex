defmodule ServiceManager.LoanMgt.Schemas.Customer do
  use Ecto.Schema
  import Ecto.Changeset

  @columns ~w(id ref_id eligibility_status max_loan_amount account_number account_type last_eligibility_check partner_id
    maker_id checker_id customer_id product_id first_name last_name email phone status inserted_at updated_at)a
  @derive {Jason.Encoder, only: @columns}

  schema "loan_customers" do
    field :first_name, :string
    field :last_name, :string
    field :email, :string
    field :phone, :string
    field :status, :string
    field :ref_id, :string
    field :eligibility_status, :string
    field :max_loan_amount, :decimal
    field :account_number, :string
    field :account_type, :string
    field :last_eligibility_check, :utc_datetime
    belongs_to :maker, ServiceManager.Schemas.AdminUsers, foreign_key: :maker_id, type: :id
    belongs_to :checker, ServiceManager.Schemas.AdminUsers, foreign_key: :checker_id, type: :id
    belongs_to :customer, ServiceManager.Accounts.User, foreign_key: :customer_id, type: :id

    belongs_to :product, ServiceManager.LoanMgt.Schemas.Product,
      foreign_key: :product_id,
      type: :id

    belongs_to :partner, ServiceManager.LoanMgt.Schemas.Partnerships,
      foreign_key: :partner_id,
      type: :id

    timestamps()
  end

  @doc false
  def changeset(loan_customer, attrs) do
    loan_customer
    |> cast(attrs, @columns)
    |> validate_required([
      :ref_id,
      :customer_id,
      :product_id,
      :eligibility_status,
      :max_loan_amount,
      :last_eligibility_check,
      :partner_id
    ])
    |> validate_inclusion(:eligibility_status, ["ELIGIBLE", "INELIGIBLE", "PENDING"])
    |> validate_number(:max_loan_amount, greater_than: 0)

    # |> unique_constraint([:customer_id, :product_id])
  end
end
