defmodule ServiceManager.LoanMgt.Schemas.Partnerships do
  use Ecto.Schema
  import Ecto.Changeset

  @columns ~w(id name description partner_type contact_person contact_email contact_phone partnership_start_date partnership_end_date status maker_id checker_id inserted_at updated_at)a
  @derive {Jason.Encoder, only: @columns}

  schema "loan_partnerships" do
    field :name, :string
    field :description, :string
    field :partner_type, :string
    field :contact_person, :string
    field :contact_email, :string
    field :contact_phone, :string
    field :partnership_start_date, :date
    field :partnership_end_date, :date
    field :status, :string, default: "ACTIVE"

    belongs_to :maker, ServiceManager.Schemas.AdminUsers, foreign_key: :maker_id, type: :id
    belongs_to :checker, ServiceManager.Schemas.AdminUsers, foreign_key: :checker_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(loan_partnerships, attrs) do
    loan_partnerships
    |> cast(attrs, @columns)
    |> validate_required([
      :name,
      :partner_type,
      :contact_person,
      :contact_email,
      :contact_phone,
      :partnership_start_date,
      :status
    ])
    |> validate_format(:contact_email, ~r/^[^\s]+@[^\s]+$/,
      message: "must have the @ sign and no spaces"
    )
    |> validate_length(:contact_phone, min: 10, max: 15)
  end
end
