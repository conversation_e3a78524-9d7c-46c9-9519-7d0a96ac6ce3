defmodule ServiceManager.LoanMgt.Schemas.Loan do
  use Ecto.Schema
  import Ecto.Changeset

  @columns ~w(id amount interest_rate duration total_repayment repaid_amount remaining_balance status repayment_status
    account_type account_number user_id customer_id bank_account_id product_id charge_amount disbursed_amount inserted_at updated_at
  )a
  @derive {Jason.Encoder, only: @columns}

  schema "loans" do
    field :amount, :decimal
    field :charge_amount, :decimal
    field :disbursed_amount, :decimal
    field :interest_rate, :decimal
    field :duration, :integer
    field :total_repayment, :decimal
    field :repaid_amount, :decimal, default: 0
    field :remaining_balance, :decimal, default: 0
    field :status, :string
    field :repayment_status, :string, default: "PENDING"
    field :account_type, :string
    field :account_number, :string
    belongs_to :user, ServiceManager.Accounts.User, foreign_key: :user_id, type: :id
    belongs_to :customer, ServiceManager.Accounts.User, foreign_key: :customer_id, type: :id

    belongs_to :bank_account, ServiceManager.Accounts.FundAccounts,
      foreign_key: :bank_account_id,
      type: :id

    belongs_to :product, ServiceManager.LoanMgt.Schemas.Product,
      foreign_key: :product_id,
      type: :id

    timestamps()
  end

  def changeset(loan, attrs) do
    loan
    |> cast(attrs, @columns)
    |> validate_required([
      :amount,
      # :charge_amount,
      # :disbursed_amount,
      :interest_rate,
      :duration,
      :total_repayment,
      :status,
      :repayment_status,
      :account_type,
      :account_number,
      :user_id,
      :customer_id,
      :bank_account_id,
      :product_id
    ])
    |> validate_number(:amount, greater_than: 0)
    |> validate_number(:interest_rate, greater_than_or_equal_to: 0)
    |> validate_number(:duration, greater_than: 0)
    |> validate_number(:repaid_amount, greater_than_or_equal_to: 0)
    |> validate_inclusion(:status, ["PENDING", "APPROVED", "ACTIVE", "DISBURSED", "REJECTED"])
    |> validate_inclusion(:repayment_status, ["PENDING", "COMPLETED", "PARTIAL"])
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:bank_account_id)
    |> foreign_key_constraint(:product_id)

    # |> validate_total_repayment()
  end

  # defp validate_total_repayment(changeset) do
  #   amount = get_field(changeset, :amount)
  #   interest_rate = get_field(changeset, :interest_rate)

  #   if amount && interest_rate do
  #     min_repayment = Decimal.add(amount, Decimal.mult(amount, interest_rate))
  #     IO.inspect min_repayment, label: "========= min_repayment ========="
  #     validate_number(changeset, :total_repayment, greater_than_or_equal_to: min_repayment)
  #   else
  #     changeset
  #   end
  # end
end
