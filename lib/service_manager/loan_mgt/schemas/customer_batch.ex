defmodule ServiceManager.LoanMgt.Schemas.CustomerBatch do
  use Ecto.Schema
  import Ecto.Changeset

  @columns ~w(id batch_reference filename status valid_count invalid_count item_count maker_id checker_id partner_id inserted_at updated_at)a
  @derive {Jason.Encoder, only: @columns}

  schema "loan_customer_batch" do
    field :batch_reference, :string
    field :filename, :string
    field :status, :string, default: "PENDING"
    field :valid_count, :integer, default: 0
    field :invalid_count, :integer, default: 0
    field :item_count, :integer, default: 0

    belongs_to :maker, ServiceManager.Schemas.AdminUsers, foreign_key: :maker_id
    belongs_to :checker, ServiceManager.Schemas.AdminUsers, foreign_key: :checker_id
    belongs_to :partner, ServiceManager.LoanMgt.Schemas.Partnerships, foreign_key: :partner_id

    timestamps()
  end

  @doc false
  def changeset(loan_customer_batch, attrs) do
    loan_customer_batch
    |> cast(attrs, @columns)
    |> validate_required([
      :batch_reference,
      :filename,
      :status,
      :valid_count,
      :invalid_count,
      :item_count,
      :partner_id
    ])
    |> validate_number(:valid_count, greater_than_or_equal_to: 0)
    |> validate_number(:invalid_count, greater_than_or_equal_to: 0)
    |> validate_number(:item_count, greater_than_or_equal_to: 0)
    |> unique_constraint(:batch_reference)
  end
end
