defmodule ServiceManager.LoanMgt.Embedded.EmbeddedBatchCustomer do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key false
  embedded_schema do
    field :batch_reference, :string
    field :filename, :string
    field :status, :string
    field :valid_count, :integer
    field :invalid_count, :integer
    field :item_count, :integer
    field :partner_id, :string
    field :from, :date
    field :to, :date
  end

  def changeset(batch_customer, attrs) do
    batch_customer
    |> cast(attrs, [
      :batch_reference,
      :filename,
      :status,
      :valid_count,
      :invalid_count,
      :item_count,
      :partner_id,
      :from,
      :to
    ])
    |> validate_number(:valid_count, greater_than_or_equal_to: 0)
    |> validate_number(:invalid_count, greater_than_or_equal_to: 0)
    |> validate_number(:item_count, greater_than_or_equal_to: 0)
    |> validate_inclusion(:status, ["PENDING", "PROCESSING", "COMPLETED", "FAILED"])
  end

  def change_form(form, attrs \\ %{}) do
    changeset(form, attrs)
  end
end
