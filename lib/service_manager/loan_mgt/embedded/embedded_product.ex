defmodule ServiceManager.LoanMgt.Embedded.EmbeddedProduct do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key false
  embedded_schema do
    field :name, :string
    field :description, :string
    field :loan_account, :string
    field :collection_account, :string
    field :min_amount, :decimal
    field :max_amount, :decimal
    field :interest_rate, :decimal
    field :default_duration, :integer
    field :status, :string
    field :from, :date
    field :to, :date
  end

  def change_form(form \\ %__MODULE__{}, attrs \\ %{}), do: changeset(form, attrs)

  def changeset(form, attrs \\ %{}) do
    form
    |> cast(attrs, [
      :name,
      :description,
      :loan_account,
      :collection_account,
      :min_amount,
      :max_amount,
      :interest_rate,
      :default_duration,
      :status,
      :from,
      :to
    ])
    |> validate_required([])
    |> validate_dates()
    |> validate_amounts()
  end

  defp validate_dates(changeset) do
    case {get_field(changeset, :from), get_field(changeset, :to)} do
      {from_date, to_date} when not is_nil(from_date) and not is_nil(to_date) ->
        if Date.compare(from_date, to_date) == :gt do
          add_error(changeset, :to, "must be greater than start date")
        else
          changeset
        end

      _ ->
        changeset
    end
  end

  defp validate_amounts(changeset) do
    validate_amount_fields(changeset, [:min_amount, :max_amount, :interest_rate])
  end

  defp validate_amount_fields(changeset, []), do: changeset

  defp validate_amount_fields(changeset, [field | rest]) do
    case get_field(changeset, field) do
      nil ->
        validate_amount_fields(changeset, rest)

      amount ->
        if Decimal.compare(amount, Decimal.new(0)) == :lt do
          add_error(changeset, field, "must be greater than or equal to 0")
        else
          validate_amount_fields(changeset, rest)
        end
    end
  end
end
