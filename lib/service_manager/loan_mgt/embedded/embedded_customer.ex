defmodule ServiceManager.LoanMgt.Embedded.EmbeddedCustomer do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key false
  embedded_schema do
    field :customer_name, :string
    field :email, :string
    field :phone_number, :string
    field :account_number, :string
    field :account_type, :string
    field :eligibility_status, :string
    field :max_loan_amount, :decimal
    field :product_name, :string
    field :from, :date
    field :to, :date
  end

  def change_form(form \\ %__MODULE__{}, attrs \\ %{}), do: changeset(form, attrs)

  def changeset(customer, attrs) do
    customer
    |> cast(attrs, [
      :customer_name,
      :email,
      :phone_number,
      :account_number,
      :account_type,
      :eligibility_status,
      :max_loan_amount,
      :product_name,
      :from,
      :to
    ])
    |> validate_format(:email, ~r/^[^\s]+@[^\s]+$/, message: "must have the @ sign and no spaces")
    |> validate_format(:phone_number, ~r/^\+?[0-9]+$/, message: "must be a valid phone number")
    |> validate_number(:max_loan_amount, greater_than_or_equal_to: 0)
    |> validate_inclusion(:account_type, ["BANK_ACCOUNT", "WALLET"])
    |> validate_inclusion(:eligibility_status, ["ELIGIBLE", "INELIGIBLE"])
  end
end
