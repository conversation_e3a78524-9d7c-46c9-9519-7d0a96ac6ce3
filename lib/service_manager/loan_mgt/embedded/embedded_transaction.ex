defmodule ServiceManager.LoanMgt.Embedded.EmbeddedTransaction do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key false
  embedded_schema do
    field :start_date, :date
    field :end_date, :date
    field :status, :string
    field :type, :string
    field :amount_from, :decimal
    field :amount_to, :decimal
    field :reference, :string
    field :payment_method, :string
    field :external_reference, :string
    field :debit_account, :string
    field :credit_account, :string
  end

  def change_form(form \\ %__MODULE__{}, attrs \\ %{}), do: changeset(form, attrs)

  def changeset(form, attrs \\ %{}) do
    form
    |> cast(attrs, [
      :start_date,
      :end_date,
      :status,
      :type,
      :amount_from,
      :amount_to,
      :reference,
      :payment_method,
      :external_reference,
      :debit_account,
      :credit_account
    ])
    |> validate_required([])
    |> validate_dates()
    |> validate_amounts()
  end

  defp validate_dates(changeset) do
    case {get_field(changeset, :start_date), get_field(changeset, :end_date)} do
      {start_date, end_date} when not is_nil(start_date) and not is_nil(end_date) ->
        if Date.compare(start_date, end_date) == :gt do
          add_error(changeset, :end_date, "must be greater than start date")
        else
          changeset
        end

      _ ->
        changeset
    end
  end

  defp validate_amounts(changeset) do
    case {get_field(changeset, :amount_from), get_field(changeset, :amount_to)} do
      {from, to} when not is_nil(from) and not is_nil(to) ->
        if Decimal.compare(from, to) == :gt do
          add_error(changeset, :amount_to, "must be greater than amount from")
        else
          changeset
        end

      _ ->
        changeset
    end
  end
end
