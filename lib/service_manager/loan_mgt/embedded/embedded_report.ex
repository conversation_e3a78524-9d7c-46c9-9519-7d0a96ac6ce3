defmodule ServiceManager.LoanMgt.Embedded.EmbeddedReport do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key false
  embedded_schema do
    field :customer_name, :string
    field :product_name, :string
    field :amount, :decimal
    field :total_repayment, :decimal
    field :repaid_amount, :decimal
    field :remaining_balance, :decimal
    field :status, :string
    field :repayment_status, :string
    field :account_type, :string
    field :account_number, :string
    field :from, :date
    field :to, :date
  end

  def change_form(form \\ %__MODULE__{}, attrs \\ %{}), do: changeset(form, attrs)

  def changeset(form, attrs \\ %{}) do
    form
    |> cast(attrs, [
      :customer_name,
      :product_name,
      :amount,
      :total_repayment,
      :repaid_amount,
      :remaining_balance,
      :status,
      :repayment_status,
      :account_type,
      :account_number,
      :from,
      :to
    ])
    |> validate_required([])
    |> validate_dates()
    |> validate_amounts()
  end

  defp validate_dates(changeset) do
    case {get_field(changeset, :from), get_field(changeset, :to)} do
      {from_date, to_date} when not is_nil(from_date) and not is_nil(to_date) ->
        if Date.compare(from_date, to_date) == :gt do
          add_error(changeset, :to, "must be greater than start date")
        else
          changeset
        end

      _ ->
        changeset
    end
  end

  defp validate_amounts(changeset) do
    validate_amount_fields(changeset, [
      :amount,
      :total_repayment,
      :repaid_amount,
      :remaining_balance
    ])
  end

  defp validate_amount_fields(changeset, []), do: changeset

  defp validate_amount_fields(changeset, [field | rest]) do
    case get_field(changeset, field) do
      nil ->
        validate_amount_fields(changeset, rest)

      amount ->
        if Decimal.compare(amount, Decimal.new(0)) == :lt do
          add_error(changeset, field, "must be greater than or equal to 0")
        else
          validate_amount_fields(changeset, rest)
        end
    end
  end
end
