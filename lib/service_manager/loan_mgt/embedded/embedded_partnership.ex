defmodule ServiceManager.LoanMgt.Embedded.EmbeddedPartnership do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key false
  embedded_schema do
    field :name, :string
    field :description, :string
    field :contact_person, :string
    field :contact_email, :string
    field :contact_phone, :string
    field :address, :string
    field :status, :string
    field :from, :date
    field :to, :date
  end

  def change_form(form \\ %__MODULE__{}, attrs \\ %{}), do: changeset(form, attrs)

  def changeset(form, attrs \\ %{}) do
    form
    |> cast(attrs, [
      :name,
      :description,
      :contact_person,
      :contact_email,
      :contact_phone,
      :address,
      :status,
      :from,
      :to
    ])
    |> validate_required([])
    |> validate_dates()
    |> validate_email()
    |> validate_phone()
  end

  defp validate_dates(changeset) do
    case {get_field(changeset, :from), get_field(changeset, :to)} do
      {from_date, to_date} when not is_nil(from_date) and not is_nil(to_date) ->
        if Date.compare(from_date, to_date) == :gt do
          add_error(changeset, :to, "must be greater than start date")
        else
          changeset
        end

      _ ->
        changeset
    end
  end

  defp validate_email(changeset) do
    case get_field(changeset, :contact_email) do
      nil ->
        changeset

      email ->
        if String.match?(email, ~r/^[^\s]+@[^\s]+\.[^\s]+$/) do
          changeset
        else
          add_error(changeset, :contact_email, "must be a valid email address")
        end
    end
  end

  defp validate_phone(changeset) do
    case get_field(changeset, :contact_phone) do
      nil ->
        changeset

      phone ->
        if String.match?(phone, ~r/^\+?[\d\s-]+$/) do
          changeset
        else
          add_error(changeset, :contact_phone, "must be a valid phone number")
        end
    end
  end
end
