defmodule ServiceManager.LoanMgt.Embedded.EmbeddedCharge do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key false
  embedded_schema do
    field :amount_from, :decimal
    field :amount_to, :decimal
    field :description, :string
    field :status, :string
    field :reference, :string
    field :payment_method, :string
    field :external_reference, :string
    field :debit_account, :string
    field :credit_account, :string
    field :start_date, :date
    field :end_date, :date
  end

  def change_form(%__MODULE__{} = form, attrs \\ %{}) do
    form
    |> cast(attrs, [
      :amount_from,
      :amount_to,
      :description,
      :status,
      :reference,
      :payment_method,
      :external_reference,
      :debit_account,
      :credit_account,
      :start_date,
      :end_date
    ])
    |> validate_dates()
    |> validate_amounts()
  end

  defp validate_dates(changeset) do
    start_date = get_field(changeset, :start_date)
    end_date = get_field(changeset, :end_date)

    if start_date && end_date && Date.compare(end_date, start_date) == :lt do
      add_error(changeset, :end_date, "must be greater than or equal to start date")
    else
      changeset
    end
  end

  defp validate_amounts(changeset) do
    amount_from = get_field(changeset, :amount_from)
    amount_to = get_field(changeset, :amount_to)

    if amount_from && amount_to && Decimal.cmp(amount_to, amount_from) == :lt do
      add_error(changeset, :amount_to, "must be greater than or equal to amount from")
    else
      changeset
    end
  end
end
