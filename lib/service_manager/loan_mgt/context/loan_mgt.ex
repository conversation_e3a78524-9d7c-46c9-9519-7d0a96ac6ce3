defmodule ServiceManager.Context.LoanMgt do
  import Ecto.Query, warn: false
  @pagination [page_size: 10]

  alias ServiceManager.Repo
  alias ServiceManager.LoanMgt.Schemas.Loan
  alias ServiceManager.LoanMgt.Schemas.Upload
  alias ServiceManager.LoanMgt.Schemas.Product
  alias ServiceManager.LoanMgt.Schemas.Terms
  alias ServiceManager.LoanMgt.Schemas.Customer
  alias ServiceManager.LoanMgt.Helpers.Utils
  alias ServiceManager.LoanMgt.Schemas.LoanCharges
  alias ServiceManager.LoanMgt.Schemas.Transactions
  alias ServiceManager.LoanMgt.Schemas.CustomerBatch
  alias ServiceManager.LoanMgt.Schemas.Partnerships
  alias ServiceManager.LoanMgt.Schemas.Application

  def change_upload(%Upload{} = upload, attrs \\ %{}) do
    upload
    |> Upload.changeset(attrs)
  end

  # ServiceManager.Context.LoanMgt.update_to_paid()
  # def update_to_paid() do
  #   Loan
  #   # |> where([l], l.status == "PAID")
  #   |> Repo.update_all(set: [status: "DISBURSED", repayment_status: "PENDING"])
  # end

  # ============== Loan ==============
  # ServiceManager.Context.LoanMgt.get_loan_by_id(15)
  def get_loan_by_id(id) do
    Loan
    |> where([l], l.id == ^id)
    |> Repo.one()
  end

  def customer_loans_pending_repayment(customer_id, loan_id) do
    Loan
    |> where(
      [l],
      l.customer_id == ^customer_id and l.id != ^loan_id and l.status == "DISBURSED" and
        l.repayment_status in ["PENDING", "PARTIAL"]
    )
    |> Repo.all()
  end

  # ServiceManager.Context.LoanMgt.get_loan_by_account_number(39, "*************")
  def get_loan_by_account_number(product_id, account_number) do
    Loan
    |> where([l], l.account_number == ^account_number and l.product_id == ^product_id)
    |> Repo.all()
  end

  # ServiceManager.Context.LoanMgt.loan_sum_by_product_id_and_account_number(39, "*************")
  def loan_sum_by_product_id_and_account_number(product_id, account_number) do
    Loan
    |> where([l], l.product_id == ^product_id and l.account_number == ^account_number)
    |> select([l], sum(l.amount))
    |> Repo.one()
  end

  def get_total_borrowed_by_account_number_and_product_id(eligible, product_id) do
    Loan
    |> where(
      [l],
      l.account_number == ^eligible.account_number and l.customer_id == ^eligible.id and
        l.product_id == ^product_id and l.status == "DISBURSED" and
        l.repayment_status in ["PENDING", "PARTIAL"]
    )
    |> select([l], sum(l.amount))
    |> Repo.one()
    |> case do
      nil -> Decimal.new(0)
      amount -> amount
    end
  end

  def active_loans_by_product_and_acc_num(product_id, account_number) do
    Loan
    |> where(
      [l],
      l.account_number == ^account_number and l.product_id == ^product_id and
        l.status == "DISBURSED" and l.repayment_status in ["PENDING", "PARTIAL"]
    )
    |> Repo.all()
  end

  def get_user_loans(user_id) do
    Loan
    |> where([l], l.user_id == ^user_id)
    |> Repo.all()
  end

  def get_pending_loans(user_id) do
    Loan
    |> where(
      [l],
      l.user_id == ^user_id and l.status == "DISBURSED" and
        l.repayment_status in ["PENDING", "PARTIAL"]
    )
    |> Repo.all()
  end

  def get_loan_by_id(user_id, loan_id) do
    Loan
    |> where([l], l.id == ^loan_id and l.user_id == ^user_id)
    |> preload(:bank_account)
    |> Repo.one()
    |> case do
      nil -> {:error, "Loan not found"}
      loan -> {:ok, loan}
    end
  end

  def get_loans(search_params) do
    Loan
    |> join(:left, [a], b in "accounts_users", on: a.user_id == b.id)
    |> join(:left, [a, b], c in Product, on: a.product_id == c.id)
    |> handle_loan_filter(search_params)
    |> order_by(desc: :inserted_at)
    |> compose_loan_select()
    |> select_merge([a, b, c], %{
      customer_name: fragment("CONCAT(?, ' ', ?)", b.first_name, b.last_name),
      product_name: c.name
    })
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, search_params))
  end

  defp handle_loan_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        loan_isearch_filter(query, Utils.sanitize_term(value))

      {"account_number", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, _c],
          fragment("lower(?) LIKE lower(?)", a.account_number, ^Utils.sanitize_term(value))
        )

      {"amount", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, _c],
          fragment("CAST(? AS DECIMAL) = ?", a.amount, ^Utils.sanitize_term(value))
        )

      {"total_repayment", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, _c],
          fragment("CAST(? AS DECIMAL) = ?", a.total_repayment, ^Utils.sanitize_term(value))
        )

      {"status", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, _c],
          fragment("lower(?) LIKE lower(?)", a.status, ^Utils.sanitize_term(value))
        )

      {"repayment_status", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, _c],
          fragment("lower(?) LIKE lower(?)", a.repayment_status, ^Utils.sanitize_term(value))
        )

      {"account_type", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, _c],
          fragment("lower(?) LIKE lower(?)", a.account_type, ^Utils.sanitize_term(value))
        )

      {"customer_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [_a, b, _c],
          fragment(
            "lower(CONCAT(?, ' ', ?)) LIKE lower(?)",
            b.first_name,
            b.last_name,
            ^Utils.sanitize_term(value)
          )
        )

      {"product_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [_a, _b, c],
          fragment("lower(?) LIKE lower(?)", c.name, ^Utils.sanitize_term(value))
        )

      {"from", value}, query when byte_size(value) > 0 ->
        {:ok, value} = Date.from_iso8601(value)
        where(query, [a, _b, _c], fragment("CAST(? AS DATE) >= ?", a.inserted_at, ^value))

      {"to", value}, query when byte_size(value) > 0 ->
        {:ok, value} = Date.from_iso8601(value)
        where(query, [a, _b, _c], fragment("CAST(? AS DATE) <= ?", a.inserted_at, ^value))

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp loan_isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("CAST(? AS TEXT) LIKE lower(?)", a.amount, ^search_term) or
        fragment("CAST(? AS TEXT) LIKE lower(?)", a.total_repayment, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.account_number, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.status, ^search_term) or
        fragment(
          "lower(CONCAT(?, ' ', ?)) LIKE lower(?)",
          b.first_name,
          b.last_name,
          ^search_term
        ) or
        fragment("lower(?) LIKE lower(?)", c.name, ^search_term)
    )
  end

  defp compose_loan_select(query) do
    query
    |> select(
      [a, b, c],
      map(a, [
        :id,
        :amount,
        :charge_amount,
        :disbursed_amount,
        :interest_rate,
        :duration,
        :total_repayment,
        :repaid_amount,
        :remaining_balance,
        :status,
        :repayment_status,
        :account_type,
        :account_number,
        :user_id,
        :bank_account_id,
        :product_id,
        :inserted_at,
        :updated_at
      ])
    )
  end

  def get_active_loan(user_id) do
    Loan
    |> where([l], l.user_id == ^user_id and l.status == "active")
    |> preload(:bank_account)
    |> Repo.one()
  end

  def list_loans do
    Loan
    |> preload([:customer, :product])
    |> Repo.all()
  end

  def create_loan(attrs) do
    %Loan{}
    |> Loan.changeset(attrs)
    |> Repo.insert()
  end

  def get_loan(user_id, loan_id) do
    Loan
    |> where([l], l.id == ^loan_id and l.user_id == ^user_id)
    |> preload(:bank_account)
    |> Repo.one()
  end

  # ============== Loan Product ==============

  def get_loan_product!(id), do: Repo.get!(Product, id)

  def list_loan_products(search_params) do
    Product
    |> handle_product_filter(search_params)
    |> order_by(desc: :inserted_at)
    |> compose_product_select()
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, search_params))
  end

  defp handle_product_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        product_isearch_filter(query, Utils.sanitize_term(value))

      {"name", value}, query when byte_size(value) > 0 ->
        where(query, [p], fragment("lower(?) LIKE lower(?)", p.name, ^Utils.sanitize_term(value)))

      {"loan_account", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [p],
          fragment("lower(?) LIKE lower(?)", p.loan_account, ^Utils.sanitize_term(value))
        )

      {"collection_account", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [p],
          fragment("lower(?) LIKE lower(?)", p.collection_account, ^Utils.sanitize_term(value))
        )

      {"description", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [p],
          fragment("lower(?) LIKE lower(?)", p.description, ^Utils.sanitize_term(value))
        )

      {"min_amount", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [p],
          fragment("CAST(? AS DECIMAL) = ?", p.min_amount, ^Utils.sanitize_term(value))
        )

      {"max_amount", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [p],
          fragment("CAST(? AS DECIMAL) = ?", p.max_amount, ^Utils.sanitize_term(value))
        )

      {"interest_rate", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [p],
          fragment("CAST(? AS DECIMAL) = ?", p.interest_rate, ^Utils.sanitize_term(value))
        )

      {_, _}, query ->
        query
    end)
  end

  defp product_isearch_filter(query, search_term) do
    where(
      query,
      [p],
      fragment("lower(?) LIKE lower(?)", p.name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", p.description, ^search_term) or
        fragment("lower(?) LIKE lower(?)", p.loan_account, ^search_term) or
        fragment("lower(?) LIKE lower(?)", p.collection_account, ^search_term) or
        fragment("CAST(? AS TEXT) LIKE lower(?)", p.min_amount, ^search_term) or
        fragment("CAST(? AS TEXT) LIKE lower(?)", p.max_amount, ^search_term) or
        fragment("CAST(? AS TEXT) LIKE lower(?)", p.interest_rate, ^search_term)
    )
  end

  defp compose_product_select(query) do
    query
    |> select(
      [p],
      map(p, [
        :id,
        :name,
        :charge_type,
        :charge_rate,
        :description,
        :min_amount,
        :max_amount,
        :interest_rate,
        :default_duration,
        :is_active,
        :loan_account,
        :charge_account,
        :collection_account,
        :maker_id,
        :checker_id,
        :inserted_at,
        :updated_at
      ])
    )
  end

  def get_loan_products() do
    Product
    |> where([p], p.is_active == true)
    |> Repo.all()
  end

  def get_qualifying_loan_products(user) do
    Product
    |> join(:left, [p], c in Customer, on: p.id == c.product_id)
    |> where(
      [p, c],
      p.is_active == true and c.account_number == ^user.account_number
    )
    |> select([p, c], %{
      id: p.id,
      name: p.name,
      charge_rate: p.charge_rate,
      charge_type: p.charge_type,
      description: p.description,
      min_amount: p.min_amount,
      max_amount: p.max_amount,
      interest_rate: p.interest_rate,
      default_duration: p.default_duration,
      is_active: p.is_active,
      loan_account: p.loan_account,
      collection_account: p.collection_account,
      maker_id: p.maker_id,
      checker_id: p.checker_id,
      inserted_at: p.inserted_at,
      updated_at: p.updated_at,
      is_eligible: c.eligibility_status == "ELIGIBLE"
    })
    |> Repo.all()
  end

  def get_loan_product(product_id) do
    case Repo.get(Product, product_id) do
      nil -> {:error, "Loan product not found"}
      product -> {:ok, product}
    end
  end

  def deactivate_all_loan_products do
    from(p in Product, update: [set: [is_active: false]])
    |> Repo.update_all([])
  end

  def change_loan_product(%Product{} = loan_products, attrs \\ %{}) do
    Product.changeset(loan_products, attrs)
  end

  # ============== Loan Terms ==============
  # ServiceManager.Context.LoanMgt.get_terms()
  def get_terms() do
    Terms
    |> Repo.all()
  end

  def get_user_loan_terms(user_id, product_id) do
    Terms
    |> where([t], t.customer_id == ^user_id and t.product_id == ^product_id)
    |> Repo.one()
  end

  def calculate_loan_terms(user_id, loan_product, amount) do
    with {:ok, charge_amount} <- calculate_charge_amount(amount, loan_product) do
      disbursed_amount = Decimal.sub(amount, charge_amount)

      terms = %Terms{
        product_id: loan_product.id,
        amount: amount,
        customer_id: user_id,
        charge_amount: charge_amount,
        disbursed_amount: disbursed_amount,
        interest_rate: charge_amount,
        duration: loan_product.default_duration,
        total_repayment: amount
      }

      {:ok, Repo.insert!(terms)}
    else
      _ -> {:error, "Error calculating loan terms"}
    end
  end

  defp calculate_charge_amount(amount, product) do
    charge_amount =
      case product.charge_type do
        "PERCENTAGE" ->
          percentage_rate = Decimal.div(product.charge_rate, Decimal.new(100))
          Decimal.mult(amount, percentage_rate)

        "ACTUAL" ->
          product.charge_rate

        "NONE" ->
          Decimal.new(0)

        _ ->
          {:error, "Invalid charge type, please contact support"}
      end

    case charge_amount do
      {:error, reason} -> {:error, reason}
      amount -> {:ok, amount}
    end
  end

  def get_loan_terms(loan_terms_id) do
    case Repo.get(Terms, loan_terms_id) do
      nil -> {:error, "Agree to Terms of this Product."}
      terms -> {:ok, terms}
    end
  end

  # ============= Loan Application ==============

  def get_loan_applications(search_params) do
    Application
    |> join(:left, [a], b in "accounts_users", on: a.user_id == b.id)
    |> join(:left, [a, b], c in Product, on: a.product_id == c.id)
    |> handle_loan_application_filter(search_params)
    |> order_by(desc: :inserted_at)
    |> compose_loan_application_select()
    |> select_merge([a, b, c], %{
      customer_name: fragment("CONCAT(?, ' ', ?)", b.first_name, b.last_name),
      product_name: c.name
    })
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, search_params))
  end

  defp handle_loan_application_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        loan_application_isearch_filter(query, Utils.sanitize_term(value))

      {"amount", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("CAST(? AS DECIMAL) = ?", a.amount, ^Utils.sanitize_term(value))
        )

      {"status", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.status, ^Utils.sanitize_term(value))
        )

      {"from", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("CAST(? AS DATE) >= ?", a.inserted_at, ^value))

      {"to", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("CAST(? AS DATE) <= ?", a.inserted_at, ^value))

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp loan_application_isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("CAST(? AS TEXT) LIKE lower(?)", a.amount, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.status, ^search_term)
    )
  end

  defp compose_loan_application_select(query) do
    query
    |> select(
      [a, b, c],
      map(a, [
        :id,
        :duration,
        :amount,
        :status,
        :inserted_at,
        :updated_at
      ])
    )
  end

  def get_loan_application(user_id, loan_application_id) do
    Application
    |> where([a], a.id == ^loan_application_id and a.user_id == ^user_id)
    |> Repo.one()
  end

  def create_loan_application(attrs) do
    %Application{}
    |> Application.changeset(attrs)
    |> Repo.insert()
  end

  # Helper functions
  defp calculate_total_repayment(amount, interest_rate, duration) do
    interest = Decimal.mult(amount, Decimal.div(interest_rate, Decimal.new(100)))
    total_interest = Decimal.mult(interest, Decimal.new(duration))
    Decimal.add(amount, total_interest)
  end

  def list_loan_applications do
    Application
    |> preload(:loan_product)
    |> Repo.all()
  end

  # =================== Loan Customer =================
  # ServiceManager.Context.LoanMgt.update_all_if_loan_is_paid()
  def update_all_if_loan_is_paid do
    Loan
    |> where([l], l.status == "DISBURSED" and l.repayment_status == "COMPLETED")
    |> Repo.all()
  end

  # ServiceManager.Context.LoanMgt.get_eligible_customers()
  def get_eligible_customers do
    Customer
    |> where([lc], lc.eligibility_status == "ELIGIBLE")
    |> Repo.all()
  end

  # ServiceManager.Context.LoanMgt.update_customer()
  def update_customer() do
    Customer
    |> where([l], l.id == 4)
    |> Repo.update_all(set: [eligibility_status: "ELIGIBLE"])
  end

  def get_loan_customer_by_product_id_and_partner_id(account_number, product_id, partner_id) do
    Customer
    |> where(
      [lc],
      lc.account_number == ^account_number and
        lc.product_id == ^product_id and lc.partner_id == ^partner_id and
        lc.eligibility_status == "ELIGIBLE"
    )
    |> limit(1)
    |> Repo.one()
  end

  def eligible_by_product_and_acc_num(product_id, account_number) do
    Customer
    |> where(
      [lc],
      lc.account_number == ^account_number and lc.product_id == ^product_id
      # and lc.eligibility_status == "ELIGIBLE"
    )
    |> order_by(asc: :id)
    |> limit(1)
    |> Repo.one()
  end

  def get_customer_eligibility(id) do
    case Repo.get(Customer, id) do
      nil -> {:error, "Your account is not eligible for a loan at this time"}
      eligible -> {:ok, eligible}
    end
  end

  # ServiceManager.Context.LoanMgt.list_loan_customers()
  def list_loan_customers do
    Repo.all(Customer)
  end

  def get_loan_customer!(id), do: Repo.get!(Customer, id)

  def create_loan_customer(attrs \\ %{}) do
    %Customer{}
    |> Customer.changeset(attrs)
    |> Repo.insert()
  end

  def update_loan_customer(%Customer{} = loan_customer, attrs) do
    loan_customer
    |> Customer.changeset(attrs)
    |> Repo.update()
  end

  def delete_loan_customer(%Customer{} = loan_customer) do
    Repo.delete(loan_customer)
  end

  def change_loan_customer(%Customer{} = loan_customer, attrs \\ %{}) do
    Customer.changeset(loan_customer, attrs)
  end

  def get_loan_customer_by_customer_and_product(customer_id, product_id) do
    Repo.get_by(Customer, customer_id: customer_id, product_id: product_id)
  end

  def list_loan_customers_by_product(product_id) do
    Customer
    |> where([lc], lc.product_id == ^product_id)
    |> Repo.all()
  end

  def list_eligible_loan_customers_by_product(product_id) do
    Customer
    |> where([lc], lc.product_id == ^product_id and lc.eligibility_status == "eligible")
    |> Repo.all()
  end

  def list_loan_customers_with_products do
    Customer
    |> preload(:product)
    |> Repo.all()
  end

  def get_loan_customers_by_batch_reference(batch_reference, search_params) do
    Customer
    |> join(:left, [a], b in "accounts_users", on: a.customer_id == b.id)
    |> join(:left, [a, b], c in "loan_products", on: a.product_id == c.id)
    |> where([a, _b, _C], a.ref_id == ^batch_reference)
    |> handle_loan_customer_filter(search_params)
    |> order_by(desc: :inserted_at)
    |> compose_loan_customer_select()
    |> select_merge([_a, b, c], %{
      name: fragment("CONCAT(?, ' ', ?)", b.first_name, b.last_name),
      email: b.email,
      phone_number: b.phone_number
    })
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, search_params))
  end

  def get_loan_customers(search_params) do
    Customer
    |> join(:left, [a], b in "accounts_users", on: a.customer_id == b.id)
    |> join(:left, [a, b], c in "loan_products", on: a.product_id == c.id)
    |> handle_loan_customer_filter(search_params)
    |> order_by(desc: :inserted_at)
    |> compose_loan_customer_select()
    |> select_merge([_a, b, c], %{
      name: fragment("CONCAT(?, ' ', ?)", b.first_name, b.last_name),
      email: b.email,
      phone_number: b.phone_number,
      product_name: c.name
    })
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, search_params))
  end

  defp handle_loan_customer_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        loan_customer_isearch_filter(query, Utils.sanitize_term(value))

      {"max_loan_amount", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, _c],
          fragment("CAST(? AS DECIMAL) = ?", a.max_loan_amount, ^Utils.sanitize_term(value))
        )

      {"account_number", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, _c],
          fragment("lower(?) LIKE lower(?)", a.account_number, ^Utils.sanitize_term(value))
        )

      {"account_type", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, _c],
          fragment("lower(?) LIKE lower(?)", a.account_type, ^Utils.sanitize_term(value))
        )

      {"eligibility_status", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, _c],
          fragment("lower(?) LIKE lower(?)", a.eligibility_status, ^Utils.sanitize_term(value))
        )

      {"customer_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [_a, b, _c],
          fragment(
            "lower(CONCAT(?, ' ', ?)) LIKE lower(?)",
            b.first_name,
            b.last_name,
            ^Utils.sanitize_term(value)
          )
        )

      {"product_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [_a, _b, c],
          fragment("lower(?) LIKE lower(?)", c.name, ^Utils.sanitize_term(value))
        )

      {"from", value}, query when byte_size(value) > 0 ->
        where(query, [a, _b, _c], fragment("CAST(? AS DATE) >= ?", a.inserted_at, ^value))

      {"to", value}, query when byte_size(value) > 0 ->
        where(query, [a, _b, _c], fragment("CAST(? AS DATE) <= ?", a.inserted_at, ^value))

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp loan_customer_isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", a.account_number, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.eligibility_status, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.account_type, ^search_term) or
        fragment(
          "lower(CONCAT(?, ' ', ?)) LIKE lower(?)",
          b.first_name,
          b.last_name,
          ^search_term
        ) or
        fragment("lower(?) LIKE lower(?)", b.email, ^search_term) or
        fragment("lower(?) LIKE lower(?)", c.name, ^search_term)
    )
  end

  defp compose_loan_customer_select(query) do
    query
    |> select(
      [u, r],
      map(u, [
        :id,
        :ref_id,
        :eligibility_status,
        :max_loan_amount,
        :account_number,
        :account_type,
        :last_eligibility_check,
        :maker_id,
        :checker_id,
        :inserted_at,
        :updated_at
      ])
    )
  end

  # =========== TEST DATA ===========
  # ServiceManager.LoanMgt.Context.insert_loan_product()
  def insert_loan_product do
    attrs = %{
      name: "Quick Loan",
      description: "Short-term loan for emergencies",
      min_amount: Decimal.new("100.00"),
      max_amount: Decimal.new("10000.00"),
      interest_rate: Decimal.new("0.05"),
      default_duration: 30,
      is_active: true,
      maker_id: 1,
      checker_id: 1
    }

    %Product{}
    |> Product.changeset(attrs)
    |> Repo.insert()
  end

  # ServiceManager.LoanMgt.Context.insert_loan_customers()
  def insert_loan_customers() do
    attrs = %{
      ref_id: "**********",
      eligibility_status: "ELIGIBLE",
      max_loan_amount: Decimal.new("10000.00"),
      account_number: "FDH656603",
      account_type: "WALLET",
      last_eligibility_check: DateTime.utc_now(),
      maker_id: 1,
      checker_id: 1,
      customer_id: 2,
      product_id: 1
    }

    %Customer{}
    |> Customer.changeset(attrs)
    |> Repo.insert()
  end

  # ServiceManager.LoanMgt.Context.insert_loan_terms()
  def insert_loan_terms do
    {:ok, product} = insert_loan_product()

    attrs = %{
      amount: Decimal.new("5000.00"),
      interest_rate: Decimal.new("0.08"),
      duration: 60,
      total_repayment: Decimal.new("5400.00"),
      product_id: product.id,
      maker_id: 1,
      checker_id: 1
    }

    %Terms{}
    |> Terms.changeset(attrs)
    |> Repo.insert()
  end

  # ============== LOAN PARTNERSHIPS ==============

  def list_loan_partnerships(search_params) do
    Partnerships
    |> handle_loan_partnership_filter(search_params)
    |> order_by(desc: :inserted_at)
    |> compose_loan_partnership_select()
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, search_params))
  end

  defp handle_loan_partnership_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        loan_partnership_isearch_filter(query, Utils.sanitize_term(value))

      {"name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [lp],
          fragment("lower(?) LIKE lower(?)", lp.name, ^Utils.sanitize_term(value))
        )

      {"description", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [lp],
          fragment("lower(?) LIKE lower(?)", lp.description, ^Utils.sanitize_term(value))
        )

      {"contact_person", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [lp],
          fragment("lower(?) LIKE lower(?)", lp.contact_person, ^Utils.sanitize_term(value))
        )

      {"contact_email", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [lp],
          fragment("lower(?) LIKE lower(?)", lp.contact_email, ^Utils.sanitize_term(value))
        )

      {"contact_phone", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [lp],
          fragment("lower(?) LIKE lower(?)", lp.contact_phone, ^Utils.sanitize_term(value))
        )

      {"partner_type", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [lp],
          fragment("lower(?) LIKE lower(?)", lp.partner_type, ^Utils.sanitize_term(value))
        )

      {"status", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [lp],
          fragment("lower(?) LIKE lower(?)", lp.status, ^Utils.sanitize_term(value))
        )

      {"from", value}, query when byte_size(value) > 0 ->
        where(query, [lp], fragment("CAST(? AS DATE) >= ?", lp.partnership_start_date, ^value))

      {"to", value}, query when byte_size(value) > 0 ->
        where(query, [lp], fragment("CAST(? AS DATE) <= ?", lp.partnership_start_date, ^value))

      {_, _}, query ->
        query
    end)
  end

  defp loan_partnership_isearch_filter(query, search_term) do
    where(
      query,
      [lp],
      fragment("lower(?) LIKE lower(?)", lp.name, ^search_term) or
        fragment("lower(?) LIKE lower(?)", lp.description, ^search_term) or
        fragment("lower(?) LIKE lower(?)", lp.status, ^search_term) or
        fragment("lower(?) LIKE lower(?)", lp.partner_type, ^search_term) or
        fragment("lower(?) LIKE lower(?)", lp.contact_person, ^search_term) or
        fragment("lower(?) LIKE lower(?)", lp.contact_email, ^search_term) or
        fragment("lower(?) LIKE lower(?)", lp.contact_phone, ^search_term)
    )
  end

  defp compose_loan_partnership_select(query) do
    query
    |> select(
      [lp],
      map(lp, [
        :id,
        :name,
        :description,
        :partner_type,
        :contact_person,
        :contact_email,
        :contact_phone,
        :partnership_start_date,
        :partnership_end_date,
        :status,
        :inserted_at,
        :updated_at
      ])
    )
  end

  def get_loan_partnerships() do
    Partnerships
    |> where([a], a.status == "ACTIVE")
    |> Repo.all()
  end

  def list_loan_partnerships do
    Repo.all(Partnerships)
  end

  def get_loan_partnerships!(id), do: Repo.get!(Partnerships, id)

  def create_loan_partnerships(attrs \\ %{}) do
    %Partnerships{}
    |> Partnerships.changeset(attrs)
    |> Repo.insert()
  end

  def update_loan_partnerships(%Partnerships{} = loan_partnerships, attrs) do
    loan_partnerships
    |> Partnerships.changeset(attrs)
    |> Repo.update()
  end

  def delete_loan_partnerships(%Partnerships{} = loan_partnerships) do
    Repo.delete(loan_partnerships)
  end

  def change_loan_partnerships(%Partnerships{} = loan_partnerships, attrs \\ %{}) do
    Partnerships.changeset(loan_partnerships, attrs)
  end

  # ================ LOAN CUSTOMER BATCH ===================

  def get_loan_customer_batches(search_params) do
    CustomerBatch
    |> join(:left, [a], b in "accounts_users", on: a.maker_id == b.id)
    |> join(:left, [a, _b, c], c in "loan_partnerships", on: a.partner_id == c.id)
    |> handle_loan_customer_batch_filter(search_params)
    |> order_by(desc: :inserted_at)
    |> compose_loan_customer_batch_select()
    |> select_merge([_a, b, c], %{
      maker_name: fragment("CONCAT(?, ' ', ?)", b.first_name, b.last_name),
      partner_name: c.name
    })
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, search_params))
  end

  defp handle_loan_customer_batch_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        loan_customer_batch_isearch_filter(query, Utils.sanitize_term(value))

      {"batch_reference", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.batch_reference, ^Utils.sanitize_term(value))
        )

      {"filename", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.filename, ^Utils.sanitize_term(value))
        )

      {"status", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.status, ^Utils.sanitize_term(value))
        )

      {"valid_count", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("CAST(? AS INTEGER) = ?", a.valid_count, ^Utils.sanitize_term(value))
        )

      {"invalid_count", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("CAST(? AS INTEGER) = ?", a.invalid_count, ^Utils.sanitize_term(value))
        )

      {"item_count", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("CAST(? AS INTEGER) = ?", a.item_count, ^Utils.sanitize_term(value))
        )

      {"partner_name", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b, c],
          fragment("lower(?) LIKE lower(?)", c.name, ^Utils.sanitize_term(value))
        )

      {"from", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("CAST(? AS DATE) >= ?", a.inserted_at, ^value))

      {"to", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("CAST(? AS DATE) <= ?", a.inserted_at, ^value))

      {"from", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("CAST(? AS DATE) >= ?", a.inserted_at, ^value))

      {"to", value}, query when byte_size(value) > 0 ->
        where(query, [a], fragment("CAST(? AS DATE) <= ?", a.inserted_at, ^value))

      {_, _}, query ->
        query
    end)
  end

  defp loan_customer_batch_isearch_filter(query, search_term) do
    where(
      query,
      [a, b, c],
      fragment("lower(?) LIKE lower(?)", a.batch_reference, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.filename, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.status, ^search_term) or
        fragment("CAST(? AS TEXT) LIKE lower(?)", a.valid_count, ^search_term) or
        fragment("CAST(? AS TEXT) LIKE lower(?)", a.invalid_count, ^search_term) or
        fragment("CAST(? AS TEXT) LIKE lower(?)", a.item_count, ^search_term) or
        fragment("lower(?) LIKE lower(?)", c.name, ^search_term)
    )
  end

  defp compose_loan_customer_batch_select(query) do
    query
    |> select(
      [a, _b, _c],
      map(a, [
        :id,
        :batch_reference,
        :filename,
        :status,
        :valid_count,
        :invalid_count,
        :item_count,
        :maker_id,
        :checker_id,
        :partner_id,
        :inserted_at,
        :updated_at
      ])
    )
  end

  def list_loan_customer_batch do
    Repo.all(CustomerBatch)
  end

  def get_loan_customer_batch!(id), do: Repo.get!(CustomerBatch, id)

  def create_loan_customer_batch(attrs \\ %{}) do
    %CustomerBatch{}
    |> CustomerBatch.changeset(attrs)
    |> Repo.insert()
  end

  def update_loan_customer_batch(%CustomerBatch{} = loan_customer_batch, attrs) do
    loan_customer_batch
    |> CustomerBatch.changeset(attrs)
    |> Repo.update()
  end

  def delete_loan_customer_batch(%CustomerBatch{} = loan_customer_batch) do
    Repo.delete(loan_customer_batch)
  end

  def change_loan_customer_batch(%CustomerBatch{} = loan_customer_batch, attrs \\ %{}) do
    CustomerBatch.changeset(loan_customer_batch, attrs)
  end

  # ============= TRANSACTIONS ================
  def get_loan_transactions(search_params) do
    Transactions
    |> join(:left, [a], b in Loan, on: a.loan_id == b.id)
    |> handle_loan_transaction_filter(search_params)
    |> order_by(desc: :inserted_at)
    |> compose_loan_transaction_select()
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, search_params))
  end

  defp handle_loan_transaction_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        loan_transaction_isearch_filter(query, Utils.sanitize_term(value))

      {"type", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b],
          fragment("lower(?) LIKE lower(?)", a.type, ^Utils.sanitize_term(value))
        )

      {"amount", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b],
          fragment("CAST(? AS DECIMAL) = ?", a.amount, ^Utils.sanitize_term(value))
        )

      {"status", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b],
          fragment("lower(?) LIKE lower(?)", a.status, ^Utils.sanitize_term(value))
        )

      {"reference", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b],
          fragment("lower(?) LIKE lower(?)", a.reference, ^Utils.sanitize_term(value))
        )

      {"value_date", value}, query when byte_size(value) > 0 ->
        where(query, [a, _b], fragment("CAST(? AS DATE) = ?", a.value_date, ^value))

      {"transaction_date", value}, query when byte_size(value) > 0 ->
        where(query, [a, _b], fragment("CAST(? AS DATE) = ?", a.transaction_date, ^value))

      {"payment_method", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b],
          fragment("lower(?) LIKE lower(?)", a.payment_method, ^Utils.sanitize_term(value))
        )

      {"external_reference", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b],
          fragment("lower(?) LIKE lower(?)", a.external_reference, ^Utils.sanitize_term(value))
        )

      {"debit_account", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b],
          fragment("lower(?) LIKE lower(?)", a.debit_account, ^Utils.sanitize_term(value))
        )

      {"credit_account", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b],
          fragment("lower(?) LIKE lower(?)", a.credit_account, ^Utils.sanitize_term(value))
        )

      {"from", value}, query when byte_size(value) > 0 ->
        where(query, [a, _b], fragment("CAST(? AS DATE) >= ?", a.transaction_date, ^value))

      {"to", value}, query when byte_size(value) > 0 ->
        where(query, [a, _b], fragment("CAST(? AS DATE) <= ?", a.transaction_date, ^value))

      {_, _}, query ->
        query
    end)
  end

  defp loan_transaction_isearch_filter(query, search_term) do
    where(
      query,
      [a, _b],
      fragment("CAST(? AS TEXT) LIKE lower(?)", a.amount, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.type, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.status, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.reference, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.payment_method, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.external_reference, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.debit_account, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.credit_account, ^search_term)
    )
  end

  defp compose_loan_transaction_select(query) do
    query
    |> select(
      [a, b],
      map(a, [
        :id,
        :type,
        :amount,
        :opening_balance,
        :closing_balance,
        :description,
        :status,
        :reference,
        :value_date,
        :transaction_date,
        :payment_method,
        :external_reference,
        :debit_account,
        :credit_account,
        :loan_id,
        :inserted_at,
        :updated_at
      ])
    )
  end

  def list_loan_transactions do
    Repo.all(Transactions)
  end

  def get_transactions!(id), do: Repo.get!(Transactions, id)

  def create_transactions(attrs \\ %{}) do
    %Transactions{}
    |> Transactions.changeset(attrs)
    |> Repo.insert()
  end

  def update_transactions(%Transactions{} = transactions, attrs) do
    transactions
    |> Transactions.changeset(attrs)
    |> Repo.update()
  end

  def delete_transactions(%Transactions{} = transactions) do
    Repo.delete(transactions)
  end

  def change_transactions(%Transactions{} = transactions, attrs \\ %{}) do
    Transactions.changeset(transactions, attrs)
  end

  # ============= CHARGES ================
  def get_loan_charges(search_params) do
    LoanCharges
    |> join(:left, [a], b in Loan, on: a.loan_id == b.id)
    |> handle_loan_charge_filter(search_params)
    |> order_by(desc: :inserted_at)
    |> compose_loan_charge_select()
    |> Scrivener.paginate(Scrivener.Config.new(Repo, @pagination, search_params))
  end

  defp handle_loan_charge_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        loan_charge_isearch_filter(query, Utils.sanitize_term(value))

      {"charge_amount", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a, _b],
          fragment("CAST(? AS DECIMAL) = ?", a.charge_amount, ^Utils.sanitize_term(value))
        )

      {"loan_amount", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [_a, b],
          fragment("CAST(? AS DECIMAL) = ?", b.amount, ^Utils.sanitize_term(value))
        )

      {"debit_account", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [_a, b],
          fragment("lower(?) LIKE lower(?)", b.debit_account, ^Utils.sanitize_term(value))
        )

      {"credit_account", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [_a, b],
          fragment("lower(?) LIKE lower(?)", b.credit_account, ^Utils.sanitize_term(value))
        )

      {"from", value}, query when byte_size(value) > 0 ->
        where(query, [a, _b], fragment("CAST(? AS DATE) >= ?", a.inserted_at, ^value))

      {"to", value}, query when byte_size(value) > 0 ->
        where(query, [a, _b], fragment("CAST(? AS DATE) <= ?", a.inserted_at, ^value))

      {_, _}, query ->
        query
    end)
  end

  defp loan_charge_isearch_filter(query, search_term) do
    where(
      query,
      [a, b],
      fragment("lower(?) LIKE lower(?)", a.charge_type, ^search_term) or
        fragment("CAST(? AS TEXT) LIKE lower(?)", a.charge_amount, ^search_term) or
        fragment("CAST(? AS TEXT) LIKE lower(?)", b.amount, ^search_term) or
        fragment("lower(?) LIKE lower(?)", b.debit_account, ^search_term) or
        fragment("lower(?) LIKE lower(?)", b.credit_account, ^search_term)
    )
  end

  defp compose_loan_charge_select(query) do
    query
    |> select(
      [a, b],
      map(a, [
        :id,
        :amount,
        :description,
        :status,
        :status_description,
        :reference,
        :value_date,
        :transaction_date,
        :payment_method,
        :external_reference,
        :debit_account,
        :credit_account,
        :loan_id,
        :product_id,
        :inserted_at,
        :updated_at
      ])
    )
  end

  def list_loan_charges do
    Repo.all(LoanCharges)
  end

  def get_loan_charge!(id), do: Repo.get!(LoanCharges, id)

  def create_loan_charge(attrs \\ %{}) do
    %LoanCharges{}
    |> LoanCharges.changeset(attrs)
    |> Repo.insert()
  end

  def update_loan_charge(%LoanCharges{} = loan_charge, attrs) do
    loan_charge
    |> LoanCharges.changeset(attrs)
    |> Repo.update()
  end

  def delete_loan_charge(%LoanCharges{} = loan_charge) do
    Repo.delete(loan_charge)
  end

  def change_loan_charge(%LoanCharges{} = loan_charge, attrs \\ %{}) do
    LoanCharges.changeset(loan_charge, attrs)
  end
end
