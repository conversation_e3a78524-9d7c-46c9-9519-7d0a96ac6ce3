defmodule ServiceManager.MCP.Resources do
  @moduledoc """
  MCP Resources module for managing dynamic forms system resources.
  
  Provides resource discovery and read operations for:
  - Dynamic Routes
  - Dynamic Forms
  - Dynamic Plugins (Processes)
  - Entity Connections/Links
  """
  
  use GenServer
  require Logger
  
  alias ServiceManager.Routing.{DynamicRoute, DynamicRouteManager}
  alias ServiceManager.Forms.{DynamicForm, DynamicFormsManager}
  alias ServiceManager.Schemas.Dynamic.Processes.{DynamicProcess, ProcessManager}
  
  # GenServer API
  
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end
  
  # Client API
  
  @doc """
  List all available resources
  """
  def list_all() do
    GenServer.call(__MODULE__, :list_all)
  end
  
  @doc """
  Read a specific resource by URI
  """
  def read_resource(uri) do
    GenServer.call(__MODULE__, {:read_resource, uri})
  end
  
  @doc """
  Subscribe to resource changes
  """
  def subscribe_to_changes() do
    GenServer.call(__MODULE__, :subscribe_to_changes)
  end
  
  # GenServer Callbacks
  
  @impl true
  def init(_opts) do
    Logger.info("Starting MCP Resources manager")
    {:ok, %{subscribers: []}}
  end
  
  @impl true
  def handle_call(:list_all, _from, state) do
    resources = [
      %{
        uri: "mcp://dynamic-forms/routes",
        name: "Dynamic Routes",
        description: "All dynamic API routes with metadata",
        mimeType: "application/json"
      },
      %{
        uri: "mcp://dynamic-forms/forms", 
        name: "Dynamic Forms",
        description: "All dynamic forms with validation schemas",
        mimeType: "application/json"
      },
      %{
        uri: "mcp://dynamic-forms/plugins",
        name: "Dynamic Plugins",
        description: "All dynamic processes/plugins with code and metadata",
        mimeType: "application/json"
      },
      %{
        uri: "mcp://dynamic-forms/connections",
        name: "Entity Connections",
        description: "All relationships between routes, forms, and plugins",
        mimeType: "application/json"
      },
      %{
        uri: "mcp://dynamic-forms/stats",
        name: "System Statistics",
        description: "Usage statistics and analytics for the dynamic forms system",
        mimeType: "application/json"
      }
    ]
    
    {:reply, {:ok, resources}, state}
  end
  
  @impl true
  def handle_call({:read_resource, uri}, _from, state) do
    result = case parse_resource_uri(uri) do
      {:routes, filters} -> read_routes_resource(filters)
      {:forms, filters} -> read_forms_resource(filters)
      {:plugins, filters} -> read_plugins_resource(filters)
      {:connections, filters} -> read_connections_resource(filters)
      {:stats, filters} -> read_stats_resource(filters)
      {:error, reason} -> {:error, %{code: "invalid_uri", message: reason}}
    end
    
    {:reply, result, state}
  end
  
  @impl true
  def handle_call(:subscribe_to_changes, {pid, _ref}, state) do
    new_subscribers = [pid | state.subscribers]
    {:reply, :ok, %{state | subscribers: new_subscribers}}
  end
  
  # Private Functions
  
  defp parse_resource_uri(uri) do
    case String.split(uri, "/") do
      ["mcp:", "", "dynamic-forms", "routes" | filters] -> 
        {:routes, parse_filters(filters)}
      ["mcp:", "", "dynamic-forms", "forms" | filters] -> 
        {:forms, parse_filters(filters)}
      ["mcp:", "", "dynamic-forms", "plugins" | filters] -> 
        {:plugins, parse_filters(filters)}
      ["mcp:", "", "dynamic-forms", "connections" | filters] -> 
        {:connections, parse_filters(filters)}
      ["mcp:", "", "dynamic-forms", "stats" | filters] -> 
        {:stats, parse_filters(filters)}
      _ -> 
        {:error, "Invalid resource URI: #{uri}"}
    end
  end
  
  defp parse_filters(filters) do
    filters
    |> Enum.chunk_every(2)
    |> Enum.reduce(%{}, fn
      [key, value], acc -> Map.put(acc, key, value)
      [key], acc -> Map.put(acc, key, true)
    end)
  end
  
  defp read_routes_resource(filters) do
    try do
      routes = DynamicRouteManager.list_routes()
      |> apply_route_filters(filters)
      |> Enum.map(&format_route_for_mcp/1)
      
      {:ok, %{
        uri: "mcp://dynamic-forms/routes",
        data: routes,
        metadata: %{
          count: length(routes),
          filters_applied: filters,
          last_updated: DateTime.utc_now()
        }
      }}
    rescue
      error -> 
        Logger.error("Error reading routes resource: #{inspect(error)}")
        {:error, %{code: "resource_error", message: "Failed to read routes"}}
    end
  end
  
  defp read_forms_resource(filters) do
    try do
      forms = DynamicFormsManager.list_forms()
      |> apply_form_filters(filters)
      |> Enum.map(&format_form_for_mcp/1)
      
      {:ok, %{
        uri: "mcp://dynamic-forms/forms",
        data: forms,
        metadata: %{
          count: length(forms),
          filters_applied: filters,
          last_updated: DateTime.utc_now()
        }
      }}
    rescue
      error -> 
        Logger.error("Error reading forms resource: #{inspect(error)}")
        {:error, %{code: "resource_error", message: "Failed to read forms"}}
    end
  end
  
  defp read_plugins_resource(filters) do
    try do
      plugins = ProcessManager.list_processes()
      |> apply_plugin_filters(filters)
      |> Enum.map(&format_plugin_for_mcp/1)
      
      {:ok, %{
        uri: "mcp://dynamic-forms/plugins",
        data: plugins,
        metadata: %{
          count: length(plugins),
          filters_applied: filters,
          last_updated: DateTime.utc_now()
        }
      }}
    rescue
      error -> 
        Logger.error("Error reading plugins resource: #{inspect(error)}")
        {:error, %{code: "resource_error", message: "Failed to read plugins"}}
    end
  end
  
  defp read_connections_resource(_filters) do
    try do
      # Get route-form connections
      route_forms = get_route_form_connections()
      
      # Get route-process connections  
      route_processes = get_route_process_connections()
      
      # Get process chain connections
      process_chains = get_process_chain_connections()
      
      connections = %{
        route_forms: route_forms,
        route_processes: route_processes,
        process_chains: process_chains
      }
      
      {:ok, %{
        uri: "mcp://dynamic-forms/connections",
        data: connections,
        metadata: %{
          route_form_count: length(route_forms),
          route_process_count: length(route_processes),
          process_chain_count: length(process_chains),
          last_updated: DateTime.utc_now()
        }
      }}
    rescue
      error -> 
        Logger.error("Error reading connections resource: #{inspect(error)}")
        {:error, %{code: "resource_error", message: "Failed to read connections"}}
    end
  end
  
  defp read_stats_resource(_filters) do
    try do
      stats = %{
        routes: %{
          total: count_routes(),
          by_method: count_routes_by_method(),
          by_category: count_routes_by_category(),
          enabled: count_enabled_routes()
        },
        forms: %{
          total: count_forms(),
          by_method: count_forms_by_method(),
          required: count_required_forms()
        },
        plugins: %{
          total: count_plugins(),
          by_category: count_plugins_by_category(),
          by_type: count_plugins_by_type()
        }
      }
      
      {:ok, %{
        uri: "mcp://dynamic-forms/stats",
        data: stats,
        metadata: %{
          generated_at: DateTime.utc_now()
        }
      }}
    rescue
      error -> 
        Logger.error("Error reading stats resource: #{inspect(error)}")
        {:error, %{code: "resource_error", message: "Failed to read statistics"}}
    end
  end
  
  # Filter Functions
  
  defp apply_route_filters(routes, filters) do
    routes
    |> filter_if_present(filters, "method", fn route, method -> 
         String.upcase(route.method) == String.upcase(method) 
       end)
    |> filter_if_present(filters, "category", fn route, category -> 
         route.category == category 
       end)
    |> filter_if_present(filters, "enabled", fn route, enabled -> 
         route.enabled == (enabled == "true") 
       end)
  end
  
  defp apply_form_filters(forms, filters) do
    forms
    |> filter_if_present(filters, "method", fn form, method -> 
         String.upcase(form.http_method) == String.upcase(method) 
       end)
    |> filter_if_present(filters, "required", fn form, required -> 
         form.required == (required == "true") 
       end)
  end
  
  defp apply_plugin_filters(plugins, filters) do
    plugins
    |> filter_if_present(filters, "category", fn plugin, category -> 
         plugin.category == category 
       end)
    |> filter_if_present(filters, "type", fn plugin, type -> 
         plugin.plugin_type == type 
       end)
  end
  
  defp filter_if_present(items, filters, key, filter_fn) do
    case Map.get(filters, key) do
      nil -> items
      value -> Enum.filter(items, fn item -> filter_fn.(item, value) end)
    end
  end
  
  # Format Functions
  
  defp format_route_for_mcp(route) do
    %{
      id: route.id,
      name: route.name,
      method: route.method,
      path: route.path,
      enabled: route.enabled,
      category: route.category,
      group_name: route.group_name,
      priority: route.priority,
      tags: route.tags || [],
      description: route.description,
      created_at: route.inserted_at,
      updated_at: route.updated_at
    }
  end
  
  defp format_form_for_mcp(form) do
    %{
      id: form.id,
      name: form.name,
      description: form.description,
      http_method: form.http_method,
      required: form.required,
      form_schema: form.form,
      validation_schema: form.validation_schema,
      field_count: length(form.form["fields"] || []),
      created_at: form.inserted_at,
      updated_at: form.updated_at
    }
  end
  
  defp format_plugin_for_mcp(plugin) do
    %{
      id: plugin.id,
      name: plugin.name,
      description: plugin.description,
      category: plugin.category,
      group: plugin.group,
      plugin_type: plugin.plugin_type,
      version: plugin.version,
      author: plugin.author,
      tags: plugin.tags || [],
      rating: plugin.rating,
      downloads: plugin.downloads,
      featured: plugin.featured,
      verified: plugin.verified,
      execution_mode: plugin.execution_mode,
      has_code: !is_nil(plugin.code),
      created_at: plugin.inserted_at,
      updated_at: plugin.updated_at
    }
  end
  
  # Helper Functions for Connections
  
  defp get_route_form_connections() do
    # Query dynamic_route_forms table
    import Ecto.Query
    
    query = from rf in "dynamic_route_forms",
            join: r in DynamicRoute, on: rf.route_id == r.id,
            join: f in DynamicForm, on: rf.form_id == f.id,
            select: %{
              route_id: rf.route_id,
              form_id: rf.form_id,
              route_name: r.name,
              form_name: f.name,
              created_at: rf.inserted_at
            }
            
    ServiceManager.Repo.all(query)
  end
  
  defp get_route_process_connections() do
    # Query route_process_links table
    import Ecto.Query
    
    query = from rpl in "route_process_links",
            join: r in DynamicRoute, on: rpl.route_id == r.id,
            join: p in DynamicProcess, on: rpl.initial_process_id == p.id,
            select: %{
              route_id: rpl.route_id,
              process_id: rpl.initial_process_id,
              route_name: r.name,
              process_name: p.name,
              created_at: rpl.inserted_at
            }
            
    ServiceManager.Repo.all(query)
  end
  
  defp get_process_chain_connections() do
    # Query process_chain_links table
    import Ecto.Query
    
    query = from pcl in "process_chain_links",
            join: sp in DynamicProcess, on: pcl.source_process_id == sp.id,
            join: tp in DynamicProcess, on: pcl.target_process_id == tp.id,
            select: %{
              source_process_id: pcl.source_process_id,
              target_process_id: pcl.target_process_id,
              source_process_name: sp.name,
              target_process_name: tp.name,
              chain_id: pcl.chain_id,
              position: pcl.position,
              is_root: pcl.is_root,
              created_at: pcl.inserted_at
            }
            
    ServiceManager.Repo.all(query)
  end
  
  # Statistics Helper Functions
  
  defp count_routes() do
    ServiceManager.Repo.aggregate(DynamicRoute, :count)
  end
  
  defp count_routes_by_method() do
    import Ecto.Query
    
    query = from r in DynamicRoute,
            group_by: r.method,
            select: {r.method, count(r.id)}
    
    ServiceManager.Repo.all(query) |> Enum.into(%{})
  end
  
  defp count_routes_by_category() do
    import Ecto.Query
    
    query = from r in DynamicRoute,
            group_by: r.category,
            select: {r.category, count(r.id)}
    
    ServiceManager.Repo.all(query) |> Enum.into(%{})
  end
  
  defp count_enabled_routes() do
    import Ecto.Query
    
    ServiceManager.Repo.aggregate(
      from(r in DynamicRoute, where: r.enabled == true), 
      :count
    )
  end
  
  defp count_forms() do
    ServiceManager.Repo.aggregate(DynamicForm, :count)
  end
  
  defp count_forms_by_method() do
    import Ecto.Query
    
    query = from f in DynamicForm,
            group_by: f.http_method,
            select: {f.http_method, count(f.id)}
    
    ServiceManager.Repo.all(query) |> Enum.into(%{})
  end
  
  defp count_required_forms() do
    import Ecto.Query
    
    ServiceManager.Repo.aggregate(
      from(f in DynamicForm, where: f.required == true), 
      :count
    )
  end
  
  defp count_plugins() do
    ServiceManager.Repo.aggregate(DynamicProcess, :count)
  end
  
  defp count_plugins_by_category() do
    import Ecto.Query
    
    query = from p in DynamicProcess,
            group_by: p.category,
            select: {p.category, count(p.id)}
    
    ServiceManager.Repo.all(query) |> Enum.into(%{})
  end
  
  defp count_plugins_by_type() do
    import Ecto.Query
    
    query = from p in DynamicProcess,
            group_by: p.plugin_type,
            select: {p.plugin_type, count(p.id)}
    
    ServiceManager.Repo.all(query) |> Enum.into(%{})
  end
end