defmodule ServiceManager.MCP.Supervisor do
  @moduledoc """
  Supervisor for the MCP (Model Context Protocol) server components.
  """
  
  use Supervisor
  
  def start_link(init_arg) do
    Supervisor.start_link(__MODULE__, init_arg, name: __MODULE__)
  end
  
  @impl true
  def init(_init_arg) do
    children = [
      # Start supporting services first
      {ServiceManager.MCP.Resources, []},
      {ServiceManager.MCP.Tools, []},
      {ServiceManager.MCP.Prompts, []},
      # Start main server last
      {ServiceManager.MCP.Server, []}
    ]

    Supervisor.init(children, strategy: :one_for_one)
  end
end