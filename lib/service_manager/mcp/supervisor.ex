defmodule ServiceManager.MCP.Supervisor do
  @moduledoc """
  Supervisor for the MCP (Model Context Protocol) server components.
  """

  use Supervisor
  require Logger

  def start_link(init_arg) do
    Logger.info("🚀 Starting MCP Supervisor with args: #{inspect(init_arg)}")
    result = Supervisor.start_link(__MODULE__, init_arg, name: __MODULE__)

    case result do
      {:ok, pid} ->
        Logger.info("✅ MCP Supervisor started successfully with PID: #{inspect(pid)}")
        {:ok, pid}
      {:error, reason} ->
        Logger.error("❌ Failed to start MCP Supervisor: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @impl true
  def init(_init_arg) do
    Logger.info("🔧 Initializing MCP Supervisor children...")

    children = [
      # Start supporting services first
      {ServiceManager.MCP.Resources, []},
      {ServiceManager.MCP.Tools, []},
      {ServiceManager.MCP.Prompts, []},
      # Start main server last
      {ServiceManager.MCP.Server, []}
    ]

    Logger.info("📋 MCP Supervisor children defined: #{length(children)} processes")
    Logger.debug("   Children: #{inspect(Enum.map(children, fn {module, _} -> module end))}")

    result = Supervisor.init(children, strategy: :one_for_one)
    Logger.info("✅ MCP Supervisor initialization complete")
    result
  end
end