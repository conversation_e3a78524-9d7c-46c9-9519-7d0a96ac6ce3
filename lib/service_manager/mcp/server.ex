defmodule ServiceManager.MCP.Server do
  @moduledoc """
  Model Context Protocol (MCP) server for managing dynamic forms system.
  
  This server provides standardized interfaces for AI assistants to:
  - Manage dynamic routes, forms, and plugins
  - Create and modify entity relationships
  - Perform CRUD operations with validation
  - Generate AI-assisted workflows
  """
  
  use GenServer
  require Logger
  
  alias ServiceManager.MCP.{Resources, Tools, Prompts, Schemas}
  
  @server_info %{
    name: "Dynamic Forms MCP Server",
    version: "1.0.0",
    description: "MCP server for managing dynamic API routes, forms, and plugins",
    author: "ServiceManager Team",
    license: "MIT"
  }
  
  @default_config %{
    port: 8080,
    host: "localhost",
    enabled: true,
    auth_required: false,
    max_connections: 100
  }
  
  # GenServer API
  
  def start_link(opts \\ []) do
    Logger.info("🚀 Starting MCP Server with options: #{inspect(opts)}")
    config = build_config(opts)
    Logger.info("⚙️  MCP Server config: #{inspect(config)}")

    result = GenServer.start_link(__MODULE__, config, name: __MODULE__)

    case result do
      {:ok, pid} ->
        Logger.info("✅ MCP Server GenServer started with PID: #{inspect(pid)}")
        {:ok, pid}
      {:error, reason} ->
        Logger.error("❌ Failed to start MCP Server GenServer: #{inspect(reason)}")
        {:error, reason}
    end
  end
  
  def stop() do
    GenServer.stop(__MODULE__)
  end
  
  # Client API
  
  @doc """
  Get server information and capabilities
  """
  def get_server_info() do
    GenServer.call(__MODULE__, :get_server_info)
  end
  
  @doc """
  List all available resources
  """
  def list_resources() do
    GenServer.call(__MODULE__, :list_resources)
  end
  
  @doc """
  List all available tools
  """
  def list_tools() do
    GenServer.call(__MODULE__, :list_tools)
  end
  
  @doc """
  List all available prompts
  """
  def list_prompts() do
    GenServer.call(__MODULE__, :list_prompts)
  end
  
  @doc """
  Handle MCP request
  """
  def handle_request(method, params) do
    GenServer.call(__MODULE__, {:handle_request, method, params})
  end
  
  # GenServer Callbacks
  
  @impl true
  def init(config) do
    Logger.info("🔧 Initializing MCP Server...")
    Logger.info("   Host: #{config.host}")
    Logger.info("   Port: #{config.port}")
    Logger.info("   Enabled: #{config.enabled}")
    Logger.info("   Auth Required: #{config.auth_required}")
    Logger.info("   Max Connections: #{config.max_connections}")

    state = %{
      config: config,
      connections: MapSet.new(),
      request_count: 0,
      start_time: DateTime.utc_now()
    }

    if config.enabled do
      Logger.info("✅ MCP Server enabled - checking dependencies...")

      # Check if required processes are available
      check_dependencies()

      Logger.info("✅ Dynamic Forms MCP Server initialized successfully")
      Logger.info("🌐 Server available at http://#{config.host}:#{config.port}")
    else
      Logger.warn("⚠️  MCP Server is disabled in configuration")
    end

    {:ok, state}
  end
  
  @impl true
  def handle_call(:get_server_info, _from, state) do
    info = Map.merge(@server_info, %{
      status: "running",
      uptime: DateTime.diff(DateTime.utc_now(), state.start_time),
      connections: MapSet.size(state.connections),
      requests_handled: state.request_count,
      config: state.config
    })
    
    {:reply, {:ok, info}, state}
  end
  
  @impl true
  def handle_call(:list_resources, _from, state) do
    resources = Resources.list_all()
    {:reply, {:ok, resources}, state}
  end
  
  @impl true
  def handle_call(:list_tools, _from, state) do
    tools = Tools.list_all()
    {:reply, {:ok, tools}, state}
  end
  
  @impl true
  def handle_call(:list_prompts, _from, state) do
    prompts = Prompts.list_all()
    {:reply, {:ok, prompts}, state}
  end
  
  @impl true
  def handle_call({:handle_request, method, params}, _from, state) do
    result = dispatch_request(method, params)
    new_state = %{state | request_count: state.request_count + 1}
    {:reply, result, new_state}
  end
  
  @impl true
  def handle_info(:cleanup, state) do
    # Periodic cleanup of stale connections
    Logger.debug("Running MCP server cleanup")
    {:noreply, state}
  end
  
  # Private Functions
  
  defp check_dependencies() do
    Logger.info("🔍 Checking MCP Server dependencies...")

    # Check if database is available
    try do
      case ServiceManager.Repo.__adapter__.storage_status(ServiceManager.Repo.config()) do
        :up ->
          Logger.info("✅ Database connection: OK")
        status ->
          Logger.warn("⚠️  Database status: #{inspect(status)}")
      end
    rescue
      error ->
        Logger.error("❌ Database check failed: #{inspect(error)}")
    end

    # Check if required modules are loaded
    required_modules = [
      ServiceManager.MCP.Resources,
      ServiceManager.MCP.Tools,
      ServiceManager.MCP.Prompts,
      ServiceManager.Routing.DynamicRouteManager,
      ServiceManager.Forms.DynamicFormsManager
    ]

    Enum.each(required_modules, fn module ->
      if Code.ensure_loaded?(module) do
        Logger.info("✅ Module loaded: #{module}")
      else
        Logger.error("❌ Module not loaded: #{module}")
      end
    end)

    Logger.info("🔍 Dependency check complete")
  end

  defp build_config(opts) do
    Logger.debug("🔧 Building MCP Server configuration...")
    Logger.debug("   Default config: #{inspect(@default_config)}")

    app_config = Application.get_env(:service_manager, :mcp_server, %{})
    Logger.debug("   App config: #{inspect(app_config)}")
    Logger.debug("   Runtime opts: #{inspect(opts)}")

    final_config = @default_config
    |> Map.merge(app_config)
    |> Map.merge(Enum.into(opts, %{}))

    Logger.debug("   Final config: #{inspect(final_config)}")
    final_config
  end
  
  defp dispatch_request("initialize", params) do
    handle_initialize(params)
  end
  
  defp dispatch_request("resources/list", _params) do
    Resources.list_all()
  end
  
  defp dispatch_request("resources/read", %{"uri" => uri}) do
    Resources.read_resource(uri)
  end
  
  defp dispatch_request("tools/list", _params) do
    Tools.list_all()
  end
  
  defp dispatch_request("tools/call", %{"name" => name, "arguments" => args}) do
    Tools.call_tool(name, args)
  end
  
  defp dispatch_request("prompts/list", _params) do
    Prompts.list_all()
  end
  
  defp dispatch_request("prompts/get", %{"name" => name, "arguments" => args}) do
    Prompts.get_prompt(name, args)
  end
  
  defp dispatch_request(method, _params) do
    {:error, %{code: "method_not_found", message: "Unknown method: #{method}"}}
  end
  
  defp handle_initialize(params) do
    client_info = Map.get(params, "clientInfo", %{})
    capabilities = Map.get(params, "capabilities", %{})
    
    Logger.info("MCP client initialized: #{inspect(client_info)}")
    
    server_capabilities = %{
      resources: %{
        subscribe: true,
        listChanged: true
      },
      tools: %{},
      prompts: %{},
      logging: %{}
    }
    
    {:ok, %{
      protocolVersion: "2024-11-05",
      serverInfo: @server_info,
      capabilities: server_capabilities
    }}
  end
end