defmodule ServiceManager.MCP.Server do
  @moduledoc """
  Model Context Protocol (MCP) server for managing dynamic forms system.
  
  This server provides standardized interfaces for AI assistants to:
  - Manage dynamic routes, forms, and plugins
  - Create and modify entity relationships
  - Perform CRUD operations with validation
  - Generate AI-assisted workflows
  """
  
  use GenServer
  require Logger
  
  alias ServiceManager.MCP.{Resources, Tools, Prompts, Schemas}
  
  @server_info %{
    name: "Dynamic Forms MCP Server",
    version: "1.0.0",
    description: "MCP server for managing dynamic API routes, forms, and plugins",
    author: "ServiceManager Team",
    license: "MIT"
  }
  
  @default_config %{
    port: 8080,
    host: "localhost",
    enabled: true,
    auth_required: false,
    max_connections: 100
  }
  
  # GenServer API
  
  def start_link(opts \\ []) do
    config = build_config(opts)
    GenServer.start_link(__MODULE__, config, name: __MODULE__)
  end
  
  def stop() do
    GenServer.stop(__MODULE__)
  end
  
  # Client API
  
  @doc """
  Get server information and capabilities
  """
  def get_server_info() do
    GenServer.call(__MODULE__, :get_server_info)
  end
  
  @doc """
  List all available resources
  """
  def list_resources() do
    GenServer.call(__MODULE__, :list_resources)
  end
  
  @doc """
  List all available tools
  """
  def list_tools() do
    GenServer.call(__MODULE__, :list_tools)
  end
  
  @doc """
  List all available prompts
  """
  def list_prompts() do
    GenServer.call(__MODULE__, :list_prompts)
  end
  
  @doc """
  Handle MCP request
  """
  def handle_request(method, params) do
    GenServer.call(__MODULE__, {:handle_request, method, params})
  end
  
  # GenServer Callbacks
  
  @impl true
  def init(config) do
    Logger.info("Starting Dynamic Forms MCP Server on #{config.host}:#{config.port}")

    state = %{
      config: config,
      connections: MapSet.new(),
      request_count: 0,
      start_time: DateTime.utc_now()
    }

    if config.enabled do
      Logger.info("Dynamic Forms MCP Server started successfully")
    end

    {:ok, state}
  end
  
  @impl true
  def handle_call(:get_server_info, _from, state) do
    info = Map.merge(@server_info, %{
      status: "running",
      uptime: DateTime.diff(DateTime.utc_now(), state.start_time),
      connections: MapSet.size(state.connections),
      requests_handled: state.request_count,
      config: state.config
    })
    
    {:reply, {:ok, info}, state}
  end
  
  @impl true
  def handle_call(:list_resources, _from, state) do
    resources = Resources.list_all()
    {:reply, {:ok, resources}, state}
  end
  
  @impl true
  def handle_call(:list_tools, _from, state) do
    tools = Tools.list_all()
    {:reply, {:ok, tools}, state}
  end
  
  @impl true
  def handle_call(:list_prompts, _from, state) do
    prompts = Prompts.list_all()
    {:reply, {:ok, prompts}, state}
  end
  
  @impl true
  def handle_call({:handle_request, method, params}, _from, state) do
    result = dispatch_request(method, params)
    new_state = %{state | request_count: state.request_count + 1}
    {:reply, result, new_state}
  end
  
  @impl true
  def handle_info(:cleanup, state) do
    # Periodic cleanup of stale connections
    Logger.debug("Running MCP server cleanup")
    {:noreply, state}
  end
  
  # Private Functions
  
  defp build_config(opts) do
    app_config = Application.get_env(:service_manager, :mcp_server, %{})
    
    @default_config
    |> Map.merge(app_config)
    |> Map.merge(Enum.into(opts, %{}))
  end
  
  defp dispatch_request("initialize", params) do
    handle_initialize(params)
  end
  
  defp dispatch_request("resources/list", _params) do
    Resources.list_all()
  end
  
  defp dispatch_request("resources/read", %{"uri" => uri}) do
    Resources.read_resource(uri)
  end
  
  defp dispatch_request("tools/list", _params) do
    Tools.list_all()
  end
  
  defp dispatch_request("tools/call", %{"name" => name, "arguments" => args}) do
    Tools.call_tool(name, args)
  end
  
  defp dispatch_request("prompts/list", _params) do
    Prompts.list_all()
  end
  
  defp dispatch_request("prompts/get", %{"name" => name, "arguments" => args}) do
    Prompts.get_prompt(name, args)
  end
  
  defp dispatch_request(method, _params) do
    {:error, %{code: "method_not_found", message: "Unknown method: #{method}"}}
  end
  
  defp handle_initialize(params) do
    client_info = Map.get(params, "clientInfo", %{})
    capabilities = Map.get(params, "capabilities", %{})
    
    Logger.info("MCP client initialized: #{inspect(client_info)}")
    
    server_capabilities = %{
      resources: %{
        subscribe: true,
        listChanged: true
      },
      tools: %{},
      prompts: %{},
      logging: %{}
    }
    
    {:ok, %{
      protocolVersion: "2024-11-05",
      serverInfo: @server_info,
      capabilities: server_capabilities
    }}
  end
end