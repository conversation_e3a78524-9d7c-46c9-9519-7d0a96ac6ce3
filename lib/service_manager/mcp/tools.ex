defmodule ServiceManager.MCP.Tools do
  @moduledoc """
  MCP Tools module for performing CRUD operations on dynamic forms system entities.
  
  Provides tool interfaces for:
  - Route management (create, update, delete, enable/disable)
  - Form management (create, update, delete, validate)
  - Plugin management (create, update, delete, test)
  - Entity linking/unlinking operations
  """
  
  use GenServer
  require Logger
  
  alias ServiceManager.Routing.{DynamicRoute, DynamicRouteManager}
  alias ServiceManager.Forms.{DynamicForm, DynamicFormsManager, DynamicRouteForm}
  alias ServiceManager.Schemas.Dynamic.Processes.{DynamicProcess, ProcessManager, RouteProcessLink}
  alias ServiceManager.MCP.Schemas
  
  # GenServer API
  
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end
  
  # Client API
  
  @doc """
  List all available tools
  """
  def list_all() do
    GenServer.call(__MODULE__, :list_all)
  end
  
  @doc """
  Call a specific tool with arguments
  """
  def call_tool(name, args) do
    GenServer.call(__MODULE__, {:call_tool, name, args})
  end
  
  # GenServer Callbacks
  
  @impl true
  def init(_opts) do
    Logger.info("Starting MCP Tools manager")
    {:ok, %{call_count: %{}}}
  end
  
  @impl true
  def handle_call(:list_all, _from, state) do
    tools = [
      # Route Management Tools
      %{
        name: "create_route",
        description: "Create a new dynamic route",
        inputSchema: %{
          type: "object",
          properties: %{
            name: %{type: "string", description: "Route name"},
            method: %{type: "string", enum: ["GET", "POST", "PUT", "PATCH", "DELETE"], description: "HTTP method"},
            path: %{type: "string", description: "Route path pattern"},
            category: %{type: "string", description: "Route category"},
            group_name: %{type: "string", description: "Route group name"},
            description: %{type: "string", description: "Route description"},
            priority: %{type: "integer", description: "Route priority (1-10)"},
            tags: %{type: "array", items: %{type: "string"}, description: "Route tags"},
            enabled: %{type: "boolean", default: true, description: "Enable route"}
          },
          required: ["name", "method", "path"]
        }
      },
      %{
        name: "update_route",
        description: "Update an existing route",
        inputSchema: %{
          type: "object",
          properties: %{
            id: %{type: "integer", description: "Route ID"},
            name: %{type: "string", description: "Route name"},
            method: %{type: "string", enum: ["GET", "POST", "PUT", "PATCH", "DELETE"]},
            path: %{type: "string", description: "Route path pattern"},
            category: %{type: "string", description: "Route category"},
            group_name: %{type: "string", description: "Route group name"},
            description: %{type: "string", description: "Route description"},
            priority: %{type: "integer", description: "Route priority (1-10)"},
            tags: %{type: "array", items: %{type: "string"}},
            enabled: %{type: "boolean", description: "Enable/disable route"}
          },
          required: ["id"]
        }
      },
      %{
        name: "delete_route",
        description: "Delete a route",
        inputSchema: %{
          type: "object",
          properties: %{
            id: %{type: "integer", description: "Route ID"}
          },
          required: ["id"]
        }
      },
      %{
        name: "toggle_route",
        description: "Enable or disable a route",
        inputSchema: %{
          type: "object",
          properties: %{
            id: %{type: "integer", description: "Route ID"},
            enabled: %{type: "boolean", description: "Enable/disable state"}
          },
          required: ["id", "enabled"]
        }
      },
      
      # Form Management Tools
      %{
        name: "create_form",
        description: "Create a new dynamic form",
        inputSchema: %{
          type: "object",
          properties: %{
            name: %{type: "string", description: "Form name"},
            description: %{type: "string", description: "Form description"},
            http_method: %{type: "string", enum: ["GET", "POST", "PUT", "PATCH", "DELETE"], description: "HTTP method"},
            form_schema: %{type: "object", description: "JSON form schema"},
            validation_schema: %{type: "object", description: "JSON validation schema"},
            required: %{type: "boolean", default: true, description: "Is form required"}
          },
          required: ["name", "http_method", "form_schema", "validation_schema"]
        }
      },
      %{
        name: "update_form",
        description: "Update an existing form",
        inputSchema: %{
          type: "object",
          properties: %{
            id: %{type: "integer", description: "Form ID"},
            name: %{type: "string", description: "Form name"},
            description: %{type: "string", description: "Form description"},
            http_method: %{type: "string", enum: ["GET", "POST", "PUT", "PATCH", "DELETE"]},
            form_schema: %{type: "object", description: "JSON form schema"},
            validation_schema: %{type: "object", description: "JSON validation schema"},
            required: %{type: "boolean", description: "Is form required"}
          },
          required: ["id"]
        }
      },
      %{
        name: "delete_form",
        description: "Delete a form",
        inputSchema: %{
          type: "object",
          properties: %{
            id: %{type: "integer", description: "Form ID"}
          },
          required: ["id"]
        }
      },
      %{
        name: "validate_form_data",
        description: "Validate data against a form's validation schema",
        inputSchema: %{
          type: "object",
          properties: %{
            form_id: %{type: "integer", description: "Form ID"},
            data: %{type: "object", description: "Data to validate"}
          },
          required: ["form_id", "data"]
        }
      },
      
      # Plugin Management Tools
      %{
        name: "create_plugin",
        description: "Create a new dynamic plugin/process",
        inputSchema: %{
          type: "object",
          properties: %{
            name: %{type: "string", description: "Plugin name"},
            description: %{type: "string", description: "Plugin description"},
            code: %{type: "string", description: "Plugin code"},
            category: %{type: "string", default: "General", description: "Plugin category"},
            group: %{type: "string", default: "User Plugins", description: "Plugin group"},
            plugin_type: %{type: "string", enum: ["system", "public", "protected", "private", "enterprise"], default: "public"},
            version: %{type: "string", default: "1.0.0", description: "Plugin version"},
            author: %{type: "string", description: "Plugin author"},
            tags: %{type: "array", items: %{type: "string"}, description: "Plugin tags"},
            expected_params: %{type: "object", description: "Expected parameters schema"}
          },
          required: ["name", "code"]
        }
      },
      %{
        name: "update_plugin",
        description: "Update an existing plugin",
        inputSchema: %{
          type: "object",
          properties: %{
            id: %{type: "integer", description: "Plugin ID"},
            name: %{type: "string", description: "Plugin name"},
            description: %{type: "string", description: "Plugin description"},
            code: %{type: "string", description: "Plugin code"},
            category: %{type: "string", description: "Plugin category"},
            group: %{type: "string", description: "Plugin group"},
            plugin_type: %{type: "string", enum: ["system", "public", "protected", "private", "enterprise"]},
            version: %{type: "string", description: "Plugin version"},
            author: %{type: "string", description: "Plugin author"},
            tags: %{type: "array", items: %{type: "string"}},
            expected_params: %{type: "object", description: "Expected parameters schema"}
          },
          required: ["id"]
        }
      },
      %{
        name: "delete_plugin",
        description: "Delete a plugin",
        inputSchema: %{
          type: "object",
          properties: %{
            id: %{type: "integer", description: "Plugin ID"}
          },
          required: ["id"]
        }
      },
      %{
        name: "test_plugin",
        description: "Test a plugin with sample parameters",
        inputSchema: %{
          type: "object",
          properties: %{
            id: %{type: "integer", description: "Plugin ID"},
            test_params: %{type: "object", description: "Test parameters"}
          },
          required: ["id", "test_params"]
        }
      },
      
      # Linking Tools
      %{
        name: "link_route_form",
        description: "Link a route to a form",
        inputSchema: %{
          type: "object",
          properties: %{
            route_id: %{type: "integer", description: "Route ID"},
            form_id: %{type: "integer", description: "Form ID"}
          },
          required: ["route_id", "form_id"]
        }
      },
      %{
        name: "unlink_route_form",
        description: "Unlink a route from a form",
        inputSchema: %{
          type: "object",
          properties: %{
            route_id: %{type: "integer", description: "Route ID"},
            form_id: %{type: "integer", description: "Form ID"}
          },
          required: ["route_id", "form_id"]
        }
      },
      %{
        name: "link_route_plugin",
        description: "Link a route to a plugin/process",
        inputSchema: %{
          type: "object",
          properties: %{
            route_id: %{type: "integer", description: "Route ID"},
            plugin_id: %{type: "integer", description: "Plugin ID"}
          },
          required: ["route_id", "plugin_id"]
        }
      },
      %{
        name: "unlink_route_plugin",
        description: "Unlink a route from a plugin",
        inputSchema: %{
          type: "object",
          properties: %{
            route_id: %{type: "integer", description: "Route ID"}
          },
          required: ["route_id"]
        }
      },
      %{
        name: "get_entity_connections",
        description: "Get all connections for a specific entity",
        inputSchema: %{
          type: "object",
          properties: %{
            entity_type: %{type: "string", enum: ["route", "form", "plugin"], description: "Entity type"},
            entity_id: %{type: "integer", description: "Entity ID"}
          },
          required: ["entity_type", "entity_id"]
        }
      }
    ]
    
    {:reply, {:ok, tools}, state}
  end
  
  @impl true
  def handle_call({:call_tool, name, args}, _from, state) do
    # Update call count
    new_count = Map.get(state.call_count, name, 0) + 1
    new_state = put_in(state.call_count[name], new_count)
    
    result = case name do
      # Route Tools
      "create_route" -> create_route(args)
      "update_route" -> update_route(args)
      "delete_route" -> delete_route(args)
      "toggle_route" -> toggle_route(args)
      
      # Form Tools
      "create_form" -> create_form(args)
      "update_form" -> update_form(args)
      "delete_form" -> delete_form(args)
      "validate_form_data" -> validate_form_data(args)
      
      # Plugin Tools
      "create_plugin" -> create_plugin(args)
      "update_plugin" -> update_plugin(args)
      "delete_plugin" -> delete_plugin(args)
      "test_plugin" -> test_plugin(args)
      
      # Linking Tools
      "link_route_form" -> link_route_form(args)
      "unlink_route_form" -> unlink_route_form(args)
      "link_route_plugin" -> link_route_plugin(args)
      "unlink_route_plugin" -> unlink_route_plugin(args)
      "get_entity_connections" -> get_entity_connections(args)
      
      _ -> {:error, %{code: "unknown_tool", message: "Unknown tool: #{name}"}}
    end
    
    {:reply, result, new_state}
  end
  
  # Route Tool Implementations
  
  defp create_route(args) do
    case DynamicRouteManager.create_route(args) do
      {:ok, route} -> 
        {:ok, %{
          success: true,
          message: "Route created successfully",
          route: format_route_response(route)
        }}
      {:error, changeset} -> 
        {:error, %{
          code: "validation_error",
          message: "Failed to create route",
          errors: format_changeset_errors(changeset)
        }}
    end
  end
  
  defp update_route(args) do
    with {:ok, route} <- get_route_by_id(args["id"]),
         {:ok, updated_route} <- DynamicRouteManager.update_route(route, args) do
      {:ok, %{
        success: true,
        message: "Route updated successfully",
        route: format_route_response(updated_route)
      }}
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Route not found"}}
      {:error, changeset} -> 
        {:error, %{
          code: "validation_error",
          message: "Failed to update route",
          errors: format_changeset_errors(changeset)
        }}
    end
  end
  
  defp delete_route(args) do
    with {:ok, route} <- get_route_by_id(args["id"]),
         {:ok, _} <- DynamicRouteManager.delete_route(route) do
      {:ok, %{
        success: true,
        message: "Route deleted successfully"
      }}
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Route not found"}}
      {:error, reason} -> 
        {:error, %{
          code: "delete_error",
          message: "Failed to delete route: #{inspect(reason)}"
        }}
    end
  end
  
  defp toggle_route(args) do
    with {:ok, route} <- get_route_by_id(args["id"]),
         {:ok, updated_route} <- DynamicRouteManager.update_route(route, %{enabled: args["enabled"]}) do
      {:ok, %{
        success: true,
        message: "Route #{if args["enabled"], do: "enabled", else: "disabled"} successfully",
        route: format_route_response(updated_route)
      }}
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Route not found"}}
      {:error, changeset} -> 
        {:error, %{
          code: "update_error",
          message: "Failed to toggle route",
          errors: format_changeset_errors(changeset)
        }}
    end
  end
  
  # Form Tool Implementations
  
  defp create_form(args) do
    form_attrs = %{
      name: args["name"],
      description: args["description"],
      http_method: args["http_method"],
      form: args["form_schema"],
      validation_schema: args["validation_schema"],
      required: Map.get(args, "required", true)
    }
    
    case DynamicFormsManager.create_form(form_attrs) do
      {:ok, form} -> 
        {:ok, %{
          success: true,
          message: "Form created successfully",
          form: format_form_response(form)
        }}
      {:error, changeset} -> 
        {:error, %{
          code: "validation_error",
          message: "Failed to create form",
          errors: format_changeset_errors(changeset)
        }}
    end
  end
  
  defp update_form(args) do
    with {:ok, form} <- get_form_by_id(args["id"]),
         update_attrs <- Map.drop(args, ["id"]) |> rename_form_keys(),
         {:ok, updated_form} <- DynamicFormsManager.update_form(form, update_attrs) do
      {:ok, %{
        success: true,
        message: "Form updated successfully",
        form: format_form_response(updated_form)
      }}
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Form not found"}}
      {:error, changeset} -> 
        {:error, %{
          code: "validation_error",
          message: "Failed to update form",
          errors: format_changeset_errors(changeset)
        }}
    end
  end
  
  defp delete_form(args) do
    with {:ok, form} <- get_form_by_id(args["id"]),
         {:ok, _} <- DynamicFormsManager.delete_form(form) do
      {:ok, %{
        success: true,
        message: "Form deleted successfully"
      }}
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Form not found"}}
      {:error, reason} -> 
        {:error, %{
          code: "delete_error",
          message: "Failed to delete form: #{inspect(reason)}"
        }}
    end
  end
  
  defp validate_form_data(args) do
    with {:ok, form} <- get_form_by_id(args["form_id"]) do
      case DynamicFormsManager.validate_request(form, args["data"]) do
        {:ok, validated_data} ->
          {:ok, %{
            success: true,
            message: "Data validation successful",
            validated_data: validated_data
          }}
        {:error, errors} ->
          {:ok, %{
            success: false,
            message: "Data validation failed",
            errors: errors
          }}
      end
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Form not found"}}
    end
  end
  
  # Plugin Tool Implementations
  
  defp create_plugin(args) do
    plugin_attrs = Map.merge(args, %{
      "category" => args["category"] || "General",
      "group" => args["group"] || "User Plugins",
      "plugin_type" => args["plugin_type"] || "public",
      "version" => args["version"] || "1.0.0"
    })
    
    case ProcessManager.create_process(plugin_attrs) do
      {:ok, plugin} -> 
        {:ok, %{
          success: true,
          message: "Plugin created successfully",
          plugin: format_plugin_response(plugin)
        }}
      {:error, changeset} -> 
        {:error, %{
          code: "validation_error",
          message: "Failed to create plugin",
          errors: format_changeset_errors(changeset)
        }}
    end
  end
  
  defp update_plugin(args) do
    with {:ok, plugin} <- get_plugin_by_id(args["id"]),
         update_attrs <- Map.drop(args, ["id"]),
         {:ok, updated_plugin} <- ProcessManager.update_process(plugin, update_attrs) do
      {:ok, %{
        success: true,
        message: "Plugin updated successfully",
        plugin: format_plugin_response(updated_plugin)
      }}
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Plugin not found"}}
      {:error, changeset} -> 
        {:error, %{
          code: "validation_error",
          message: "Failed to update plugin",
          errors: format_changeset_errors(changeset)
        }}
    end
  end
  
  defp delete_plugin(args) do
    with {:ok, plugin} <- get_plugin_by_id(args["id"]),
         {:ok, _} <- ProcessManager.delete_process(plugin) do
      {:ok, %{
        success: true,
        message: "Plugin deleted successfully"
      }}
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Plugin not found"}}
      {:error, reason} -> 
        {:error, %{
          code: "delete_error",
          message: "Failed to delete plugin: #{inspect(reason)}"
        }}
    end
  end
  
  defp test_plugin(args) do
    with {:ok, plugin} <- get_plugin_by_id(args["id"]) do
      case ProcessManager.test_process(plugin, args["test_params"]) do
        {:ok, result} ->
          {:ok, %{
            success: true,
            message: "Plugin test completed",
            result: result
          }}
        {:error, reason} ->
          {:ok, %{
            success: false,
            message: "Plugin test failed",
            error: reason
          }}
      end
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Plugin not found"}}
    end
  end
  
  # Linking Tool Implementations
  
  defp link_route_form(args) do
    with {:ok, _route} <- get_route_by_id(args["route_id"]),
         {:ok, _form} <- get_form_by_id(args["form_id"]) do
      
      link_attrs = %{
        route_id: args["route_id"],
        form_id: args["form_id"]
      }
      
      case ServiceManager.Repo.insert(%DynamicRouteForm{} |> DynamicRouteForm.changeset(link_attrs)) do
        {:ok, _link} ->
          {:ok, %{
            success: true,
            message: "Route and form linked successfully"
          }}
        {:error, changeset} ->
          {:error, %{
            code: "link_error",
            message: "Failed to link route and form",
            errors: format_changeset_errors(changeset)
          }}
      end
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Route or form not found"}}
    end
  end
  
  defp unlink_route_form(args) do
    import Ecto.Query
    
    with {:ok, _route} <- get_route_by_id(args["route_id"]),
         {:ok, _form} <- get_form_by_id(args["form_id"]) do
      
      query = from rf in DynamicRouteForm,
              where: rf.route_id == ^args["route_id"] and rf.form_id == ^args["form_id"]
      
      case ServiceManager.Repo.delete_all(query) do
        {count, _} when count > 0 ->
          {:ok, %{
            success: true,
            message: "Route and form unlinked successfully"
          }}
        {0, _} ->
          {:error, %{code: "not_found", message: "Link not found"}}
      end
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Route or form not found"}}
    end
  end
  
  defp link_route_plugin(args) do
    with {:ok, _route} <- get_route_by_id(args["route_id"]),
         {:ok, _plugin} <- get_plugin_by_id(args["plugin_id"]) do
      
      link_attrs = %{
        route_id: args["route_id"],
        initial_process_id: args["plugin_id"]
      }
      
      case ServiceManager.Repo.insert(%RouteProcessLink{} |> RouteProcessLink.changeset(link_attrs)) do
        {:ok, _link} ->
          {:ok, %{
            success: true,
            message: "Route and plugin linked successfully"
          }}
        {:error, changeset} ->
          {:error, %{
            code: "link_error",
            message: "Failed to link route and plugin",
            errors: format_changeset_errors(changeset)
          }}
      end
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Route or plugin not found"}}
    end
  end
  
  defp unlink_route_plugin(args) do
    import Ecto.Query
    
    with {:ok, _route} <- get_route_by_id(args["route_id"]) do
      query = from rpl in RouteProcessLink,
              where: rpl.route_id == ^args["route_id"]
      
      case ServiceManager.Repo.delete_all(query) do
        {count, _} when count > 0 ->
          {:ok, %{
            success: true,
            message: "Route and plugin unlinked successfully"
          }}
        {0, _} ->
          {:error, %{code: "not_found", message: "Link not found"}}
      end
    else
      {:error, :not_found} -> 
        {:error, %{code: "not_found", message: "Route not found"}}
    end
  end
  
  defp get_entity_connections(args) do
    entity_type = args["entity_type"]
    entity_id = args["entity_id"]
    
    connections = case entity_type do
      "route" -> get_route_connections(entity_id)
      "form" -> get_form_connections(entity_id)
      "plugin" -> get_plugin_connections(entity_id)
      _ -> {:error, "Invalid entity type"}
    end
    
    case connections do
      {:error, reason} -> 
        {:error, %{code: "invalid_request", message: reason}}
      connections ->
        {:ok, %{
          success: true,
          entity_type: entity_type,
          entity_id: entity_id,
          connections: connections
        }}
    end
  end
  
  # Helper Functions
  
  defp get_route_by_id(id) do
    case DynamicRouteManager.get_route(id) do
      nil -> {:error, :not_found}
      route -> {:ok, route}
    end
  end
  
  defp get_form_by_id(id) do
    case DynamicFormsManager.get_form(id) do
      nil -> {:error, :not_found}
      form -> {:ok, form}
    end
  end
  
  defp get_plugin_by_id(id) do
    case ProcessManager.get_process(id) do
      nil -> {:error, :not_found}
      plugin -> {:ok, plugin}
    end
  end
  
  defp rename_form_keys(attrs) do
    attrs
    |> Map.put("form", attrs["form_schema"])
    |> Map.put("validation_schema", attrs["validation_schema"])
    |> Map.drop(["form_schema"])
  end
  
  defp format_route_response(route) do
    %{
      id: route.id,
      name: route.name,
      method: route.method,
      path: route.path,
      enabled: route.enabled,
      category: route.category,
      group_name: route.group_name,
      priority: route.priority,
      tags: route.tags || [],
      description: route.description
    }
  end
  
  defp format_form_response(form) do
    %{
      id: form.id,
      name: form.name,
      description: form.description,
      http_method: form.http_method,
      required: form.required,
      field_count: length(form.form["fields"] || [])
    }
  end
  
  defp format_plugin_response(plugin) do
    %{
      id: plugin.id,
      name: plugin.name,
      description: plugin.description,
      category: plugin.category,
      group: plugin.group,
      plugin_type: plugin.plugin_type,
      version: plugin.version,
      author: plugin.author
    }
  end
  
  defp format_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {message, opts} ->
      Enum.reduce(opts, message, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
  end
  
  defp get_route_connections(route_id) do
    import Ecto.Query
    
    # Get linked forms
    forms_query = from rf in DynamicRouteForm,
                  join: f in DynamicForm, on: rf.form_id == f.id,
                  where: rf.route_id == ^route_id,
                  select: %{type: "form", id: f.id, name: f.name}
    
    # Get linked plugins
    plugins_query = from rpl in RouteProcessLink,
                    join: p in DynamicProcess, on: rpl.initial_process_id == p.id,
                    where: rpl.route_id == ^route_id,
                    select: %{type: "plugin", id: p.id, name: p.name}
    
    forms = ServiceManager.Repo.all(forms_query)
    plugins = ServiceManager.Repo.all(plugins_query)
    
    forms ++ plugins
  end
  
  defp get_form_connections(form_id) do
    import Ecto.Query
    
    query = from rf in DynamicRouteForm,
            join: r in DynamicRoute, on: rf.route_id == r.id,
            where: rf.form_id == ^form_id,
            select: %{type: "route", id: r.id, name: r.name}
    
    ServiceManager.Repo.all(query)
  end
  
  defp get_plugin_connections(plugin_id) do
    import Ecto.Query
    
    query = from rpl in RouteProcessLink,
            join: r in DynamicRoute, on: rpl.route_id == r.id,
            where: rpl.initial_process_id == ^plugin_id,
            select: %{type: "route", id: r.id, name: r.name}
    
    ServiceManager.Repo.all(query)
  end
end