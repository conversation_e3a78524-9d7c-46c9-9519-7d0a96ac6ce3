defmodule ServiceManager.MCP.Schemas do
  @moduledoc """
  MCP Schemas module for validation and schema management.
  
  Provides JSON Schema validation for:
  - MCP protocol messages
  - Tool parameters
  - Resource data
  - System configurations
  """
  
  @doc """
  Validate MCP protocol message
  """
  def validate_mcp_message(message) do
    case message do
      %{"jsonrpc" => "2.0", "method" => method} when is_binary(method) ->
        {:ok, message}
      _ ->
        {:error, "Invalid MCP message format"}
    end
  end
  
  @doc """
  Validate route creation parameters
  """
  def validate_route_params(params) do
    schema = %{
      "type" => "object",
      "properties" => %{
        "name" => %{"type" => "string", "minLength" => 1, "maxLength" => 255},
        "method" => %{"type" => "string", "enum" => ["GET", "POST", "PUT", "PATCH", "DELETE"]},
        "path" => %{"type" => "string", "minLength" => 1, "pattern" => "^/"},
        "category" => %{"type" => "string", "maxLength" => 100},
        "group_name" => %{"type" => "string", "maxLength" => 100},
        "description" => %{"type" => "string", "maxLength" => 1000},
        "priority" => %{"type" => "integer", "minimum" => 1, "maximum" => 10},
        "tags" => %{"type" => "array", "items" => %{"type" => "string"}},
        "enabled" => %{"type" => "boolean"}
      },
      "required" => ["name", "method", "path"],
      "additionalProperties" => false
    }
    
    validate_against_schema(params, schema)
  end
  
  @doc """
  Validate form creation parameters
  """
  def validate_form_params(params) do
    schema = %{
      "type" => "object",
      "properties" => %{
        "name" => %{"type" => "string", "minLength" => 1, "maxLength" => 255},
        "description" => %{"type" => "string", "maxLength" => 1000},
        "http_method" => %{"type" => "string", "enum" => ["GET", "POST", "PUT", "PATCH", "DELETE"]},
        "form_schema" => %{"type" => "object"},
        "validation_schema" => %{"type" => "object"},
        "required" => %{"type" => "boolean"}
      },
      "required" => ["name", "http_method", "form_schema", "validation_schema"],
      "additionalProperties" => false
    }
    
    validate_against_schema(params, schema)
  end
  
  @doc """
  Validate plugin creation parameters
  """
  def validate_plugin_params(params) do
    schema = %{
      "type" => "object",
      "properties" => %{
        "name" => %{"type" => "string", "minLength" => 1, "maxLength" => 255},
        "description" => %{"type" => "string", "maxLength" => 1000},
        "code" => %{"type" => "string", "minLength" => 1},
        "category" => %{"type" => "string", "maxLength" => 100},
        "group" => %{"type" => "string", "maxLength" => 100},
        "plugin_type" => %{"type" => "string", "enum" => ["system", "public", "protected", "private", "enterprise"]},
        "version" => %{"type" => "string", "pattern" => "^\\d+\\.\\d+\\.\\d+$"},
        "author" => %{"type" => "string", "maxLength" => 255},
        "tags" => %{"type" => "array", "items" => %{"type" => "string"}},
        "expected_params" => %{"type" => "object"}
      },
      "required" => ["name", "code"],
      "additionalProperties" => false
    }
    
    validate_against_schema(params, schema)
  end
  
  @doc """
  Validate linking parameters
  """
  def validate_link_params(params) do
    schema = %{
      "type" => "object",
      "properties" => %{
        "route_id" => %{"type" => "integer", "minimum" => 1},
        "form_id" => %{"type" => "integer", "minimum" => 1},
        "plugin_id" => %{"type" => "integer", "minimum" => 1}
      },
      "additionalProperties" => false
    }
    
    validate_against_schema(params, schema)
  end
  
  @doc """
  Validate form schema structure
  """
  def validate_form_schema(form_schema) do
    schema = %{
      "type" => "object",
      "properties" => %{
        "fields" => %{
          "type" => "array",
          "items" => %{
            "type" => "object",
            "properties" => %{
              "name" => %{"type" => "string", "minLength" => 1},
              "type" => %{"type" => "string", "enum" => ["text", "number", "email", "password", "select", "checkbox", "radio", "textarea", "date", "file"]},
              "label" => %{"type" => "string"},
              "placeholder" => %{"type" => "string"},
              "required" => %{"type" => "boolean"},
              "validation" => %{"type" => "object"},
              "options" => %{"type" => "array"}
            },
            "required" => ["name", "type"],
            "additionalProperties" => true
          }
        },
        "layout" => %{"type" => "object"},
        "metadata" => %{"type" => "object"}
      },
      "required" => ["fields"],
      "additionalProperties" => true
    }
    
    validate_against_schema(form_schema, schema)
  end
  
  @doc """
  Validate JSON Schema structure
  """
  def validate_json_schema(validation_schema) do
    # Basic JSON Schema validation
    required_props = ["type"]
    
    case validation_schema do
      %{"type" => type} when type in ["object", "array", "string", "number", "integer", "boolean"] ->
        {:ok, validation_schema}
      _ ->
        {:error, "Invalid JSON Schema: missing or invalid 'type' property"}
    end
  end
  
  @doc """
  Validate plugin code structure
  """
  def validate_plugin_code(code) when is_binary(code) do
    cond do
      String.contains?(code, "def process") ->
        # Basic validation that the code contains a process function
        if validate_elixir_syntax(code) do
          {:ok, code}
        else
          {:error, "Invalid Elixir syntax in plugin code"}
        end
      
      true ->
        {:error, "Plugin code must contain a 'process' function"}
    end
  end
  
  def validate_plugin_code(_), do: {:error, "Plugin code must be a string"}
  
  @doc """
  Validate API path pattern
  """
  def validate_api_path(path) when is_binary(path) do
    cond do
      not String.starts_with?(path, "/") ->
        {:error, "API path must start with '/'"}
      
      String.contains?(path, "//") ->
        {:error, "API path cannot contain consecutive slashes"}
      
      String.match?(path, ~r/[^a-zA-Z0-9\-_\/{}:]/) ->
        {:error, "API path contains invalid characters"}
      
      String.length(path) > 255 ->
        {:error, "API path is too long (max 255 characters)"}
      
      true ->
        {:ok, path}
    end
  end
  
  def validate_api_path(_), do: {:error, "API path must be a string"}
  
  @doc """
  Validate HTTP method
  """
  def validate_http_method(method) when is_binary(method) do
    valid_methods = ["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"]
    
    if String.upcase(method) in valid_methods do
      {:ok, String.upcase(method)}
    else
      {:error, "Invalid HTTP method. Must be one of: #{Enum.join(valid_methods, ", ")}"}
    end
  end
  
  def validate_http_method(_), do: {:error, "HTTP method must be a string"}
  
  @doc """
  Validate entity ID
  """
  def validate_entity_id(id) when is_integer(id) and id > 0, do: {:ok, id}
  def validate_entity_id(id) when is_binary(id) do
    case Integer.parse(id) do
      {parsed_id, ""} when parsed_id > 0 -> {:ok, parsed_id}
      _ -> {:error, "Invalid entity ID"}
    end
  end
  def validate_entity_id(_), do: {:error, "Entity ID must be a positive integer"}
  
  @doc """
  Validate priority value
  """
  def validate_priority(priority) when is_integer(priority) and priority >= 1 and priority <= 10 do
    {:ok, priority}
  end
  def validate_priority(priority) when is_binary(priority) do
    case Integer.parse(priority) do
      {parsed_priority, ""} when parsed_priority >= 1 and parsed_priority <= 10 -> 
        {:ok, parsed_priority}
      _ -> 
        {:error, "Priority must be between 1 and 10"}
    end
  end
  def validate_priority(_), do: {:error, "Priority must be an integer between 1 and 10"}
  
  @doc """
  Validate tags array
  """
  def validate_tags(tags) when is_list(tags) do
    if Enum.all?(tags, &is_binary/1) and length(tags) <= 50 do
      cleaned_tags = tags
      |> Enum.map(&String.trim/1)
      |> Enum.filter(fn tag -> String.length(tag) > 0 end)
      |> Enum.uniq()
      
      {:ok, cleaned_tags}
    else
      {:error, "Tags must be a list of strings (max 50 tags)"}
    end
  end
  def validate_tags(nil), do: {:ok, []}
  def validate_tags(_), do: {:error, "Tags must be a list of strings"}
  
  @doc """
  Validate version string
  """
  def validate_version(version) when is_binary(version) do
    if Regex.match?(~r/^\d+\.\d+\.\d+$/, version) do
      {:ok, version}
    else
      {:error, "Version must follow semantic versioning format (e.g., 1.0.0)"}
    end
  end
  def validate_version(_), do: {:error, "Version must be a string"}
  
  @doc """
  Validate plugin type
  """
  def validate_plugin_type(plugin_type) when is_binary(plugin_type) do
    valid_types = ["system", "public", "protected", "private", "enterprise"]
    
    if plugin_type in valid_types do
      {:ok, plugin_type}
    else
      {:error, "Invalid plugin type. Must be one of: #{Enum.join(valid_types, ", ")}"}
    end
  end
  def validate_plugin_type(_), do: {:error, "Plugin type must be a string"}
  
  @doc """
  Sanitize input string
  """
  def sanitize_string(input) when is_binary(input) do
    input
    |> String.trim()
    |> String.replace(~r/[<>\"']/, "")  # Remove potentially dangerous characters
    |> String.slice(0, 10000)  # Limit length
  end
  def sanitize_string(input), do: input
  
  @doc """
  Validate and sanitize description
  """
  def validate_description(description) when is_binary(description) do
    sanitized = sanitize_string(description)
    
    if String.length(sanitized) <= 1000 do
      {:ok, sanitized}
    else
      {:error, "Description is too long (max 1000 characters)"}
    end
  end
  def validate_description(nil), do: {:ok, nil}
  def validate_description(_), do: {:error, "Description must be a string"}
  
  # Private Functions
  
  defp validate_against_schema(data, schema) do
    # This is a simplified validation implementation
    # In a production environment, you might want to use a proper JSON Schema validator
    case validate_required_fields(data, schema) do
      {:ok, data} -> validate_field_types(data, schema)
      error -> error
    end
  end
  
  defp validate_required_fields(data, %{"required" => required_fields}) do
    missing_fields = required_fields -- Map.keys(data)
    
    if Enum.empty?(missing_fields) do
      {:ok, data}
    else
      {:error, "Missing required fields: #{Enum.join(missing_fields, ", ")}"}
    end
  end
  defp validate_required_fields(data, _schema), do: {:ok, data}
  
  defp validate_field_types(data, %{"properties" => properties}) do
    errors = Enum.reduce(data, [], fn {key, value}, acc ->
      case Map.get(properties, key) do
        nil -> acc  # Unknown fields are allowed by default
        field_schema -> 
          case validate_field_type(value, field_schema) do
            {:ok, _} -> acc
            {:error, error} -> ["#{key}: #{error}" | acc]
          end
      end
    end)
    
    if Enum.empty?(errors) do
      {:ok, data}
    else
      {:error, "Validation errors: #{Enum.join(errors, ", ")}"}
    end
  end
  defp validate_field_types(data, _schema), do: {:ok, data}
  
  defp validate_field_type(value, %{"type" => "string"}) when is_binary(value), do: {:ok, value}
  defp validate_field_type(value, %{"type" => "integer"}) when is_integer(value), do: {:ok, value}
  defp validate_field_type(value, %{"type" => "number"}) when is_number(value), do: {:ok, value}
  defp validate_field_type(value, %{"type" => "boolean"}) when is_boolean(value), do: {:ok, value}
  defp validate_field_type(value, %{"type" => "array"}) when is_list(value), do: {:ok, value}
  defp validate_field_type(value, %{"type" => "object"}) when is_map(value), do: {:ok, value}
  defp validate_field_type(value, %{"enum" => valid_values}) do
    if value in valid_values do
      {:ok, value}
    else
      {:error, "must be one of #{inspect(valid_values)}"}
    end
  end
  defp validate_field_type(_value, %{"type" => expected_type}) do
    {:error, "must be of type #{expected_type}"}
  end
  defp validate_field_type(value, _schema), do: {:ok, value}
  
  defp validate_elixir_syntax(code) do
    try do
      Code.string_to_quoted(code)
      true
    rescue
      _ -> false
    end
  end
end