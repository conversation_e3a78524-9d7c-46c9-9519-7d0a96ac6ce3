defmodule ServiceManager.MCP.Prompts do
  @moduledoc """
  MCP Prompts module for AI-assisted management of dynamic forms system.
  
  Provides intelligent prompts for:
  - Route creation and configuration guidance
  - Form schema generation and validation
  - Plugin code generation and testing
  - End-to-end workflow creation
  - Best practices and optimization suggestions
  """
  
  use GenServer
  require Logger
  
  alias ServiceManager.Routing.DynamicRouteManager
  alias ServiceManager.Forms.DynamicFormsManager
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
  
  # GenServer API
  
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end
  
  # Client API
  
  @doc """
  List all available prompts
  """
  def list_all() do
    GenServer.call(__MODULE__, :list_all)
  end
  
  @doc """
  Get a specific prompt with arguments
  """
  def get_prompt(name, args) do
    GenServer.call(__MODULE__, {:get_prompt, name, args})
  end
  
  # GenServer Callbacks
  
  @impl true
  def init(_opts) do
    Logger.info("Starting MCP Prompts manager")
    {:ok, %{}}
  end
  
  @impl true
  def handle_call(:list_all, _from, state) do
    prompts = [
      %{
        name: "route_designer",
        description: "AI assistant for designing and creating new API routes with proper categorization and best practices",
        arguments: [
          %{name: "purpose", description: "What the route should accomplish", required: true},
          %{name: "data_type", description: "Type of data the route will handle", required: false},
          %{name: "http_method", description: "Preferred HTTP method", required: false},
          %{name: "existing_routes", description: "Consider existing routes for consistency", required: false}
        ]
      },
      %{
        name: "form_builder",
        description: "Generate comprehensive JSON form schemas with validation rules based on API requirements",
        arguments: [
          %{name: "api_purpose", description: "Purpose of the API endpoint", required: true},
          %{name: "data_fields", description: "List of expected data fields", required: true},
          %{name: "validation_level", description: "Strict, moderate, or lenient validation", required: false},
          %{name: "form_type", description: "Type of form (create, update, search, etc.)", required: false}
        ]
      },
      %{
        name: "plugin_generator",
        description: "Generate plugin code for business logic processing with proper error handling and structure",
        arguments: [
          %{name: "functionality", description: "What the plugin should do", required: true},
          %{name: "input_data", description: "Expected input parameters", required: true},
          %{name: "output_format", description: "Expected output format", required: false},
          %{name: "external_apis", description: "External APIs to integrate", required: false},
          %{name: "complexity_level", description: "Simple, intermediate, or advanced", required: false}
        ]
      },
      %{
        name: "workflow_creator",
        description: "Create complete end-to-end API workflows connecting routes, forms, and plugins",
        arguments: [
          %{name: "business_process", description: "Business process to implement", required: true},
          %{name: "user_journey", description: "User journey or flow description", required: true},
          %{name: "data_sources", description: "External data sources or APIs", required: false},
          %{name: "security_requirements", description: "Security and validation requirements", required: false}
        ]
      },
      %{
        name: "route_validator",
        description: "Validate and suggest improvements for existing route configurations",
        arguments: [
          %{name: "route_id", description: "ID of the route to validate", required: true},
          %{name: "check_type", description: "Type of validation (security, performance, best_practices)", required: false}
        ]
      },
      %{
        name: "form_optimizer",
        description: "Optimize form schemas for better user experience and validation",
        arguments: [
          %{name: "form_id", description: "ID of the form to optimize", required: true},
          %{name: "optimization_goal", description: "Goal (user_experience, performance, validation)", required: false}
        ]
      },
      %{
        name: "plugin_tester",
        description: "Generate comprehensive test cases and scenarios for plugin validation",
        arguments: [
          %{name: "plugin_id", description: "ID of the plugin to test", required: true},
          %{name: "test_scenarios", description: "Specific scenarios to test", required: false}
        ]
      },
      %{
        name: "api_documenter",
        description: "Generate comprehensive API documentation for routes with forms and plugins",
        arguments: [
          %{name: "route_ids", description: "List of route IDs to document", required: true},
          %{name: "doc_format", description: "Documentation format (openapi, markdown, postman)", required: false}
        ]
      },
      %{
        name: "integration_helper",
        description: "Help integrate existing components and suggest optimal connections",
        arguments: [
          %{name: "components", description: "Components to integrate (routes, forms, plugins)", required: true},
          %{name: "integration_goal", description: "What the integration should achieve", required: true}
        ]
      },
      %{
        name: "troubleshooter",
        description: "Diagnose and suggest fixes for common issues in the dynamic forms system",
        arguments: [
          %{name: "issue_description", description: "Description of the problem", required: true},
          %{name: "error_logs", description: "Error logs or messages", required: false},
          %{name: "affected_components", description: "Which components are affected", required: false}
        ]
      }
    ]
    
    {:reply, {:ok, prompts}, state}
  end
  
  @impl true
  def handle_call({:get_prompt, name, args}, _from, state) do
    result = case name do
      "route_designer" -> route_designer_prompt(args)
      "form_builder" -> form_builder_prompt(args)
      "plugin_generator" -> plugin_generator_prompt(args)
      "workflow_creator" -> workflow_creator_prompt(args)
      "route_validator" -> route_validator_prompt(args)
      "form_optimizer" -> form_optimizer_prompt(args)
      "plugin_tester" -> plugin_tester_prompt(args)
      "api_documenter" -> api_documenter_prompt(args)
      "integration_helper" -> integration_helper_prompt(args)
      "troubleshooter" -> troubleshooter_prompt(args)
      _ -> {:error, %{code: "unknown_prompt", message: "Unknown prompt: #{name}"}}
    end
    
    {:reply, result, state}
  end
  
  # Prompt Implementations
  
  defp route_designer_prompt(args) do
    purpose = args["purpose"]
    data_type = args["data_type"] || "general"
    http_method = args["http_method"]
    
    # Get existing routes for context if requested
    existing_context = if args["existing_routes"] do
      routes = DynamicRouteManager.list_routes()
      build_existing_routes_context(routes)
    else
      ""
    end
    
    prompt = """
    # API Route Design Assistant
    
    You are an expert API designer helping to create a new dynamic route for a banking/financial services platform.
    
    ## Request Details:
    - **Purpose**: #{purpose}
    - **Data Type**: #{data_type}
    - **Preferred HTTP Method**: #{http_method || "Not specified"}
    
    #{existing_context}
    
    ## Your Task:
    Design an optimal API route configuration that follows REST principles and banking industry best practices.
    
    ## Consider:
    1. **RESTful Design**: Proper HTTP method selection, resource-oriented URLs
    2. **Banking Security**: Authentication, authorization, data validation requirements
    3. **Categorization**: Appropriate category and group for organization
    4. **Path Structure**: Clean, predictable URL patterns
    5. **Priority**: Route priority for conflict resolution
    6. **Error Handling**: Proper error responses and status codes
    
    ## Provide:
    1. **Recommended Route Configuration**:
       - Name
       - HTTP Method
       - Path Pattern
       - Category
       - Group Name
       - Priority (1-10)
       - Description
       - Tags
    
    2. **Best Practices Explanation**:
       - Why this design is optimal
       - Security considerations
       - Integration patterns
    
    3. **Form Requirements**:
       - What form validation might be needed
       - Required vs optional fields
    
    4. **Plugin Suggestions**:
       - Business logic requirements
       - External integrations needed
    
    Please provide a comprehensive route design with detailed explanations.
    """
    
    {:ok, %{
      prompt: prompt,
      context: %{
        purpose: purpose,
        data_type: data_type,
        existing_routes_count: if(existing_context != "", do: count_existing_routes(), else: 0)
      }
    }}
  end
  
  defp form_builder_prompt(args) do
    api_purpose = args["api_purpose"]
    data_fields = args["data_fields"]
    validation_level = args["validation_level"] || "moderate"
    form_type = args["form_type"] || "create"
    
    prompt = """
    # Form Schema Generator
    
    You are an expert form designer creating JSON schemas for a banking/financial API system.
    
    ## Requirements:
    - **API Purpose**: #{api_purpose}
    - **Data Fields**: #{data_fields}
    - **Validation Level**: #{validation_level}
    - **Form Type**: #{form_type}
    
    ## Banking Industry Context:
    - Financial data requires strict validation
    - PII (Personally Identifiable Information) needs special handling
    - Regulatory compliance (KYC, AML) considerations
    - Multi-currency support where applicable
    - Audit trail requirements
    
    ## Generate:
    
    1. **Complete Form Schema** (JSON):
    ```json
    {
      "fields": [
        // Your field definitions here
      ],
      "layout": {
        // Form layout configuration
      },
      "metadata": {
        // Form metadata
      }
    }
    ```
    
    2. **Validation Schema** (JSON Schema):
    ```json
    {
      "type": "object",
      "properties": {
        // Your validation rules here
      },
      "required": [],
      "additionalProperties": false
    }
    ```
    
    3. **Field Specifications**:
    - Field types (text, number, email, etc.)
    - Validation rules (min/max length, patterns, etc.)
    - Banking-specific validations (account numbers, amounts, etc.)
    - Required vs optional fields
    - Default values where appropriate
    
    4. **Security Considerations**:
    - Input sanitization requirements
    - Data encryption needs
    - Audit logging fields
    
    5. **User Experience**:
    - Field grouping and sections
    - Help text and validation messages
    - Progressive disclosure patterns
    
    Please provide complete, production-ready form and validation schemas with detailed explanations.
    """
    
    {:ok, %{
      prompt: prompt,
      context: %{
        api_purpose: api_purpose,
        validation_level: validation_level,
        form_type: form_type
      }
    }}
  end
  
  defp plugin_generator_prompt(args) do
    functionality = args["functionality"]
    input_data = args["input_data"]
    output_format = args["output_format"] || "JSON response"
    external_apis = args["external_apis"]
    complexity_level = args["complexity_level"] || "intermediate"
    
    prompt = """
    # Plugin Code Generator
    
    You are an expert Elixir developer creating business logic plugins for a banking platform.
    
    ## Requirements:
    - **Functionality**: #{functionality}
    - **Input Data**: #{input_data}
    - **Output Format**: #{output_format}
    - **External APIs**: #{external_apis || "None specified"}
    - **Complexity Level**: #{complexity_level}
    
    ## Banking Platform Context:
    - Built with Elixir/Phoenix
    - Uses GenServer patterns for state management
    - Integrates with T24 core banking system
    - Requires comprehensive error handling
    - All transactions must be auditable
    - Multi-currency support
    - Real-time processing capabilities
    
    ## Generate:
    
    1. **Complete Plugin Code**:
    ```elixir
    defmodule YourPlugin do
      @moduledoc \"\"\"
      Your plugin description here
      \"\"\"
      
      def process(params) do
        # Your implementation here
      end
      
      # Helper functions as needed
    end
    ```
    
    2. **Error Handling**:
    - Proper error cases and responses
    - Validation of input parameters
    - Graceful degradation strategies
    - Logging and monitoring
    
    3. **Integration Patterns**:
    - How to call external APIs (if applicable)
    - Database operations (if needed)
    - State management
    - Async processing (if required)
    
    4. **Testing Strategy**:
    - Unit test examples
    - Integration test scenarios
    - Mock data for testing
    
    5. **Performance Considerations**:
    - Efficiency optimizations
    - Memory usage
    - Concurrent processing
    
    6. **Configuration**:
    - Expected parameters schema
    - Environment-specific settings
    - Feature flags support
    
    Please provide production-ready Elixir code with comprehensive error handling and documentation.
    """
    
    {:ok, %{
      prompt: prompt,
      context: %{
        functionality: functionality,
        complexity_level: complexity_level,
        has_external_apis: !is_nil(external_apis)
      }
    }}
  end
  
  defp workflow_creator_prompt(args) do
    business_process = args["business_process"]
    user_journey = args["user_journey"]
    data_sources = args["data_sources"]
    security_requirements = args["security_requirements"]
    
    # Get system overview for context
    system_stats = get_system_overview()
    
    prompt = """
    # End-to-End Workflow Creator
    
    You are a senior system architect designing complete API workflows for a banking platform.
    
    ## Business Requirements:
    - **Business Process**: #{business_process}
    - **User Journey**: #{user_journey}
    - **Data Sources**: #{data_sources || "Internal systems only"}
    - **Security Requirements**: #{security_requirements || "Standard banking security"}
    
    ## Current System Overview:
    #{system_stats}
    
    ## Design Complete Workflow:
    
    1. **Route Design**:
    ```json
    {
      "name": "Your Route Name",
      "method": "POST",
      "path": "/api/v1/your-endpoint",
      "category": "Your Category",
      "group_name": "Your Group",
      "description": "Detailed description"
    }
    ```
    
    2. **Form Schema**:
    ```json
    {
      "fields": [
        // Complete form definition
      ]
    }
    ```
    
    3. **Validation Rules**:
    ```json
    {
      "type": "object",
      "properties": {
        // Validation schema
      }
    }
    ```
    
    4. **Plugin Logic**:
    ```elixir
    # Business logic implementation
    def process(params) do
      # Your workflow steps
    end
    ```
    
    5. **Integration Plan**:
    - How components connect
    - Data flow diagrams
    - Error handling strategies
    - Security checkpoints
    
    6. **Testing Strategy**:
    - Test scenarios
    - Mock data requirements
    - Integration testing approach
    
    7. **Deployment Checklist**:
    - Configuration requirements
    - Monitoring setup
    - Performance benchmarks
    
    Provide a complete, implementable workflow with all necessary components and detailed implementation guidance.
    """
    
    {:ok, %{
      prompt: prompt,
      context: %{
        business_process: business_process,
        system_stats: system_stats
      }
    }}
  end
  
  defp route_validator_prompt(args) do
    route_id = args["route_id"]
    check_type = args["check_type"] || "best_practices"
    
    # Get route details
    route = case DynamicRouteManager.get_route(route_id) do
      nil -> nil
      route -> route
    end
    
    if route do
      route_json = Jason.encode!(%{
        id: route.id,
        name: route.name,
        method: route.method,
        path: route.path,
        enabled: route.enabled,
        category: route.category,
        group_name: route.group_name,
        description: route.description
      })
      
      prompt = """
      # Route Configuration Validator
      
      You are a senior API architect reviewing route configurations for a banking platform.
      
      ## Route to Validate:
      ```json
      #{route_json}
      ```
      
      ## Validation Type: #{check_type}
      
      ## Analysis Areas:
      
      ### Security Review:
      - Path injection vulnerabilities
      - Authentication/authorization requirements
      - Data exposure risks
      - Input validation needs
      
      ### Performance Analysis:
      - Route efficiency
      - Caching opportunities
      - Database query optimization
      - Rate limiting requirements
      
      ### Best Practices Check:
      - RESTful design compliance
      - Naming conventions
      - HTTP method appropriateness
      - Response format consistency
      
      ### Banking Compliance:
      - Regulatory requirement adherence
      - Audit trail completeness
      - Data protection compliance
      - Transaction integrity
      
      ## Provide:
      
      1. **Validation Results**:
         - ✅ Passes / ❌ Fails for each area
         - Specific issues identified
         - Risk severity levels
      
      2. **Improvement Recommendations**:
         - Specific changes to make
         - Priority order (high/medium/low)
         - Implementation guidance
      
      3. **Code Examples**:
         - Before/after configurations
         - Implementation samples
      
      4. **Related Components**:
         - Form validation suggestions
         - Plugin integration recommendations
         - Monitoring requirements
      
      Please provide a comprehensive validation report with actionable recommendations.
      """
      
      {:ok, %{
        prompt: prompt,
        context: %{
          route_id: route_id,
          route_name: route.name,
          check_type: check_type
        }
      }}
    else
      {:error, %{code: "route_not_found", message: "Route with ID #{route_id} not found"}}
    end
  end
  
  defp form_optimizer_prompt(args) do
    form_id = args["form_id"]
    optimization_goal = args["optimization_goal"] || "user_experience"
    
    # Get form details
    form = case DynamicFormsManager.get_form(form_id) do
      nil -> nil
      form -> form
    end
    
    if form do
      field_count = length(form.form["fields"] || [])
      
      prompt = """
      # Form Schema Optimizer
      
      You are a UX expert and form designer optimizing forms for a banking application.
      
      ## Form to Optimize:
      - **Name**: #{form.name}
      - **HTTP Method**: #{form.http_method}
      - **Field Count**: #{field_count}
      - **Required**: #{form.required}
      - **Description**: #{form.description || "No description"}
      
      ## Current Form Schema:
      ```json
      #{Jason.encode!(form.form, pretty: true)}
      ```
      
      ## Current Validation Schema:
      ```json
      #{Jason.encode!(form.validation_schema, pretty: true)}
      ```
      
      ## Optimization Goal: #{optimization_goal}
      
      ## Analysis Framework:
      
      ### User Experience:
      - Form length and complexity
      - Field ordering and grouping
      - Validation feedback clarity
      - Progressive disclosure opportunities
      - Mobile responsiveness
      
      ### Performance:
      - Validation efficiency
      - Field rendering optimization
      - Client-side vs server-side validation
      - Real-time validation impact
      
      ### Validation Quality:
      - Rule completeness
      - Error message clarity
      - Edge case handling
      - Security validation
      
      ### Banking Specifics:
      - Regulatory compliance
      - Data accuracy requirements
      - Fraud prevention measures
      - Accessibility standards
      
      ## Provide:
      
      1. **Optimization Analysis**:
         - Current strengths
         - Identified weaknesses
         - Impact assessment
      
      2. **Improved Form Schema**:
         ```json
         // Your optimized form schema
         ```
      
      3. **Enhanced Validation Rules**:
         ```json
         // Your improved validation schema
         ```
      
      4. **Implementation Guide**:
         - Step-by-step changes
         - Testing recommendations
         - Rollback strategy
      
      5. **Performance Metrics**:
         - Expected improvements
         - Measurement strategies
         - Success criteria
      
      Please provide a comprehensive optimization plan with measurable improvements.
      """
      
      {:ok, %{
        prompt: prompt,
        context: %{
          form_id: form_id,
          form_name: form.name,
          field_count: field_count,
          optimization_goal: optimization_goal
        }
      }}
    else
      {:error, %{code: "form_not_found", message: "Form with ID #{form_id} not found"}}
    end
  end
  
  defp plugin_tester_prompt(args) do
    plugin_id = args["plugin_id"]
    test_scenarios = args["test_scenarios"]
    
    # Get plugin details
    plugin = case ProcessManager.get_process(plugin_id) do
      nil -> nil
      plugin -> plugin
    end
    
    if plugin do
      prompt = """
      # Plugin Test Suite Generator
      
      You are a QA engineer creating comprehensive test suites for banking plugins.
      
      ## Plugin to Test:
      - **Name**: #{plugin.name}
      - **Category**: #{plugin.category}
      - **Type**: #{plugin.plugin_type}
      - **Description**: #{plugin.description || "No description"}
      - **Expected Params**: #{Jason.encode!(plugin.expected_params || %{})}
      
      ## Specific Test Scenarios:
      #{test_scenarios || "Generate comprehensive test scenarios"}
      
      ## Test Categories:
      
      ### Unit Tests:
      - Valid input scenarios
      - Invalid input handling
      - Edge cases
      - Boundary conditions
      - Error scenarios
      
      ### Integration Tests:
      - External API interactions
      - Database operations
      - Service dependencies
      - Authentication/authorization
      
      ### Performance Tests:
      - Load testing scenarios
      - Memory usage validation
      - Response time benchmarks
      - Concurrent execution
      
      ### Security Tests:
      - Input validation bypass attempts
      - SQL injection protection
      - Authentication bypasses
      - Data exposure risks
      
      ### Banking-Specific Tests:
      - Transaction integrity
      - Compliance validation
      - Audit trail generation
      - Currency handling
      - Regulatory requirements
      
      ## Generate:
      
      1. **Test Plan**:
         - Test scenarios matrix
         - Priority levels
         - Success criteria
         - Risk assessment
      
      2. **Test Data**:
         ```elixir
         # Valid test cases
         @valid_test_data [
           %{
             # Test case data
           }
         ]
         
         # Invalid test cases
         @invalid_test_data [
           %{
             # Error scenarios
           }
         ]
         ```
      
      3. **Test Implementation**:
         ```elixir
         defmodule YourPluginTest do
           use ExUnit.Case
           
           # Your test implementations
         end
         ```
      
      4. **Mock Services**:
         - External API mocks
         - Database mocks
         - Service stubs
      
      5. **Performance Benchmarks**:
         - Expected performance metrics
         - Load testing scripts
         - Memory usage tests
      
      Please provide a complete, executable test suite with realistic banking scenarios.
      """
      
      {:ok, %{
        prompt: prompt,
        context: %{
          plugin_id: plugin_id,
          plugin_name: plugin.name,
          plugin_category: plugin.category
        }
      }}
    else
      {:error, %{code: "plugin_not_found", message: "Plugin with ID #{plugin_id} not found"}}
    end
  end
  
  defp api_documenter_prompt(args) do
    route_ids = args["route_ids"]
    doc_format = args["doc_format"] || "openapi"
    
    # Get route details
    routes = Enum.map(route_ids, fn id ->
      case DynamicRouteManager.get_route(id) do
        nil -> nil
        route -> route
      end
    end)
    |> Enum.filter(& &1)
    
    if length(routes) > 0 do
      routes_summary = Enum.map(routes, fn route ->
        "- #{route.method} #{route.path} (#{route.name})"
      end)
      |> Enum.join("\n")
      
      prompt = """
      # API Documentation Generator
      
      You are a technical writer creating comprehensive API documentation for a banking platform.
      
      ## Routes to Document:
      #{routes_summary}
      
      ## Documentation Format: #{doc_format}
      
      ## Banking API Standards:
      - Security-first approach
      - Comprehensive error handling
      - Audit trail requirements
      - Regulatory compliance notes
      - Multi-currency support
      - Rate limiting information
      
      ## Generate Complete Documentation:
      
      ### OpenAPI Specification (if requested):
      ```yaml
      openapi: 3.0.3
      info:
        title: Banking API
        version: 1.0.0
        description: Comprehensive banking services API
      paths:
        # Your path definitions
      ```
      
      ### For Each Endpoint:
      1. **Overview**:
         - Purpose and use case
         - Business context
         - Security requirements
      
      2. **Request Details**:
         - HTTP method and path
         - Headers required
         - Authentication needs
         - Request body schema
         - Query parameters
      
      3. **Response Details**:
         - Success responses (200, 201, etc.)
         - Error responses (400, 401, 500, etc.)
         - Response schemas
         - Example payloads
      
      4. **Security**:
         - Authentication requirements
         - Authorization levels
         - Rate limiting
         - IP restrictions
      
      5. **Examples**:
         - cURL commands
         - Request/response samples
         - SDK usage examples
         - Postman collection snippets
      
      6. **Error Handling**:
         - Error codes and meanings
         - Troubleshooting guide
         - Common issues
      
      7. **Business Logic**:
         - Validation rules
         - Processing flow
         - Side effects
         - Audit implications
      
      Please provide production-ready API documentation following banking industry standards.
      """
      
      {:ok, %{
        prompt: prompt,
        context: %{
          route_count: length(routes),
          doc_format: doc_format,
          routes: Enum.map(routes, & &1.name)
        }
      }}
    else
      {:error, %{code: "routes_not_found", message: "No valid routes found for documentation"}}
    end
  end
  
  defp integration_helper_prompt(args) do
    components = args["components"]
    integration_goal = args["integration_goal"]
    
    prompt = """
    # Integration Assistant
    
    You are a system integration expert helping connect components in a banking platform.
    
    ## Components to Integrate:
    #{components}
    
    ## Integration Goal:
    #{integration_goal}
    
    ## Integration Patterns:
    
    ### Route-Form Integration:
    - Form validation requirements
    - Data transformation needs
    - Error handling coordination
    
    ### Route-Plugin Integration:
    - Parameter passing strategies
    - Response formatting
    - Error propagation
    
    ### Plugin Chaining:
    - Sequential processing
    - Data flow management
    - State preservation
    
    ### Cross-Component Patterns:
    - Event-driven architectures
    - Message passing
    - State synchronization
    
    ## Provide:
    
    1. **Integration Architecture**:
       - Component relationship diagram
       - Data flow patterns
       - Communication protocols
    
    2. **Implementation Strategy**:
       - Step-by-step integration plan
       - Configuration requirements
       - Code examples
    
    3. **Error Handling**:
       - Failure scenarios
       - Rollback strategies
       - Monitoring requirements
    
    4. **Testing Approach**:
       - Integration test scenarios
       - Mock strategies
       - End-to-end validation
    
    5. **Performance Considerations**:
       - Bottleneck identification
       - Optimization opportunities
       - Scalability planning
    
    Please provide a comprehensive integration plan with implementation guidance.
    """
    
    {:ok, %{
      prompt: prompt,
      context: %{
        integration_goal: integration_goal,
        component_types: extract_component_types(components)
      }
    }}
  end
  
  defp troubleshooter_prompt(args) do
    issue_description = args["issue_description"]
    error_logs = args["error_logs"]
    affected_components = args["affected_components"]
    
    # Get system health overview
    system_health = get_system_health_overview()
    
    prompt = """
    # System Troubleshooter
    
    You are a senior DevOps engineer diagnosing issues in a banking platform.
    
    ## Issue Report:
    **Description**: #{issue_description}
    
    **Error Logs**: 
    ```
    #{error_logs || "No specific error logs provided"}
    ```
    
    **Affected Components**: #{affected_components || "Not specified"}
    
    ## System Health Overview:
    #{system_health}
    
    ## Troubleshooting Framework:
    
    ### Initial Diagnosis:
    1. **Issue Classification**:
       - Performance issue
       - Functional bug
       - Integration failure
       - Security concern
       - Data integrity problem
    
    2. **Impact Assessment**:
       - User-facing impact
       - Business process disruption
       - Security implications
       - Compliance risks
    
    ### Root Cause Analysis:
    3. **Common Causes**:
       - Configuration errors
       - Database issues
       - External API failures
       - Memory/resource exhaustion
       - Network connectivity
    
    4. **System Dependencies**:
       - Route configurations
       - Form validation rules
       - Plugin execution
       - Database connections
       - External service integrations
    
    ### Resolution Strategy:
    5. **Immediate Actions**:
       - Quick fixes/workarounds
       - Service restarts
       - Configuration adjustments
       - Rollback procedures
    
    6. **Long-term Solutions**:
       - Code fixes
       - Architecture improvements
       - Monitoring enhancements
       - Prevention measures
    
    ## Provide:
    
    1. **Diagnosis Results**:
       - Probable root causes
       - Contributing factors
       - Risk assessment
       - Urgency level
    
    2. **Resolution Plan**:
       ```
       Immediate Actions (0-1 hour):
       - Action 1
       - Action 2
       
       Short-term Fixes (1-24 hours):
       - Fix 1
       - Fix 2
       
       Long-term Solutions (1+ days):
       - Solution 1
       - Solution 2
       ```
    
    3. **Verification Steps**:
       - How to confirm the fix
       - Test scenarios
       - Monitoring checks
    
    4. **Prevention Measures**:
       - Process improvements
       - Monitoring enhancements
       - Code quality measures
       - Documentation updates
    
    Please provide a comprehensive troubleshooting analysis with actionable solutions.
    """
    
    {:ok, %{
      prompt: prompt,
      context: %{
        issue_type: classify_issue(issue_description),
        system_health: system_health
      }
    }}
  end
  
  # Helper Functions
  
  defp build_existing_routes_context(routes) do
    if length(routes) > 0 do
      categories = routes
      |> Enum.group_by(& &1.category)
      |> Enum.map(fn {cat, cat_routes} ->
           "- #{cat || "Uncategorized"}: #{length(cat_routes)} routes"
         end)
      |> Enum.join("\n")
      
      """
      ## Existing Routes Context:
      **Total Routes**: #{length(routes)}
      **Categories**:
      #{categories}
      """
    else
      "## No existing routes in the system."
    end
  end
  
  defp count_existing_routes() do
    ServiceManager.Repo.aggregate(ServiceManager.Routing.DynamicRoute, :count)
  end
  
  defp get_system_overview() do
    routes_count = ServiceManager.Repo.aggregate(ServiceManager.Routing.DynamicRoute, :count)
    forms_count = ServiceManager.Repo.aggregate(ServiceManager.Forms.DynamicForm, :count)
    plugins_count = ServiceManager.Repo.aggregate(ServiceManager.Schemas.Dynamic.Processes.DynamicProcess, :count)
    
    """
    **System Statistics**:
    - Routes: #{routes_count}
    - Forms: #{forms_count}
    - Plugins: #{plugins_count}
    """
  end
  
  defp get_system_health_overview() do
    # This would typically check various system metrics
    # For now, return basic stats
    """
    **System Health**:
    - Status: Operational
    - Routes Active: #{count_enabled_routes()}
    - Recent Errors: Check logs for details
    - Performance: Normal
    """
  end
  
  defp count_enabled_routes() do
    import Ecto.Query
    ServiceManager.Repo.aggregate(
      from(r in ServiceManager.Routing.DynamicRoute, where: r.enabled == true), 
      :count
    )
  end
  
  defp extract_component_types(components) do
    String.downcase(components)
    |> String.split([",", " ", "and"])
    |> Enum.map(&String.trim/1)
    |> Enum.filter(fn s -> s != "" end)
  end
  
  defp classify_issue(description) do
    description_lower = String.downcase(description)
    
    cond do
      String.contains?(description_lower, ["error", "exception", "crash", "fail"]) -> "error"
      String.contains?(description_lower, ["slow", "performance", "timeout"]) -> "performance"
      String.contains?(description_lower, ["security", "auth", "unauthorized"]) -> "security"
      String.contains?(description_lower, ["data", "database", "query"]) -> "data"
      true -> "general"
    end
  end
end