defmodule ServiceManager.MCP.StdioServer do
  @moduledoc """
  MCP Server that communicates via stdio using JSON-RPC protocol.
  This is the entry point for MCP clients like LM Studio.
  """
  
  require Logger
  
  alias ServiceManager.MCP.{Server, Resources, Tools, Prompts}
  
  @server_info %{
    name: "Dynamic Forms MCP Server",
    version: "1.0.0"
  }
  
  def start() do
    Logger.info("🚀 Starting MCP Stdio Server...")
    
    # Ensure the main application is started
    case Application.ensure_all_started(:service_manager) do
      {:ok, _} ->
        Logger.info("✅ ServiceManager application started")
        
        # Wait for MCP components to be ready
        wait_for_mcp_components()
        
        # Start stdio loop
        Logger.info("🔄 Starting stdio communication loop...")
        stdio_loop()
        
      {:error, reason} ->
        Logger.error("❌ Failed to start application: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  defp wait_for_mcp_components() do
    Logger.info("⏳ Waiting for MCP components to be ready...")
    
    # Wait for components with retries
    wait_for_component(ServiceManager.MCP.Server, "MCP Server", 10)
    wait_for_component(ServiceManager.MCP.Resources, "MCP Resources", 5)
    wait_for_component(ServiceManager.MCP.Tools, "MCP Tools", 5)
    wait_for_component(ServiceManager.MCP.Prompts, "MCP Prompts", 5)
    
    Logger.info("✅ All MCP components are ready")
  end
  
  defp wait_for_component(module, name, retries) when retries > 0 do
    case GenServer.whereis(module) do
      nil ->
        Logger.info("⏳ Waiting for #{name}... (#{retries} retries left)")
        Process.sleep(1000)
        wait_for_component(module, name, retries - 1)
      _pid ->
        Logger.info("✅ #{name} is ready")
    end
  end
  
  defp wait_for_component(module, name, 0) do
    Logger.error("❌ #{name} (#{module}) failed to start after retries")
    System.halt(1)
  end
  
  defp stdio_loop() do
    # Read from stdin
    case IO.read(:stdio, :line) do
      :eof ->
        Logger.info("📤 EOF received, shutting down...")
        System.halt(0)
        
      {:error, reason} ->
        Logger.error("❌ Error reading from stdin: #{inspect(reason)}")
        System.halt(1)
        
      data when is_binary(data) ->
        data
        |> String.trim()
        |> handle_request()
        
        # Continue the loop
        stdio_loop()
    end
  end
  
  defp handle_request("") do
    # Ignore empty lines
    :ok
  end
  
  defp handle_request(json_string) do
    Logger.debug("📥 Received: #{json_string}")
    
    case Jason.decode(json_string) do
      {:ok, request} ->
        response = process_request(request)
        send_response(response)
        
      {:error, reason} ->
        Logger.error("❌ JSON decode error: #{inspect(reason)}")
        error_response = %{
          jsonrpc: "2.0",
          id: nil,
          error: %{
            code: -32700,
            message: "Parse error",
            data: inspect(reason)
          }
        }
        send_response(error_response)
    end
  end
  
  defp process_request(%{"jsonrpc" => "2.0", "method" => method, "id" => id} = request) do
    Logger.debug("🔄 Processing method: #{method}")
    
    params = Map.get(request, "params", %{})
    
    result = case method do
      "initialize" ->
        handle_initialize(params)
        
      "initialized" ->
        # Client confirms initialization
        Logger.info("✅ Client initialized successfully")
        :ok
        
      "resources/list" ->
        handle_resources_list()
        
      "resources/read" ->
        handle_resources_read(params)
        
      "tools/list" ->
        handle_tools_list()
        
      "tools/call" ->
        handle_tools_call(params)
        
      "prompts/list" ->
        handle_prompts_list()
        
      "prompts/get" ->
        handle_prompts_get(params)
        
      _ ->
        Logger.warn("⚠️  Unknown method: #{method}")
        {:error, %{code: -32601, message: "Method not found"}}
    end
    
    case result do
      {:ok, data} ->
        %{
          jsonrpc: "2.0",
          id: id,
          result: data
        }
        
      {:error, error} ->
        %{
          jsonrpc: "2.0",
          id: id,
          error: error
        }
        
      :ok ->
        %{
          jsonrpc: "2.0",
          id: id,
          result: nil
        }
    end
  end
  
  defp process_request(%{"jsonrpc" => "2.0", "method" => method} = request) do
    # Notification (no id)
    Logger.debug("📢 Processing notification: #{method}")
    params = Map.get(request, "params", %{})
    
    case method do
      "notifications/initialized" ->
        Logger.info("✅ Client sent initialized notification")
      _ ->
        Logger.debug("📢 Unhandled notification: #{method}")
    end
    
    # No response for notifications
    nil
  end
  
  defp process_request(request) do
    Logger.error("❌ Invalid request format: #{inspect(request)}")
    %{
      jsonrpc: "2.0",
      id: nil,
      error: %{
        code: -32600,
        message: "Invalid Request"
      }
    }
  end
  
  defp handle_initialize(_params) do
    Logger.info("🤝 Handling initialize request")
    
    capabilities = %{
      resources: %{
        subscribe: true,
        listChanged: true
      },
      tools: %{},
      prompts: %{},
      logging: %{}
    }
    
    {:ok, %{
      protocolVersion: "2024-11-05",
      serverInfo: @server_info,
      capabilities: capabilities
    }}
  end
  
  defp handle_resources_list() do
    case Resources.list_all() do
      {:ok, resources} -> {:ok, %{resources: resources}}
      error -> error
    end
  end
  
  defp handle_resources_read(%{"uri" => uri}) do
    case Resources.read_resource(uri) do
      {:ok, resource} -> {:ok, %{contents: [resource]}}
      error -> error
    end
  end
  
  defp handle_resources_read(_params) do
    {:error, %{code: -32602, message: "Invalid params: missing uri"}}
  end
  
  defp handle_tools_list() do
    case Tools.list_all() do
      {:ok, tools} -> {:ok, %{tools: tools}}
      error -> error
    end
  end
  
  defp handle_tools_call(%{"name" => name, "arguments" => args}) do
    case Tools.call_tool(name, args) do
      {:ok, result} -> {:ok, result}
      error -> error
    end
  end
  
  defp handle_tools_call(_params) do
    {:error, %{code: -32602, message: "Invalid params: missing name or arguments"}}
  end
  
  defp handle_prompts_list() do
    case Prompts.list_all() do
      {:ok, prompts} -> {:ok, %{prompts: prompts}}
      error -> error
    end
  end
  
  defp handle_prompts_get(%{"name" => name} = params) do
    args = Map.get(params, "arguments", %{})
    case Prompts.get_prompt(name, args) do
      {:ok, prompt} -> {:ok, prompt}
      error -> error
    end
  end
  
  defp handle_prompts_get(_params) do
    {:error, %{code: -32602, message: "Invalid params: missing name"}}
  end
  
  defp send_response(nil) do
    # No response for notifications
    :ok
  end
  
  defp send_response(response) do
    json = Jason.encode!(response)
    Logger.debug("📤 Sending: #{json}")
    IO.puts(json)
    :ok
  end
end
