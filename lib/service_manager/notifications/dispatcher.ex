defmodule ServiceManager.Notifications.Dispatcher do
  use GenServer
  alias ServiceManager.Notifications
  alias ServiceManager.Repo

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  def init(state) do
    {:ok, state}
  end

  def dispatch(notification) do
    GenServer.cast(__MODULE__, {:dispatch, notification})
  end

  def handle_cast({:dispatch, notification}, state) do
    case notification.type do
      :email -> send_email(notification)
      :sms -> send_sms(notification)
      :push -> send_push(notification)
    end

    {:noreply, state}
  end

  defp send_email(notification) do
    # Implement email sending logic here
    # Update notification status after sending
    Notifications.update_email_notification(notification, %{
      status: "sent",
      sent_at: DateTime.utc_now()
    })
  end

  defp send_sms(notification) do
    # Implement SMS sending logic here
    # Update notification status after sending
    Notifications.update_sms_notification(notification, %{
      status: "sent",
      sent_at: DateTime.utc_now()
    })
  end

  defp send_push(notification) do
    # Implement push notification sending logic here
    # Update notification status after sending
    Notifications.update_push_notification(notification, %{
      status: "sent",
      sent_at: DateTime.utc_now()
    })
  end
end
