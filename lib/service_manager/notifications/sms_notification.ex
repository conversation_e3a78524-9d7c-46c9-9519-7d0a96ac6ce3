defmodule ServiceManager.Notifications.SMSNotification do
  use Ecto.Schema
  import Ecto.Changeset

  schema "sms_notifications" do
    field :user_id, :id, default: 1
    field :message, :string
    field :sent_at, :utc_datetime
    field :status, :string, default: "ready"
    field :msisdn, :string
    field :details, :map
    field :attempt_count, :integer, default: 0

    timestamps()
  end

  def changeset(sms_notification, attrs) do
    sms_notification
    |> cast(attrs, [:user_id, :message, :msisdn, :details, :sent_at, :status])
    |> validate_required([:msisdn, :message])
  end

  def update_changeset(sms_notification, attrs) do
    sms_notification
    |> cast(attrs, [:attempt_count, :details, :sent_at, :status])
  end
end

# ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{msisdn: "************", message: "Hello Boldwin from the FDH Mobile Banking "})
