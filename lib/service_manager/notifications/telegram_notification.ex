defmodule ServiceManager.TelegramNotify do
  @moduledoc """
  Module for sending notifications to Telegram channels
  """

  require Logger

  @doc """
  Test function that sends various notification messages with different icons
  to demonstrate different notification scenarios
  """
  def test() do
    # Success notification
    send_telegram_notification("✅ SUCCESS: Transaction completed successfully")

    # Failure notification
    send_telegram_notification("❌ FAILED: Unable to process payment request")

    # Warning/caution notification
    send_telegram_notification("⚠️ WARNING: System approaching capacity limits")

    # Information notification
    send_telegram_notification("ℹ️ INFO: Scheduled maintenance starting in 30 minutes")

    # Alert notification
    send_telegram_notification("🔔 ALERT: Unusual login activity detected")

    # Critical error notification
    send_telegram_notification("🚨 CRITICAL: Database connection lost")

    # System status notification
    send_telegram_notification("⚙️ SYSTEM: Services restarting after update")

    # Money/transaction notification
    send_telegram_notification("💰 TRANSACTION: Large deposit of K50,000 received")

    # Security notification
    send_telegram_notification("🔒 SECURITY: New device authorization required")

    # Service status notifications
    send_telegram_notification("🟢 SERVICE UP: Payment gateway is now operational")
    send_telegram_notification("🔴 SERVICE DOWN: API endpoint not responding")

    # Performance notification
    send_telegram_notification("⏱️ PERFORMANCE: Response time increased to 2.5s")

    # Update notification
    send_telegram_notification("🔄 UPDATE: System upgraded to version 2.3.1")
  end

  @doc """
  Sends a notification that the application is starting to initialize
  """
  def notify_app_initializing do
    {hostname, environment} = get_system_info()

    message =
      "🚀 APPLICATION STARTING: ServiceManager initializing on #{hostname} in #{environment} mode"

    send_telegram_notification(message)
  end

  @doc """
  Sends a notification that critical services have been initialized
  """
  def notify_critical_services_ready do
    {hostname, _environment} = get_system_info()

    message =
      "⚙️ CORE SERVICES READY: Database, Phoenix, and critical services initialized on #{hostname}"

    send_telegram_notification(message)
  end

  @doc """
  Sends a notification that the application is fully started and operational
  """
  def notify_app_ready do
    {hostname, environment} = get_system_info()
    version = get_app_version()

    message =
      "✅ APPLICATION READY: ServiceManager v#{version} fully operational on #{hostname} in #{environment} mode"

    send_telegram_notification(message)
  end

  @doc """
  Gets system information for inclusion in notifications
  """
  defp get_system_info do
    # Get hostname
    hostname =
      case :inet.gethostname() do
        {:ok, hostname} -> List.to_string(hostname)
        _ -> "unknown-host"
      end

    # Get environment
    environment = Application.get_env(:service_manager, :environment, "development")

    {hostname, environment}
  end

  @doc """
  Gets the application version from mix.exs
  """
  defp get_app_version do
    case Application.spec(:service_manager, :vsn) do
      nil -> "0.0.0"
      version -> List.to_string(version)
    end
  end

  # Send notification to Telegram
  def send_telegram_notification(message) do
    telegram_channel_id = Application.get_env(:service_manager, :telegram_channel_id)

    if telegram_channel_id do
      Task.start(fn ->
        try do
          Telegex.send_message(telegram_channel_id, message)
        rescue
          e ->
            Logger.error("Failed to send Telegram notification: #{inspect(e)}")
        catch
          kind, reason ->
            Logger.error(
              "Error sending Telegram notification: #{inspect(kind)} #{inspect(reason)}"
            )
        end
      end)
    else
      Logger.warn("Telegram channel ID not configured, skipping notification")
    end
  end
end
