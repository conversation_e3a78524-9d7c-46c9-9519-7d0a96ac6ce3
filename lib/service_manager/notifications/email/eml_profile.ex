defmodule ServiceManager.Notifications.Email.EmlProfile do
  import Swoosh.Email

  def welcome(user) do
    new()
    |> to({user.name, user.email})
    |> from({"FDH Mobile Banking", "<EMAIL>"})
    |> subject("Hello, Avengers!")
    |> html_body("<h1>Hello #{user.name}</h1>")
    |> text_body("Hello #{user.name}\n")
  end

  # email = ServiceManager.Notifications.Email.EmlProfile.welcome(%{name: "<PERSON>", email: "<EMAIL>"}) |> ServiceManager.Mailer.deliver()
  # ServiceManager.Mailer.deliver(email)
end
