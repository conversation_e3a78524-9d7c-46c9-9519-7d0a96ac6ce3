defmodule ServiceManager.Notifications.Sms.NotifySms do
  def send_sms(from, to, body) do
    url = "https://api.bulksms.com/v1/messages"

    headers = [
      # "5F52D2847CBC4436A3A89C449D0630E4-01-F:KupKsvrDq4iielNh4f!8iXlXnRd4N"
      {"Authorization",
       "Basic NjRBQUFENEQzMDY3NDNFNjkyQzRBMTdDQkYzMkU2NDMtMDEtNTpLdXBLc3ZyRHE0aWllbE5oNGYhOGlYbFhuUmQ0Tg=="},
      {"Content-Type", "application/json"}
    ]

    payload = %{
      "from" => from,
      "to" => to,
      "body" => body
    }

    HTTPoison.post(url, Jason.encode!(payload), headers)
  end

  def send_sms_test() do
    to = "+260970210154"
    body = "This is a test message"
    from = "260970210154"

    case send_sms(from, to, body) do
      {:ok, response} ->
        IO.puts("SMS sent successfully: #{response.body}")
        response

      {:error, reason} ->
        IO.puts("Failed to send SMS: #{reason}")
        reason
    end
  end
end
