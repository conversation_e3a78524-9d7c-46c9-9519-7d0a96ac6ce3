defmodule ServiceManager.Notifications.PushNotification do
  use Ecto.Schema
  import Ecto.Changeset

  schema "push_notifications" do
    field :user_id, :id
    field :title, :string
    field :body, :string
    field :sent_at, :utc_datetime
    field :status, :string

    timestamps()
  end

  def changeset(push_notification, attrs) do
    push_notification
    |> cast(attrs, [:user_id, :title, :body, :sent_at, :status])
    |> validate_required([:user_id, :title, :body])
  end
end
