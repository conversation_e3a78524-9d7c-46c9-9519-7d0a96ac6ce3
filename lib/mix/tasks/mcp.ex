defmodule Mix.Tasks.Mcp do
  @moduledoc """
  Mix task for managing the MCP server.

  ## Examples

      # Start the MCP server
      mix mcp.start

      # Check MCP server status
      mix mcp.status

      # List available resources
      mix mcp.resources

      # List available tools
      mix mcp.tools

      # List available prompts
      mix mcp.prompts
  """
  
  use Mix.Task
  
  @shortdoc "Manage the MCP server for dynamic forms"
  
  def run(["start"]) do
    Mix.shell().info("🚀 Starting MCP server for Dynamic Forms system...")
    Mix.shell().info("=" |> String.duplicate(60))

    # Check configuration first
    mcp_config_raw = Application.get_env(:service_manager, :mcp_server, %{})
    Mix.shell().info("📋 MCP Configuration (raw): #{inspect(mcp_config_raw)}")

    # Convert keyword list to map if needed
    mcp_config = case mcp_config_raw do
      config when is_list(config) -> Enum.into(config, %{})
      config when is_map(config) -> config
      _ -> %{}
    end

    Mix.shell().info("📋 MCP Configuration (normalized): #{inspect(mcp_config)}")

    unless Map.get(mcp_config, :enabled, false) do
      Mix.shell().error("❌ MCP Server is disabled in configuration!")
      Mix.shell().info("💡 Enable it by setting config :service_manager, :mcp_server, enabled: true")
      System.halt(1)
    end

    # Start the application
    Mix.shell().info("🔄 Starting ServiceManager application...")
    case Application.ensure_all_started(:service_manager) do
      {:ok, apps} ->
        Mix.shell().info("✅ Application started successfully")
        Mix.shell().info("📦 Started apps: #{inspect(apps)}")

        # Wait for services to initialize
        Mix.shell().info("⏳ Waiting for services to initialize...")
        Process.sleep(3000)

        # Check if MCP supervisor is running
        check_mcp_supervisor()

        # Check if MCP server is running
        case wait_for_mcp_server(10) do
          {:ok, info} ->
            Mix.shell().info("✅ MCP Server started successfully!")
            print_server_info(info)
            print_usage_info()

            # Keep the process alive
            Mix.shell().info("\n🚀 MCP Server is running! Press Ctrl+C to stop.")
            Process.sleep(:infinity)

          {:error, reason} ->
            Mix.shell().error("❌ Failed to start MCP server: #{reason}")
            print_troubleshooting_info()
            System.halt(1)
        end

      {:error, {app, reason}} ->
        Mix.shell().error("❌ Failed to start application #{app}: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  def run(["status"]) do
    Mix.shell().info("Checking MCP server status...")
    
    {:ok, _} = Application.ensure_all_started(:service_manager)
    
    case ServiceManager.MCP.Server.get_server_info() do
      {:ok, info} ->
        Mix.shell().info("MCP Server is running")
        print_server_info(info)
        
      {:error, reason} ->
        Mix.shell().error("MCP Server is not running: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  def run(["resources"]) do
    Mix.shell().info("Listing available MCP resources...")
    
    {:ok, _} = Application.ensure_all_started(:service_manager)
    
    case ServiceManager.MCP.Server.list_resources() do
      {:ok, resources} ->
        Mix.shell().info("Available Resources:")
        Enum.each(resources, fn resource ->
          Mix.shell().info("  • #{resource.name} (#{resource.uri})")
          Mix.shell().info("    #{resource.description}")
        end)
        
      {:error, reason} ->
        Mix.shell().error("Failed to list resources: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  def run(["tools"]) do
    Mix.shell().info("Listing available MCP tools...")
    
    {:ok, _} = Application.ensure_all_started(:service_manager)
    
    case ServiceManager.MCP.Server.list_tools() do
      {:ok, tools} ->
        Mix.shell().info("Available Tools:")
        Enum.each(tools, fn tool ->
          Mix.shell().info("  • #{tool.name}")
          Mix.shell().info("    #{tool.description}")
        end)
        
      {:error, reason} ->
        Mix.shell().error("Failed to list tools: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  def run(["prompts"]) do
    Mix.shell().info("Listing available MCP prompts...")
    
    {:ok, _} = Application.ensure_all_started(:service_manager)
    
    case ServiceManager.MCP.Server.list_prompts() do
      {:ok, prompts} ->
        Mix.shell().info("Available Prompts:")
        Enum.each(prompts, fn prompt ->
          Mix.shell().info("  • #{prompt.name}")
          Mix.shell().info("    #{prompt.description}")
        end)
        
      {:error, reason} ->
        Mix.shell().error("Failed to list prompts: #{inspect(reason)}")
        System.halt(1)
    end
  end
  
  def run(["stdio"]) do
    Mix.shell().info("🚀 Starting MCP stdio server for LM Studio...")

    # Start the application
    case Application.ensure_all_started(:service_manager) do
      {:ok, _} ->
        # Wait for services to initialize
        Process.sleep(3000)

        # Start stdio server
        ServiceManager.MCP.StdioServer.start()

      {:error, {app, reason}} ->
        Mix.shell().error("❌ Failed to start application #{app}: #{inspect(reason)}")
        System.halt(1)
    end
  end

  def run([]) do
    Mix.shell().info("""
    MCP Server for Dynamic Forms System

    Available commands:
      mix mcp start     - Start the MCP server (GenServer mode)
      mix mcp stdio     - Start the MCP stdio server (for LM Studio)
      mix mcp status    - Check server status
      mix mcp resources - List available resources
      mix mcp tools     - List available tools
      mix mcp prompts   - List available prompts

    Examples:
      mix mcp start
      mix mcp stdio
      mix mcp status
    """)
  end
  
  def run([unknown_command]) do
    Mix.shell().error("Unknown command: #{unknown_command}")
    Mix.shell().info("Run 'mix mcp' for available commands")
    System.halt(1)
  end
  
  defp check_mcp_supervisor() do
    case Process.whereis(ServiceManager.MCP.Supervisor) do
      nil ->
        Mix.shell().error("❌ MCP Supervisor is not running!")
      pid ->
        Mix.shell().info("✅ MCP Supervisor is running (PID: #{inspect(pid)})")

        # Check children
        children = Supervisor.which_children(ServiceManager.MCP.Supervisor)
        Mix.shell().info("👥 MCP Supervisor children: #{length(children)}")

        Enum.each(children, fn {id, child_pid, type, modules} ->
          status = if is_pid(child_pid) and Process.alive?(child_pid), do: "✅", else: "❌"
          Mix.shell().info("   #{status} #{id} (#{type}): #{inspect(child_pid)}")
        end)
    end
  end

  defp wait_for_mcp_server(retries) when retries > 0 do
    Mix.shell().info("🔍 Checking MCP server status... (#{retries} retries left)")

    case ServiceManager.MCP.Server.get_server_info() do
      {:ok, info} ->
        Mix.shell().info("✅ MCP Server responded successfully!")
        {:ok, info}
      {:error, reason} ->
        Mix.shell().info("⏳ MCP server not ready: #{inspect(reason)}")
        Process.sleep(1000)
        wait_for_mcp_server(retries - 1)
    end
  end

  defp wait_for_mcp_server(0) do
    {:error, "MCP server failed to start after multiple retries"}
  end

  defp print_usage_info() do
    Mix.shell().info("\n" <> ("=" |> String.duplicate(60)))
    Mix.shell().info("📖 USAGE INFORMATION")
    Mix.shell().info("=" |> String.duplicate(60))
    Mix.shell().info("Available commands:")
    Mix.shell().info("  mix mcp status    - Check server status")
    Mix.shell().info("  mix mcp resources - List available resources")
    Mix.shell().info("  mix mcp tools     - List available tools")
    Mix.shell().info("  mix mcp prompts   - List available prompts")
  end

  defp print_troubleshooting_info() do
    Mix.shell().info("\n" <> ("=" |> String.duplicate(60)))
    Mix.shell().info("🔧 TROUBLESHOOTING")
    Mix.shell().info("=" |> String.duplicate(60))
    Mix.shell().info("1. Check if database is running and accessible")
    Mix.shell().info("2. Verify MCP server is enabled in config")
    Mix.shell().info("3. Check application logs for detailed errors")
    Mix.shell().info("4. Ensure all dependencies are properly loaded")
    Mix.shell().info("\nFor detailed logs, check the application output above.")
  end

  defp print_server_info(info) do
    Mix.shell().info("\n📊 Server Information:")
    Mix.shell().info("  Name: #{info.name}")
    Mix.shell().info("  Version: #{info.version}")
    Mix.shell().info("  Status: #{info.status}")
    Mix.shell().info("  Uptime: #{info.uptime} seconds")
    Mix.shell().info("  Connections: #{info.connections}")
    Mix.shell().info("  Requests Handled: #{info.requests_handled}")

    if info.config do
      Mix.shell().info("\n⚙️  Configuration:")
      Mix.shell().info("    Host: #{info.config.host}")
      Mix.shell().info("    Port: #{info.config.port}")
      Mix.shell().info("    Auth Required: #{info.config.auth_required}")
      Mix.shell().info("    Max Connections: #{info.config.max_connections}")
    end
  end
end