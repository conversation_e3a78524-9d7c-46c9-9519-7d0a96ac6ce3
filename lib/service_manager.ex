defmodule ServiceManager do
  @moduledoc """
  ServiceManager keeps the contexts that define your domain
  and business logic.

  Contexts are also responsible for managing your data, regardless
  if it comes from the database, an external API or others.

  ## Examples

      # User management
      iex> ServiceManager.get_user(123)
      %ServiceManager.Accounts.User{id: 123, email: "<EMAIL>"}

      iex> ServiceManager.authenticate_user("<EMAIL>", "password123")
      {:ok, %ServiceManager.Accounts.User{}}

      # Registration
      iex> params = %{"phone_number" => "+**********", "customer_number" => "CUST001"}
      iex> ServiceManager.register_mobile_banking(params)
      {:ok, %{"status" => true, "message" => "Registration successful"}}

      # Account management
      iex> ServiceManager.get_account_balance("ACC123456")
      {:ok, 1500.50}

      # Notifications
      iex> user = %ServiceManager.Accounts.User{email: "<EMAIL>"}
      iex> ServiceManager.send_email_notification(user, "Welcome!")
      {:ok, :sent}

      # Device binding
      iex> ServiceManager.bind_device(user, "device_123")
      {:ok, %ServiceManager.Accounts.User{primary_device_id: "device_123"}}
  """

  # User Management APIs
  defdelegate get_user(id), to: ServiceManager.Accounts
  defdelegate get_user_by_email(email), to: ServiceManager.Accounts
  defdelegate get_user_by_customer_no(customer_no), to: ServiceManager.Accounts
  defdelegate create_user(attrs), to: ServiceManager.Accounts
  defdelegate update_user(user, attrs), to: ServiceManager.Accounts
  defdelegate delete_user(user), to: ServiceManager.Accounts
  defdelegate authenticate_user(email, password), to: ServiceManager.Accounts
  defdelegate change_user_password(user, attrs), to: ServiceManager.Accounts

  # Registration APIs
  defdelegate register_mobile_banking(params), to: ServiceManager.Registration
  defdelegate validate_registration_params(params), to: ServiceManager.Registration
  defdelegate link_user_account(params), to: ServiceManager.Registration

  # Account Management APIs
  defdelegate get_user_accounts(user_id), to: ServiceManager.Accounts
  defdelegate get_account_balance(account_number), to: ServiceManager.Accounts
  defdelegate update_account_balance(account_number, balance), to: ServiceManager.Accounts

  # Third Party Service APIs
  defdelegate handle_third_party_request(service_type, params), to: ServiceManager.ThirdPartyServices
  defdelegate validate_third_party_params(service_type, params), to: ServiceManager.ThirdPartyServices

  # Notification APIs
  defdelegate send_email_notification(user, message), to: ServiceManager.Notifications
  defdelegate send_sms_notification(user, message), to: ServiceManager.Notifications
  defdelegate send_push_notification(user, message), to: ServiceManager.Notifications
  defdelegate update_notification_settings(user, settings), to: ServiceManager.Notifications

  # Core Banking Integration APIs
  defdelegate sync_user_with_core_banking(user), to: ServiceManager.CoreBanking
  defdelegate fetch_customer_accounts(customer_number), to: ServiceManager.CoreBanking
  defdelegate validate_customer_exists(customer_number), to: ServiceManager.CoreBanking

  # Device Management APIs
  defdelegate bind_device(user, device_id), to: ServiceManager.DeviceManagement
  defdelegate unbind_device(user), to: ServiceManager.DeviceManagement
  defdelegate validate_device_binding(user, device_id), to: ServiceManager.DeviceManagement

  # Session Management APIs
  defdelegate create_user_session(user), to: ServiceManager.Sessions
  defdelegate get_user_session(session_id), to: ServiceManager.Sessions
  defdelegate delete_user_session(session_id), to: ServiceManager.Sessions
  defdelegate validate_session(session_id), to: ServiceManager.Sessions
end
