defmodule ServiceManagerWeb.Backend.DynamicFormsLive.RoutesIndex do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Routing.{DynamicRoute, DynamicRouteManager}
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
  alias ServiceManagerWeb.Backend.DynamicFormsLive.AsyncQueryManager

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/dynamic-forms/routes")
      |> assign(:loading_route_data, true)
      |> assign(:view_mode, "list")
      |> AsyncQueryManager.init_async_tracking()
      |> load_routes()

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    case DynamicRouteManager.get_route(id) do
      nil ->
        socket
        |> put_flash(:error, "Route not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/routes")
      
      route ->
        socket
        |> assign(:page_title, "Edit Route")
        |> assign(:route, route)
    end
  end

  defp apply_action(socket, :link_forms, %{"id" => id}) do
    case DynamicRouteManager.get_route(id) do
      nil ->
        socket
        |> put_flash(:error, "Route not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/routes")
      
      route ->
        # Get all forms for linking
        forms = ServiceManager.Forms.DynamicFormsManager.list_forms()
        routes = DynamicRouteManager.list_routes()
        
        socket
        |> assign(:page_title, "Link Forms to Route")
        |> assign(:route, route)
        |> assign(:route_form, %{route_id: route.id, form_id: nil})
        |> assign(:forms, forms)
        |> assign(:all_routes, routes)
    end
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Route")
    |> assign(:route, %DynamicRoute{})
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    case DynamicRouteManager.get_route(id) do
      nil ->
        socket
        |> put_flash(:error, "Route not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/routes")
      
      route ->
        {:noreply, push_navigate(socket, to: ~p"/mobileBanking/dynamic-forms/routes/#{route.id}/details")}
    end
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "API Routes")
    |> assign(:route, nil)
  end

  @impl true
  def handle_info({ref, result}, socket) when is_reference(ref) do
    AsyncQueryManager.handle_async_result(ref, result, socket, &handle_route_data_result/4)
  end

  @impl true
  def handle_info({:DOWN, ref, :process, _pid, _reason}, socket) do
    {:noreply, AsyncQueryManager.handle_async_failure(ref, socket)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    case DynamicRouteManager.get_route(id) do
      nil ->
        {:noreply, 
         socket
         |> put_flash(:error, "Route not found")
         |> load_routes()}
      
      route ->
        {:ok, _} = DynamicRouteManager.delete_route(route)
        {:noreply, 
         socket
         |> put_flash(:info, "Route deleted successfully")
         |> load_routes()}
    end
  end

  @impl true
  def handle_event("toggle-enabled", %{"id" => id}, socket) do
    case DynamicRouteManager.get_route(id) do
      nil ->
        {:noreply, 
         socket
         |> put_flash(:error, "Route not found")
         |> load_routes()}
      
      route ->
        {:ok, _} = DynamicRouteManager.update_route(route, %{enabled: !route.enabled})
        {:noreply, 
         socket
         |> put_flash(:info, "Route #{route.enabled && "disabled" || "enabled"} successfully")
         |> load_routes()}
    end
  end

  @impl true
  def handle_event("toggle_view", %{"view" => view}, socket) do
    {:noreply, assign(socket, :view_mode, view)}
  end

  defp load_routes(socket) do
    routes = DynamicRouteManager.list_routes()
    
    # Load routes with placeholder data initially
    routes_with_placeholder = Enum.map(routes, fn route ->
      Map.merge(route, %{
        initial_process: nil,
        linked_forms_count: 0,
        loading: true
      })
    end)
    
    socket
    |> assign(:routes, routes_with_placeholder)
    |> load_route_data_async()
  end

  defp load_route_data_async(socket) do
    AsyncQueryManager.start_async_item_queries(
      socket.assigns.routes,
      "route_data",
      &calculate_route_data/1,
      socket
    )
  end

  defp handle_route_data_result(socket, _query_key, route_id, result) do
    case result do
      {:ok, route_data} ->
        # Update the specific route with the loaded data
        updated_routes = Enum.map(socket.assigns.routes, fn route ->
          if route.id == route_id do
            route
            |> Map.merge(route_data)
            |> Map.put(:loading, false)
          else
            route
          end
        end)

        # Check if all route data tasks are complete
        loading_route_data = AsyncQueryManager.has_pending_tasks_with_prefix?(socket, "route_data")

        {:noreply,
         socket
         |> assign(:routes, updated_routes)
         |> assign(:loading_route_data, loading_route_data)}

      {:error, _error} ->
        # On error, just mark as not loading with empty data
        updated_routes = Enum.map(socket.assigns.routes, fn route ->
          if route.id == route_id do
            route
            |> Map.put(:initial_process, nil)
            |> Map.put(:linked_forms_count, 0)
            |> Map.put(:loading, false)
          else
            route
          end
        end)

        loading_route_data = AsyncQueryManager.has_pending_tasks_with_prefix?(socket, "route_data")

        {:noreply,
         socket
         |> assign(:routes, updated_routes)
         |> assign(:loading_route_data, loading_route_data)}
    end
  end

  defp calculate_route_data(route) do
    try do
      # Get linked process
      initial_process = case ProcessManager.get_initial_process(route.id) do
        {:ok, process} -> process
        {:error, _} -> nil
      end
      
      # Get linked forms count
      linked_forms_count = count_linked_forms(route.id)
      
      %{
        initial_process: initial_process,
        linked_forms_count: linked_forms_count
      }
    rescue
      _ ->
        %{
          initial_process: nil,
          linked_forms_count: 0
        }
    end
  end

  defp count_linked_forms(route_id) do
    import Ecto.Query
    
    query = from drf in "dynamic_route_forms",
            where: drf.route_id == ^route_id
    
    ServiceManager.Repo.aggregate(query, :count, :route_id)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
        id="navigation"
        page_title="API Routes"
        subtitle="Manage API endpoints and routing"
        current_page={:routes}
        breadcrumb={[
          %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
          %{title: "Routes"}
        ]}
      />


      <div class="px-6 py-6">
        <%= if Enum.empty?(@routes) do %>
          <!-- Empty State -->
          <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <.icon name="hero-globe-alt" class="h-12 w-12 text-gray-400" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No routes yet</h3>
            <p class="text-gray-600 mb-6">Get started by creating your first API route</p>
            <.link
              patch={~p"/mobileBanking/dynamic-forms/routes/new"}
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <.icon name="hero-plus" class="h-4 w-4 mr-2" />
              Create Route
            </.link>
          </div>
        <% else %>
          <!-- Header with View Controls -->
          <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-6">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h2 class="text-xl font-semibold text-gray-900">All Routes</h2>
                  <p class="text-sm text-gray-600">Manage your API endpoints and routing</p>
                </div>
                <.link
                  patch={~p"/mobileBanking/dynamic-forms/routes/new"}
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                  Create Route
                </.link>
              </div>

              <!-- Controls -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-600"><%= length(@routes) %> routes</span>
                </div>

                <!-- View Toggle -->
                <div class="flex items-center bg-gray-100 rounded-lg p-1">
                  <button
                    phx-click="toggle_view"
                    phx-value-view="grid"
                    class={[
                      "px-3 py-1 rounded text-sm font-medium transition-colors",
                      if(@view_mode == "grid", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
                    ]}
                  >
                    <.icon name="hero-squares-2x2" class="h-4 w-4" />
                  </button>
                  <button
                    phx-click="toggle_view"
                    phx-value-view="list"
                    class={[
                      "px-3 py-1 rounded text-sm font-medium transition-colors",
                      if(@view_mode == "list", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
                    ]}
                  >
                    <.icon name="hero-list-bullet" class="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="px-6 py-6">
            <!-- Grid View -->
            <div :if={@view_mode == "grid"} class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
              <%= for route <- @routes do %>
                <div class="bg-white rounded-md border border-gray-200 hover:shadow-md transition-shadow duration-200">
                  <!-- Header -->
                  <div class="p-3 border-b border-gray-100">
                    <div class="flex items-start justify-between mb-1">
                      <h3 class="text-sm font-medium text-gray-900 truncate pr-1"><%= route.name %></h3>
                      <div class="flex space-x-1 flex-shrink-0">
                        <%= if !route.enabled do %>
                          <.icon name="hero-x-circle" class="h-3 w-3 text-red-500" />
                        <% else %>
                          <.icon name="hero-check-circle" class="h-3 w-3 text-green-500" />
                        <% end %>
                      </div>
                    </div>

                    <div class="flex items-center space-x-1 mb-2">
                      <span class={[
                        "inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium",
                        case route.method do
                          "GET" -> "bg-green-100 text-green-800"
                          "POST" -> "bg-blue-100 text-blue-800"
                          "PUT" -> "bg-yellow-100 text-yellow-800"
                          "PATCH" -> "bg-orange-100 text-orange-800"
                          "DELETE" -> "bg-red-100 text-red-800"
                          _ -> "bg-gray-100 text-gray-800"
                        end
                      ]}>
                        <%= route.method %>
                      </span>
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                        Route
                      </span>
                    </div>

                    <code class="text-xs text-gray-600 bg-gray-50 px-1.5 py-0.5 rounded block mb-2 truncate">
                      <%= route.path %>
                    </code>

                    <!-- Route Stats -->
                    <div class="flex items-center justify-between text-xs">
                      <div class="flex items-center space-x-2">
                        <%= if route.initial_process do %>
                          <span class="flex items-center text-gray-500">
                            <.icon name="hero-cog" class="h-3 w-3 mr-1" />
                            Process
                          </span>
                        <% end %>
                        <%= if route.linked_forms_count > 0 do %>
                          <span class="flex items-center text-gray-500">
                            <.icon name="hero-document-text" class="h-3 w-3 mr-1" />
                            <%= route.linked_forms_count %> forms
                          </span>
                        <% end %>
                      </div>
                      <div class="flex items-center space-x-2">
                        <span class={[
                          "text-xs font-medium",
                          if(route.enabled, do: "text-green-600", else: "text-red-600")
                        ]}>
                          <%= if route.enabled, do: "Active", else: "Inactive" %>
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- Actions -->
                  <div class="p-2">
                    <div class="flex space-x-1">
                      <.link
                        patch={~p"/mobileBanking/dynamic-forms/routes/#{route}/edit"}
                        class="flex-1 inline-flex justify-center items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <.icon name="hero-pencil" class="h-3 w-3 mr-1" />
                        Edit
                      </.link>
                      <button
                        phx-click="toggle-enabled"
                        phx-value-id={route.id}
                        class={[
                          "inline-flex items-center px-2 py-1 border text-xs font-medium rounded",
                          if route.enabled do
                            "border-red-300 text-red-700 bg-white hover:bg-red-50"
                          else
                            "border-green-300 text-green-700 bg-white hover:bg-green-50"
                          end
                        ]}
                        title={route.enabled && "Disable" || "Enable"}
                      >
                        <.icon name={route.enabled && "hero-x-mark" || "hero-check"} class="h-3 w-3" />
                      </button>
                      <button
                        phx-click="delete"
                        phx-value-id={route.id}
                        data-confirm="Are you sure you want to delete this route?"
                        class="inline-flex items-center px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50"
                      >
                        <.icon name="hero-trash" class="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>

            <!-- List View - Compact Table -->
            <div :if={@view_mode == "list"} class="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Route</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Method</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Path</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Process</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Forms</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Created</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <%= for route <- @routes do %>
                      <tr class="hover:bg-gray-50">
                        <td class="px-4 py-3 whitespace-nowrap">
                          <.link 
                            navigate={~p"/mobileBanking/dynamic-forms/routes/#{route.id}/details"}
                            class="text-sm font-medium text-indigo-600 hover:text-indigo-900 truncate max-w-32 block"
                            title={route.name}
                          >
                            <%= route.name %>
                          </.link>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <span class={[
                            "inline-flex items-center px-2 py-1 rounded text-sm font-medium",
                            case route.method do
                              "GET" -> "bg-green-100 text-green-800"
                              "POST" -> "bg-blue-100 text-blue-800"
                              "PUT" -> "bg-yellow-100 text-yellow-800"
                              "PATCH" -> "bg-orange-100 text-orange-800"
                              "DELETE" -> "bg-red-100 text-red-800"
                              _ -> "bg-gray-100 text-gray-800"
                            end
                          ]}>
                            <%= route.method %>
                          </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <code class="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded max-w-40 block truncate" title={route.path}>
                            <%= route.path %>
                          </code>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <%= if route.initial_process do %>
                            <div class="flex items-center">
                              <.icon name="hero-cog" class="h-4 w-4 mr-1 text-gray-400" />
                              <span class="text-sm text-gray-900 truncate max-w-24 block" title={route.initial_process.name}>
                                <%= route.initial_process.name %>
                              </span>
                            </div>
                          <% else %>
                            <span class="text-sm text-gray-400">No process</span>
                          <% end %>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <%= if route.linked_forms_count > 0 do %>
                            <div class="flex items-center">
                              <.icon name="hero-document-text" class="h-4 w-4 mr-1 text-gray-400" />
                              <span class="text-sm text-gray-900"><%= route.linked_forms_count %></span>
                            </div>
                          <% else %>
                            <span class="text-sm text-gray-400">0</span>
                          <% end %>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center">
                            <div class={[
                              "w-2 h-2 rounded-full mr-2",
                              if(route.enabled, do: "bg-green-400", else: "bg-red-400")
                            ]}></div>
                            <span class={[
                              "text-sm font-medium",
                              if(route.enabled, do: "text-green-800", else: "text-red-800")
                            ]}>
                              <%= if route.enabled, do: "Active", else: "Inactive" %>
                            </span>
                          </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <span class="text-sm text-gray-500">
                            <%= Calendar.strftime(route.inserted_at, "%b %d, %Y") %>
                          </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center space-x-2">
                            <!-- Test Route Button -->
                            <%= if route.enabled && route.initial_process do %>
                              <a
                                href={"/dynamic#{route.path}"}
                                target="_blank"
                                class="inline-flex items-center px-2 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50"
                                title="Test Route"
                              >
                                <.icon name="hero-play" class="h-4 w-4" />
                              </a>
                            <% end %>

                            <!-- Toggle Enable/Disable -->
                            <button
                              phx-click="toggle-enabled"
                              phx-value-id={route.id}
                              class={[
                                "inline-flex items-center px-2 py-1 border text-sm rounded",
                                if route.enabled do
                                  "border-red-300 text-red-700 bg-white hover:bg-red-50"
                                else
                                  "border-green-300 text-green-700 bg-white hover:bg-green-50"
                                end
                              ]}
                              title={route.enabled && "Disable" || "Enable"}
                            >
                              <.icon name={route.enabled && "hero-x-mark" || "hero-check"} class="h-4 w-4" />
                            </button>

                            <!-- Link Forms Button -->
                            <.link
                              patch={~p"/mobileBanking/dynamic-forms/routes/#{route}/link-forms"}
                              class="inline-flex items-center px-2 py-1 border border-blue-300 text-sm rounded text-blue-700 bg-white hover:bg-blue-50"
                              title="Link Forms"
                            >
                              <.icon name="hero-link" class="h-4 w-4" />
                            </.link>

                            <!-- Edit Button -->
                            <.link
                              patch={~p"/mobileBanking/dynamic-forms/routes/#{route}/edit"}
                              class="inline-flex items-center px-2 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50"
                              title="Edit"
                            >
                              <.icon name="hero-pencil" class="h-4 w-4" />
                            </.link>

                            <!-- Delete Button -->
                            <button
                              phx-click="delete"
                              phx-value-id={route.id}
                              data-confirm="Are you sure you want to delete this route?"
                              class="inline-flex items-center px-2 py-1 border border-red-300 text-sm rounded text-red-700 bg-white hover:bg-red-50"
                              title="Delete"
                            >
                              <.icon name="hero-trash" class="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <.modal :if={@live_action in [:new, :edit]} id="route-modal" show on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms/routes")}>
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.RouteFormComponent}
        id={@route.id || :new}
        title={@page_title}
        action={@live_action}
        route={@route}
        patch={~p"/mobileBanking/dynamic-forms/routes"}
      />
    </.modal>

    <.modal :if={@live_action == :link_forms} id="link-forms-modal" show on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms/routes")}>
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.LinkFormComponent}
        id="link-forms"
        title={@page_title}
        action={@live_action}
        route_form={@route_form}
        routes={@all_routes}
        forms={@forms}
        patch={~p"/mobileBanking/dynamic-forms/routes"}
      />
    </.modal>
    """
  end
end