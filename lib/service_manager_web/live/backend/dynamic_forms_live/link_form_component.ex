defmodule ServiceManagerWeb.Backend.DynamicFormsLive.LinkFormComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Forms.DynamicFormsManager
  alias ServiceManager.Routing.DynamicRouteManager

  @impl true
  def update(%{route_form: route_form, routes: routes, forms: forms} = assigns, socket) do
    # If route_id is provided in the URL params, use it
    route_id = if assigns[:action] == :link_form && assigns[:params] && assigns[:params]["route_id"] do
      case Integer.parse(assigns[:params]["route_id"]) do
        {int_id, _} -> int_id
        :error -> nil
      end
    else
      route_form[:route_id]
    end

    # Filter forms to only show those matching the selected route's method
    filtered_forms = if route_id do
      case Enum.find(routes, &(&1.id == route_id)) do
        nil -> forms
        route -> Enum.filter(forms, &(&1.http_method == route.method))
      end
    else
      forms
    end

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:route_id, route_id)
     |> assign(:form_id, route_form[:form_id])
     |> assign(:routes, routes)
     |> assign(:all_forms, forms)
     |> assign(:filtered_forms, filtered_forms)}
  end

  @impl true
  def handle_event("validate", %{"_target" => ["route_id"], "route_id" => route_id}, socket) do
    # Safely convert route_id to integer
    parsed_route_id = 
      case route_id do
        "" -> nil
        id -> 
          case Integer.parse(id) do
            {int_id, _} -> int_id
            :error -> nil
          end
      end
    
    # Filter forms based on the selected route
    filtered_forms = if parsed_route_id do
      case Enum.find(socket.assigns.routes, &(&1.id == parsed_route_id)) do
        nil -> socket.assigns.all_forms
        route -> Enum.filter(socket.assigns.all_forms, &(&1.http_method == route.method))
      end
    else
      socket.assigns.all_forms
    end

    {:noreply, 
     socket
     |> assign(:route_id, parsed_route_id)
     |> assign(:form_id, nil)  # Reset form_id when route changes
     |> assign(:filtered_forms, filtered_forms)}
  end

  @impl true
  def handle_event("validate", %{"route_id" => route_id, "form_id" => form_id}, socket) do
    # Safely convert route_id and form_id to integers
    parsed_route_id = 
      case route_id do
        "" -> nil
        id -> 
          case Integer.parse(id) do
            {int_id, _} -> int_id
            :error -> nil
          end
      end
      
    parsed_form_id = 
      case form_id do
        "" -> nil
        id -> 
          case Integer.parse(id) do
            {int_id, _} -> int_id
            :error -> nil
          end
      end

    # Filter forms based on the selected route
    filtered_forms = if parsed_route_id do
      case Enum.find(socket.assigns.routes, &(&1.id == parsed_route_id)) do
        nil -> socket.assigns.all_forms
        route -> Enum.filter(socket.assigns.all_forms, &(&1.http_method == route.method))
      end
    else
      socket.assigns.all_forms
    end

    {:noreply, 
     socket
     |> assign(:route_id, parsed_route_id)
     |> assign(:form_id, parsed_form_id)
     |> assign(:filtered_forms, filtered_forms)}
  end

  @impl true
  def handle_event("save", %{"route_id" => route_id, "form_id" => form_id}, socket) do
    # Safely convert route_id and form_id to integers
    parsed_route_id = 
      case Integer.parse(route_id) do
        {int_id, _} -> int_id
        :error -> nil
      end
      
    parsed_form_id = 
      case Integer.parse(form_id) do
        {int_id, _} -> int_id
        :error -> nil
      end

    if is_nil(parsed_route_id) or is_nil(parsed_form_id) do
      {:noreply,
       socket
       |> put_flash(:error, "Invalid route or form ID")
       |> push_navigate(to: socket.assigns.patch)}
    else
      # Validate that the form's HTTP method matches the route's method
      route = Enum.find(socket.assigns.routes, &(&1.id == parsed_route_id))
      form = Enum.find(socket.assigns.all_forms, &(&1.id == parsed_form_id))

      if route && form && form.http_method == route.method do
        case DynamicFormsManager.link_form_to_route(parsed_route_id, parsed_form_id) do
          {:ok, _} ->
            {:noreply,
             socket
             |> put_flash(:info, "Form linked to route successfully")
             |> push_navigate(to: socket.assigns.patch)}

          {:error, changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Error linking form to route: #{inspect(changeset.errors)}")
             |> push_navigate(to: socket.assigns.patch)}
        end
      else
        {:noreply,
         socket
         |> put_flash(:error, "Form HTTP method must match route method")
         |> push_navigate(to: socket.assigns.patch)}
      end
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h2 class="text-lg font-medium mb-4"><%= @title %></h2>

      <.simple_form
        for={%{}}
        id="link-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Route</label>
            <select
              name="route_id"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              required
            >
              <option value="" selected={is_nil(@route_id)}>Select a route</option>
              <%= for route <- @routes do %>
                <option value={route.id} selected={@route_id == route.id}>
                  <%= route.name %> (<%= route.method %> <%= route.path %>)
                </option>
              <% end %>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Form</label>
            <select
              name="form_id"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              required
              disabled={is_nil(@route_id)}
            >
              <option value="" selected={is_nil(@form_id)}>Select a form</option>
              <%= for form <- @filtered_forms do %>
                <option value={form.id} selected={@form_id == form.id}>
                  <%= form.name %> (<%= form.http_method %><%= if form.required, do: ", Required", else: ", Optional" %>)
                </option>
              <% end %>
            </select>
            <%= if is_nil(@route_id) do %>
              <p class="text-xs text-gray-500 mt-1">
                Please select a route first
              </p>
            <% else %>
              <p class="text-xs text-gray-500 mt-1">
                Only forms with matching HTTP method are shown
              </p>
            <% end %>
          </div>
        </div>

        <:actions>
          <.button
            type="button"
            class="px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
            phx-click={JS.navigate(@patch)}
          >
            Cancel
          </.button>
          <.button 
            type="submit" 
            phx-disable-with="Saving..." 
            class="px-4 py-2 text-sm text-white bg-blue-600 rounded-md shadow-sm hover:bg-blue-700"
            disabled={is_nil(@route_id) || is_nil(@form_id)}
          >
            Link Form to Route
          </.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end
end
