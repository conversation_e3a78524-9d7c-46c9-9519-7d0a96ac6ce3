defmodule ServiceManagerWeb.Backend.DynamicFormsLive.RouteFormComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Routing.DynamicRouteManager
  alias ServiceManager.Routing.DynamicRouter

  @impl true
  def update(%{route: route} = assigns, socket) do
    changeset = ServiceManager.Routing.DynamicRoute.changeset(route, %{})

    # Get processes for linking
    processes = ServiceManager.Schemas.Dynamic.Processes.ProcessManager.list_processes()

    # Get current linked process if editing
    current_process = case assigns.action do
      :edit -> get_linked_process(route.id)
      _ -> nil
    end

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:form, to_form(changeset))
     |> assign(:http_methods, ["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"])
     |> assign(:processes, processes)
     |> assign(:selected_process_id, current_process && current_process.id)
     |> assign(:current_process, current_process)}
  end

  @impl true
  def handle_event("validate", %{"dynamic_route" => route_params}, socket) do
    changeset =
      socket.assigns.route
      |> ServiceManager.Routing.DynamicRoute.changeset(route_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :form, to_form(changeset))}
  end

  def handle_event("save", %{"dynamic_route" => route_params}, socket) do
    save_route(socket, socket.assigns.action, route_params)
  end

  def handle_event("select-process", %{"process-id" => process_id}, socket) do
    process_id = case process_id do
      "" -> nil
      id -> String.to_integer(id)
    end

    {:noreply, assign(socket, :selected_process_id, process_id)}
  end

  defp save_route(socket, :edit, route_params) do
    case DynamicRouteManager.update_route(socket.assigns.route, route_params) do
      {:ok, route} ->
        # Handle process linking
        handle_process_linking(socket, route)

        {:noreply,
         socket
         |> put_flash(:info, "Route updated successfully")
         |> push_navigate(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset))}
    end
  end

  defp save_route(socket, :new, route_params) do
    # Extract parts from the path
    {parts, flat_parts} = DynamicRouter.extract_parts(route_params["path"])

    # Add parts and flat_parts to the params
    route_params = Map.merge(route_params, %{
      "parts" => parts,
      "flat_parts" => flat_parts
    })

    case DynamicRouteManager.create_route(
      route_params["name"],
      route_params["method"],
      route_params["path"],
      route_params["enabled"] == "true"
    ) do
      {:ok, route} ->
        # Handle process linking
        handle_process_linking(socket, route)

        {:noreply,
         socket
         |> put_flash(:info, "Route created successfully")
         |> push_navigate(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset))}
    end
  end

  # Helper functions
  defp get_linked_process(route_id) do
    case ServiceManager.Schemas.Dynamic.Processes.ProcessManager.get_initial_process(route_id) do
      {:ok, process} -> process
      _ -> nil
    end
  end

  defp handle_process_linking(socket, route) do
    current_process_id = socket.assigns.current_process && socket.assigns.current_process.id
    selected_process_id = socket.assigns.selected_process_id

    # Get user_id from socket assigns if available
    user_id = case socket.assigns do
      %{current_user: %{id: id}} -> id
      _ -> nil
    end

    cond do
      # If no process was linked before and none selected now, do nothing
      is_nil(current_process_id) && is_nil(selected_process_id) -> :ok

      # If process was linked before but none selected now, unlink
      current_process_id && is_nil(selected_process_id) ->
        ServiceManager.Schemas.Dynamic.Processes.ProcessManager.unlink_process_from_route(route.id)

      # If no process was linked before but one is selected now, link it
      is_nil(current_process_id) && selected_process_id ->
        ServiceManager.Schemas.Dynamic.Processes.ProcessManager.link_process_to_route(route.id, selected_process_id, user_id)

      # If different process selected, unlink old and link new
      current_process_id != selected_process_id ->
        ServiceManager.Schemas.Dynamic.Processes.ProcessManager.unlink_process_from_route(route.id)
        ServiceManager.Schemas.Dynamic.Processes.ProcessManager.link_process_to_route(route.id, selected_process_id, user_id)

      # If same process, do nothing
      true -> :ok
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h2 class="text-lg font-medium mb-4"><%= @title %></h2>

      <.simple_form
        for={@form}
        id="route-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <.input field={@form[:name]} type="text" label="Name" required />

        <.input
          field={@form[:method]}
          type="select"
          label="HTTP Method"
          options={@http_methods}
          required
        />

        <.input
          field={@form[:path]}
          type="text"
          label="Path"
          placeholder="/example/path"
          required
        />
        <p class="text-xs text-gray-500 mt-1">
          Path should start with a slash (e.g., /users/profile)
        </p>

        <.input
          field={@form[:enabled]}
          type="checkbox"
          label="Enabled"
        />

        <!-- Process Linking Section -->
        <div class="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h3 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <.icon name="hero-link" class="h-4 w-4 mr-2 text-indigo-600" />
            Link Initial Process (Optional)
          </h3>

          <div class="space-y-3">
            <label for="process-select" class="block text-xs text-gray-600">
              Select a process to execute when this route is called
            </label>

            <select
              id="process-select"
              name="process-id"
              value={@selected_process_id || ""}
              class="block w-full text-sm rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="">-- No process (route only) --</option>
              <%= for process <- @processes do %>
                <option value={process.id} selected={@selected_process_id == process.id}>
                  <%= process.name %> - <%= process.description || "No description" %>
                </option>
              <% end %>
            </select>

            <%= if @selected_process_id do %>
              <div class="text-xs text-green-600 flex items-center">
                <.icon name="hero-check-circle" class="h-3 w-3 mr-1" />
                Process will be linked to this route
              </div>
            <% end %>
          </div>
        </div>

        <:actions>
          <.button
            type="button"
            class="px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
            phx-click={JS.navigate(@patch)}
          >
            Cancel
          </.button>
          <.button type="submit" phx-disable-with="Saving..." class="px-4 py-2 text-sm text-white bg-blue-600 rounded-md shadow-sm hover:bg-blue-700">
            Save Route
          </.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end
end
