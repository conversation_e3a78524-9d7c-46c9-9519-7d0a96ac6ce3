defmodule ServiceManagerWeb.Backend.DynamicFormsLive.FormsIndex do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Forms.{DynamicForm, DynamicFormsManager}

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/dynamic-forms/forms")
      |> assign(:view_mode, "grid")
      |> load_forms()

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    case DynamicFormsManager.get_form(id) do
      nil ->
        socket
        |> put_flash(:error, "Form not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/forms")
      
      form ->
        socket
        |> assign(:page_title, "Edit Form")
        |> assign(:form, form)
    end
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Form")
    |> assign(:form, %DynamicForm{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Forms")
    |> assign(:form, nil)
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    case DynamicFormsManager.get_form(id) do
      nil ->
        {:noreply, 
         socket
         |> put_flash(:error, "Form not found")
         |> load_forms()}
      
      form ->
        {:ok, _} = DynamicFormsManager.delete_form(form)
        {:noreply, 
         socket
         |> put_flash(:info, "Form deleted successfully")
         |> load_forms()}
    end
  end

  @impl true
  def handle_event("toggle_view", %{"view" => view}, socket) do
    {:noreply, assign(socket, :view_mode, view)}
  end

  defp load_forms(socket) do
    forms = DynamicFormsManager.list_forms()
    assign(socket, :forms, forms)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
        id="navigation"
        page_title="Forms"
        subtitle="Manage API forms and validation schemas"
        current_page={:forms}
        breadcrumb={[
          %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
          %{title: "Forms"}
        ]}
      />


      <div class="px-6 py-6">
        <%= if Enum.empty?(@forms) do %>
          <!-- Empty State -->
          <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <.icon name="hero-document-text" class="h-12 w-12 text-gray-400" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No forms yet</h3>
            <p class="text-gray-600 mb-6">Get started by creating your first API form</p>
            <.link
              patch={~p"/mobileBanking/dynamic-forms/forms/new"}
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <.icon name="hero-plus" class="h-4 w-4 mr-2" />
              Create Form
            </.link>
          </div>
        <% else %>
          <!-- Header with View Controls -->
          <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-6">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h2 class="text-xl font-semibold text-gray-900">All Forms</h2>
                  <p class="text-sm text-gray-600">Manage your API forms and validation schemas</p>
                </div>
                <.link
                  patch={~p"/mobileBanking/dynamic-forms/forms/new"}
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                  Create Form
                </.link>
              </div>

              <!-- Controls -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-600"><%= length(@forms) %> forms</span>
                </div>

                <!-- View Toggle -->
                <div class="flex items-center bg-gray-100 rounded-lg p-1">
                  <button
                    phx-click="toggle_view"
                    phx-value-view="grid"
                    class={[
                      "px-3 py-1 rounded text-sm font-medium transition-colors",
                      if(@view_mode == "grid", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
                    ]}
                  >
                    <.icon name="hero-squares-2x2" class="h-4 w-4" />
                  </button>
                  <button
                    phx-click="toggle_view"
                    phx-value-view="list"
                    class={[
                      "px-3 py-1 rounded text-sm font-medium transition-colors",
                      if(@view_mode == "list", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
                    ]}
                  >
                    <.icon name="hero-list-bullet" class="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="px-6 py-6">
            <!-- Grid View -->
            <div :if={@view_mode == "grid"} class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
              <%= for form <- @forms do %>
                <div class="bg-white rounded-md border border-gray-200 hover:shadow-md transition-shadow duration-200">
                  <!-- Header -->
                  <div class="p-3 border-b border-gray-100">
                    <div class="flex items-start justify-between mb-1">
                      <h3 class="text-sm font-medium text-gray-900 truncate pr-1"><%= form.name %></h3>
                      <div class="flex space-x-1 flex-shrink-0">
                        <%= if form.required do %>
                          <.icon name="hero-lock-closed" class="h-3 w-3 text-red-500" />
                        <% end %>
                      </div>
                    </div>

                    <div class="flex items-center space-x-1 mb-2">
                      <span class={[
                        "inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium",
                        case form.http_method do
                          "GET" -> "bg-green-100 text-green-800"
                          "POST" -> "bg-blue-100 text-blue-800"
                          "PUT" -> "bg-yellow-100 text-yellow-800"
                          "PATCH" -> "bg-orange-100 text-orange-800"
                          "DELETE" -> "bg-red-100 text-red-800"
                          _ -> "bg-gray-100 text-gray-800"
                        end
                      ]}>
                        <%= form.http_method %>
                      </span>
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                        Form
                      </span>
                    </div>

                    <p class="text-xs text-gray-600 line-clamp-2 mb-2">
                      <%= form.description || "No description provided" %>
                    </p>

                    <!-- Form Stats -->
                    <div class="flex items-center justify-between text-xs">
                      <div class="flex items-center space-x-2">
                        <span class="flex items-center text-gray-500">
                          <.icon name="hero-document" class="h-3 w-3 mr-1" />
                          <%= length(form.form["fields"] || []) %> fields
                        </span>
                      </div>
                      <div class="flex items-center space-x-2">
                        <span class="flex items-center text-gray-500">
                          <.icon name={form.required && "hero-lock-closed" || "hero-lock-open"} class="h-3 w-3 mr-1" />
                          <%= form.required && "Required" || "Optional" %>
                        </span>
                        <span class="text-gray-400"><%= Calendar.strftime(form.inserted_at, "%b %d") %></span>
                      </div>
                    </div>
                  </div>

                  <!-- Actions -->
                  <div class="p-2">
                    <div class="flex space-x-1">
                      <.link
                        patch={~p"/mobileBanking/dynamic-forms/forms/#{form}/edit"}
                        class="flex-1 inline-flex justify-center items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <.icon name="hero-pencil" class="h-3 w-3 mr-1" />
                        Edit
                      </.link>
                      <button
                        phx-click="delete"
                        phx-value-id={form.id}
                        data-confirm="Are you sure you want to delete this form?"
                        class="inline-flex items-center px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50"
                      >
                        <.icon name="hero-trash" class="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>

            <!-- List View - Compact Table -->
            <div :if={@view_mode == "list"} class="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Form</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Method</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Fields</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Required</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Created</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <%= for form <- @forms do %>
                      <tr class="hover:bg-gray-50">
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="min-w-0 flex-1">
                            <span class="text-sm font-medium text-gray-900 truncate max-w-32 block" title={form.name}>
                              <%= form.name %>
                            </span>
                            <p class="text-xs text-gray-500 truncate max-w-32" title={form.description}>
                              <%= form.description || "No description" %>
                            </p>
                          </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <span class={[
                            "inline-flex items-center px-2 py-1 rounded text-sm font-medium",
                            case form.http_method do
                              "GET" -> "bg-green-100 text-green-800"
                              "POST" -> "bg-blue-100 text-blue-800"
                              "PUT" -> "bg-yellow-100 text-yellow-800"
                              "PATCH" -> "bg-orange-100 text-orange-800"
                              "DELETE" -> "bg-red-100 text-red-800"
                              _ -> "bg-gray-100 text-gray-800"
                            end
                          ]}>
                            <%= form.http_method %>
                          </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center">
                            <.icon name="hero-document" class="h-4 w-4 mr-1 text-gray-400" />
                            <span class="text-sm text-gray-900">
                              <%= length(form.form["fields"] || []) %> fields
                            </span>
                          </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center">
                            <.icon name={form.required && "hero-lock-closed" || "hero-lock-open"} class="h-4 w-4 mr-1 text-gray-400" />
                            <span class={[
                              "text-sm font-medium",
                              if(form.required, do: "text-red-800", else: "text-green-800")
                            ]}>
                              <%= form.required && "Required" || "Optional" %>
                            </span>
                          </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <span class="text-sm text-gray-500">
                            <%= Calendar.strftime(form.inserted_at, "%b %d, %Y") %>
                          </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center space-x-2">
                            <.link
                              patch={~p"/mobileBanking/dynamic-forms/forms/#{form}/edit"}
                              class="inline-flex items-center px-2 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50"
                              title="Edit"
                            >
                              <.icon name="hero-pencil" class="h-4 w-4" />
                            </.link>
                            <button
                              phx-click="delete"
                              phx-value-id={form.id}
                              data-confirm="Are you sure you want to delete this form?"
                              class="inline-flex items-center px-2 py-1 border border-red-300 text-sm rounded text-red-700 bg-white hover:bg-red-50"
                              title="Delete"
                            >
                              <.icon name="hero-trash" class="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <.modal :if={@live_action in [:new, :edit]} id="form-modal" show on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms/forms")}>
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.FormComponent}
        id={@form.id || :new}
        title={@page_title}
        action={@live_action}
        form={@form}
        current_user={@current_user}
        patch={~p"/mobileBanking/dynamic-forms/forms"}
      />
    </.modal>
    """
  end
end