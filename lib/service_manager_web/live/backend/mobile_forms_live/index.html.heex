<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <.header>
    Mobile Forms Management
    <:subtitle>Manage dynamic forms for mobile applications with hierarchical organization</:subtitle>
    <:actions>
      <.link patch={~p"/mobileBanking/mobile-forms/new"} phx-click={JS.push_focus()}>
        <.button>
          <.icon name="hero-plus" class="h-4 w-4 mr-2" />
          New Field
        </.button>
      </.link>
    </:actions>
  </.header>

  <!-- Tab Navigation -->
  <div class="border-b border-gray-200 mb-6">
    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
      <button
        phx-click="select_tab"
        phx-value-tab="forms"
        class={[
          "py-2 px-1 border-b-2 font-medium text-sm",
          if(@active_tab == "forms", do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
        ]}
      >
        Forms Overview
      </button>
      <button
        phx-click="select_tab"
        phx-value-tab="fields"
        class={[
          "py-2 px-1 border-b-2 font-medium text-sm",
          if(@active_tab == "fields", do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
        ]}
      >
        Field Details
      </button>
      <button
        phx-click="select_tab"
        phx-value-tab="hierarchy"
        class={[
          "py-2 px-1 border-b-2 font-medium text-sm",
          if(@active_tab == "hierarchy", do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
        ]}
      >
        Hierarchy Management
      </button>
      <button
        phx-click="select_tab"
        phx-value-tab="ussd"
        class={[
          "py-2 px-1 border-b-2 font-medium text-sm",
          if(@active_tab == "ussd", do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
        ]}
      >
        USSD Management
      </button>
    </nav>
  </div>

  <!-- Forms Overview Tab -->
  <div :if={@active_tab == "forms"}>
    <!-- Search and Filters -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="px-6 py-6">
        <!-- Search and Filters -->
        <div class="flex flex-col lg:flex-row gap-4 mb-6">
          <!-- Search Bar -->
          <div class="flex-1">
            <.form for={%{}} as={:search} phx-change="search" class="relative">
              <input
                type="text"
                name="search[query]"
                value={@search_query}
                placeholder="Search forms..."
                class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
              <.icon name="hero-magnifying-glass" class="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
            </.form>
          </div>

          <!-- Filters -->
          <div class="flex gap-3">
            <.form for={%{}} as={:filter} phx-change="filter" class="flex gap-3">
              <select name="filter[form]" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                <option value="all">All Forms</option>
                <%= for form <- @unique_forms do %>
                  <option value={form} selected={@selected_form_filter == form}><%= form %></option>
                <% end %>
              </select>

              <select name="filter[type]" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                <option value="all">All Field Types</option>
                <%= for type <- @field_types do %>
                  <option value={type} selected={@selected_type == type}><%= String.capitalize(type) %></option>
                <% end %>
              </select>
            </.form>
          </div>
        </div>

        <!-- Sort and View Controls -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600"><%= length(@grouped_forms) %> forms</span>

            <!-- Sort Controls -->
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-600">Sort:</span>
              <.form for={%{}} as={:sort} phx-change="sort" class="inline">
                <select name="sort_by" class="border border-gray-300 rounded text-sm px-2 py-1">
                  <option value="name" selected={@sort_by == "name"}>Name A-Z</option>
                  <option value="fields" selected={@sort_by == "fields"}>Most Fields</option>
                  <option value="updated" selected={@sort_by == "updated"}>Recently Updated</option>
                </select>
              </.form>
            </div>
          </div>

          <!-- View Toggle -->
          <div class="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              phx-click="toggle_view"
              phx-value-view="grid"
              class={[
                "px-3 py-1 rounded text-sm font-medium transition-colors",
                if(@view_mode == "grid", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
              ]}
            >
              <.icon name="hero-squares-2x2" class="h-4 w-4" />
            </button>
            <button
              phx-click="toggle_view"
              phx-value-view="list"
              class={[
                "px-3 py-1 rounded text-sm font-medium transition-colors",
                if(@view_mode == "list", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
              ]}
            >
              <.icon name="hero-list-bullet" class="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="px-6 py-6">
      <%= if Enum.empty?(@grouped_forms) do %>
        <!-- Empty State -->
        <div class="text-center py-12">
          <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <.icon name="hero-document-text" class="h-12 w-12 text-gray-400" />
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No forms found</h3>
          <p class="text-gray-600 mb-6">Try adjusting your search or filter criteria</p>
          <.link
            patch={~p"/mobileBanking/mobile-forms/new"}
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <.icon name="hero-plus" class="h-4 w-4 mr-2" />
            Create Your First Form
          </.link>
        </div>
      <% else %>
        <!-- Grid View -->
        <div :if={@view_mode == "grid"} class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
          <%= for form <- @grouped_forms do %>
            <div class="bg-white rounded-md border border-gray-200 hover:shadow-md transition-shadow duration-200">
              <!-- Header -->
              <div class="p-3 border-b border-gray-100">
                <div class="flex items-start justify-between mb-1">
                  <h3 class="text-sm font-medium text-gray-900 truncate pr-1"><%= form.name %></h3>
                  <div class="flex space-x-1 flex-shrink-0">
                    <%= if form.has_buttons do %>
                      <.icon name="hero-cursor-arrow-rays" class="h-3 w-3 text-green-500" title="Has action buttons" />
                    <% end %>
                  </div>
                </div>

                <div class="flex items-center space-x-1 mb-2">
                  <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                    v<%= form.version %>
                  </span>
                  <%= if form.has_buttons do %>
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-700">
                      Interactive
                    </span>
                  <% end %>
                </div>

                <div class="text-xs text-gray-600 space-y-1">
                  <div class="flex justify-between">
                    <span>Fields:</span>
                    <span class="font-medium"><%= form.field_count %></span>
                  </div>
                  <div class="flex justify-between">
                    <span>Screens:</span>
                    <span class="font-medium"><%= form.screen_count %></span>
                  </div>
                  <div class="flex justify-between">
                    <span>Pages:</span>
                    <span class="font-medium"><%= form.page_count %></span>
                  </div>
                </div>
              </div>

              <!-- Field Types -->
              <div class="p-2 border-b border-gray-100">
                <div class="flex flex-wrap gap-1">
                  <%= for type <- Enum.take(form.field_types, 3) do %>
                    <span class="inline-block px-1.5 py-0.5 text-xs bg-gray-50 text-gray-700 rounded">
                      <%= type %>
                    </span>
                  <% end %>
                  <%= if length(form.field_types) > 3 do %>
                    <span class="inline-block px-1.5 py-0.5 text-xs bg-gray-50 text-gray-500 rounded">
                      +<%= length(form.field_types) - 3 %>
                    </span>
                  <% end %>
                </div>
              </div>

              <!-- Actions -->
              <div class="p-2">
                <div class="flex space-x-1">
                  <button
                    phx-click="select_hierarchy"
                    phx-value-form={form.name}
                    class="flex-1 inline-flex justify-center items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <.icon name="hero-eye" class="h-3 w-3 mr-1" />
                    View
                  </button>
                  <.link
                    patch={~p"/mobileBanking/mobile-forms/new"}
                    class="inline-flex items-center px-2 py-1 border border-indigo-300 text-xs font-medium rounded text-indigo-700 bg-white hover:bg-indigo-50"
                    title="Add Field"
                  >
                    <.icon name="hero-plus" class="h-3 w-3" />
                  </.link>
                </div>
              </div>
            </div>
          <% end %>
        </div>

        <!-- List View - Compact Table -->
        <div :if={@view_mode == "list"} class="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Form Name</th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Version</th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Fields</th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Structure</th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Types</th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Updated</th>
                  <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <%= for form <- @grouped_forms do %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-4 py-3 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 mr-3">
                          <%= if form.has_buttons do %>
                            <.icon name="hero-cursor-arrow-rays" class="h-4 w-4 text-green-500" title="Has action buttons" />
                          <% else %>
                            <.icon name="hero-document-text" class="h-4 w-4 text-gray-400" />
                          <% end %>
                        </div>
                        <div class="min-w-0 flex-1">
                          <span class="text-sm font-medium text-gray-900 truncate max-w-32 block" title={form.name}>
                            <%= form.name %>
                          </span>
                        </div>
                      </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                      <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium bg-blue-100 text-blue-800">
                        v<%= form.version %>
                      </span>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                      <span class="text-sm text-gray-900"><%= form.field_count %></span>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                      <div class="text-sm text-gray-900">
                        <%= form.screen_count %> screens, <%= form.page_count %> pages
                      </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                      <div class="flex flex-wrap gap-1 max-w-32">
                        <%= for type <- Enum.take(form.field_types, 2) do %>
                          <span class="inline-block px-1.5 py-0.5 text-xs bg-gray-100 text-gray-700 rounded">
                            <%= type %>
                          </span>
                        <% end %>
                        <%= if length(form.field_types) > 2 do %>
                          <span class="inline-block px-1.5 py-0.5 text-xs bg-gray-100 text-gray-500 rounded">
                            +<%= length(form.field_types) - 2 %>
                          </span>
                        <% end %>
                      </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                      <span class="text-sm text-gray-500">
                        <%= Calendar.strftime(form.updated_at, "%b %d, %Y") %>
                      </span>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                      <div class="flex items-center space-x-2">
                        <button
                          phx-click="select_hierarchy"
                          phx-value-form={form.name}
                          class="inline-flex items-center px-2 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50"
                          title="View Details"
                        >
                          <.icon name="hero-eye" class="h-4 w-4" />
                        </button>
                        <.link
                          patch={~p"/mobileBanking/mobile-forms/new"}
                          class="inline-flex items-center px-2 py-1 border border-indigo-300 text-sm rounded text-indigo-700 bg-white hover:bg-indigo-50"
                          title="Add Field"
                        >
                          <.icon name="hero-plus" class="h-4 w-4" />
                        </.link>
                      </div>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Field Details Tab -->
  <div :if={@active_tab == "fields"} class="space-y-6">
    <!-- Filters -->
    <div class="bg-gray-50 p-4 rounded-lg">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Filters</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Form</label>
          <select phx-change="filter_by_form" name="form" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
            <option value="">All Forms</option>
            <%= for form <- @unique_forms do %>
              <option value={form} selected={form == @selected_form}><%= form %></option>
            <% end %>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Screen</label>
          <select phx-change="filter_by_screen" name="screen" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" disabled={is_nil(@selected_form)}>
            <option value="">All Screens</option>
            <%= for screen <- @unique_screens do %>
              <option value={screen} selected={screen == @selected_screen}><%= screen %></option>
            <% end %>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Page</label>
          <select phx-change="filter_by_page" name="page" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" disabled={is_nil(@selected_screen)}>
            <option value="">All Pages</option>
            <%= for page <- @unique_pages do %>
              <option value={page} selected={page == @selected_page}><%= page %></option>
            <% end %>
          </select>
        </div>
      </div>
    </div>

    <!-- Fields Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Form Fields</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
          <%= length(@fields) %> field(s) found
        </p>
      </div>
      <div class="border-t border-gray-200">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Form</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Screen</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Page</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Field Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Label</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submit To</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= for field <- @fields do %>
                <tr id={"field-#{field.field_id}"}>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><%= field.form %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= field.screen || "-" %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= field.page || "-" %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= field.field_name %></td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class={[
                      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                      if(field.field_type == "button", do: "bg-green-100 text-green-800", else: "bg-blue-100 text-blue-800")
                    ]}>
                      <%= field.field_type %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= field.label %></td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs">
                    <%= if field.submit_to do %>
                      <span class="truncate block" title={field.submit_to}><%= field.submit_to %></span>
                    <% else %>
                      <span class="text-gray-400">-</span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= if field.is_required do %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Required</span>
                    <% else %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Optional</span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= field.field_order %></td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <%= if field.active do %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                    <% else %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Inactive</span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex justify-end space-x-2">
                      <.link patch={~p"/mobileBanking/mobile-forms/#{field.field_id}/edit"} class="text-indigo-600 hover:text-indigo-900">
                        Edit
                      </.link>
                      <.link
                        phx-click={JS.push("delete", value: %{id: field.field_id}) |> JS.hide(to: "#field-#{field.field_id}")}
                        data-confirm="Are you sure?"
                        class="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </.link>
                    </div>
                  </td>
                </tr>
              <% end %>
              <%= if @fields == [] do %>
                <tr>
                  <td colspan="11" class="px-6 py-4 text-center text-gray-500">
                    No form fields found. <.link patch={~p"/mobileBanking/mobile-forms/new"} class="text-indigo-600 hover:text-indigo-900">Create the first field</.link>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Hierarchy Tab -->
  <div :if={@active_tab == "hierarchy"} class="h-screen flex flex-col">
    <!-- Split Screen Layout -->
    <div class="flex-1 flex gap-4 overflow-hidden">
      <!-- Left Side: Hierarchy Management -->
      <div class="w-1/2 flex flex-col space-y-4">
        <!-- Quick Create Forms -->
        <div class="bg-white shadow rounded-lg p-4">
          <h3 class="text-sm font-medium text-gray-900 mb-3">Quick Create</h3>
          <div class="grid grid-cols-3 gap-2">
            <.simple_form for={@create_form_form} phx-submit="create_form" class="col-span-1">
              <div class="flex gap-1">
                <.input field={@create_form_form[:form_name]} type="text" placeholder="Form name" required class="text-xs" />
                <.button type="submit" class="px-2 py-1 text-xs">+</.button>
              </div>
            </.simple_form>
            
            <.simple_form for={@create_screen_form} phx-submit="create_screen" class="col-span-1">
              <div class="flex gap-1">
                <.input field={@create_screen_form[:form_name]} type="select" options={[{"", ""} | Enum.map(@unique_forms, &{&1, &1})]} class="text-xs" />
                <.input field={@create_screen_form[:screen_name]} type="text" placeholder="Screen" class="text-xs" />
                <.button type="submit" class="px-2 py-1 text-xs">+</.button>
              </div>
            </.simple_form>
            
            <.simple_form for={@create_page_form} phx-submit="create_page" class="col-span-1">
              <div class="flex gap-1">
                <.input field={@create_page_form[:page_name]} type="text" placeholder="Page" class="text-xs" />
                <.button type="submit" class="px-2 py-1 text-xs">+</.button>
              </div>
            </.simple_form>
          </div>
        </div>

        <!-- Visual Hierarchy Tree -->
        <div class="flex-1 bg-white shadow rounded-lg p-4 overflow-y-auto">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Hierarchy Navigator</h3>
          <div class="space-y-3">
            <%= for form <- @unique_forms do %>
              <div class="border rounded-lg p-3 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between cursor-pointer" 
                     phx-click="select_hierarchy" 
                     phx-value-form={form}>
                  <div class="flex items-center">
                    <.icon name="hero-folder" class="h-5 w-5 text-blue-500 mr-2" />
                    <span class={[
                      "font-medium", 
                      if(@selected_form == form, do: "text-blue-600", else: "text-gray-900")
                    ]}><%= form %></span>
                  </div>
                  <span class="text-xs text-gray-500">
                    <%= Enum.count(Enum.filter(@fields, &(&1.form == form))) %> fields
                  </span>
                </div>
                
                <!-- Screens for this form -->
                <%= if @selected_form == form do %>
                  <%= for screen <- Enum.filter(@fields, &(&1.form == form and not is_nil(&1.screen))) |> Enum.map(& &1.screen) |> Enum.uniq() |> Enum.sort() do %>
                    <div class="ml-6 mt-2">
                      <div class="flex items-center justify-between cursor-pointer p-2 rounded hover:bg-gray-50" 
                           phx-click="select_hierarchy" 
                           phx-value-form={form} 
                           phx-value-screen={screen}>
                        <div class="flex items-center">
                          <.icon name="hero-computer-desktop" class="h-4 w-4 text-green-500 mr-2" />
                          <span class={[
                            "text-sm",
                            if(@selected_screen == screen, do: "text-green-600 font-medium", else: "text-gray-700")
                          ]}><%= screen %></span>
                        </div>
                        <span class="text-xs text-gray-500">
                          <%= Enum.count(Enum.filter(@fields, &(&1.form == form and &1.screen == screen))) %> fields
                        </span>
                      </div>
                      
                      <!-- Pages for this screen -->
                      <%= if @selected_screen == screen do %>
                        <%= for page <- Enum.filter(@fields, &(&1.form == form and &1.screen == screen and not is_nil(&1.page))) |> Enum.map(& &1.page) |> Enum.uniq() |> Enum.sort() do %>
                          <div class="ml-8 mt-1">
                            <div class="flex items-center justify-between cursor-pointer p-2 rounded hover:bg-gray-50" 
                                 phx-click="select_hierarchy" 
                                 phx-value-form={form} 
                                 phx-value-screen={screen} 
                                 phx-value-page={page}>
                              <div class="flex items-center">
                                <.icon name="hero-document" class="h-3 w-3 text-purple-500 mr-2" />
                                <span class={[
                                  "text-sm",
                                  if(@selected_page == page, do: "text-purple-600 font-medium", else: "text-gray-600")
                                ]}><%= page %></span>
                              </div>
                              <span class="text-xs text-gray-500">
                                <%= Enum.count(Enum.filter(@fields, &(&1.form == form and &1.screen == screen and &1.page == page))) %> fields
                              </span>
                            </div>
                          </div>
                        <% end %>
                      <% end %>
                    </div>
                  <% end %>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Right Side: Mobile Preview -->
      <div class="w-1/2 flex justify-center items-start pt-8">
        <div class="mobile-phone-container">
          <!-- Mobile Phone Frame -->
          <div class="mobile-phone">
            <!-- Phone Header -->
            <div class="mobile-header">
              <div class="status-bar">
                <span class="time">9:41</span>
                <div class="battery-indicator">
                  <div class="battery"></div>
                </div>
              </div>
              <div class="app-header">
                <h4 class="app-title">
                  <%= if @selected_form, do: String.capitalize(@selected_form), else: "Mobile Form" %>
                </h4>
              </div>
            </div>

            <!-- Phone Screen Content -->
            <div class="mobile-screen">
              <%= if @selected_form do %>
                <!-- Form Fields Preview -->
                <div class="form-preview">
                  <%= for field <- Enum.filter(@fields, fn f -> 
                    f.form == @selected_form and
                    (is_nil(@selected_screen) or f.screen == @selected_screen) and
                    (is_nil(@selected_page) or f.page == @selected_page)
                  end) |> Enum.sort_by(& &1.field_order) do %>
                    <div class="form-field">
                      <label class="field-label">
                        <%= field.label %>
                        <%= if field.is_required do %>
                          <span class="required">*</span>
                        <% end %>
                      </label>
                      <%= case field.field_type do %>
                        <% "select" -> %>
                          <select class="field-input">
                            <option>Choose option...</option>
                          </select>
                        <% "textarea" -> %>
                          <textarea class="field-input" rows="3" placeholder={"Enter #{field.label}"}></textarea>
                        <% "boolean" -> %>
                          <div class="checkbox-container">
                            <input type="checkbox" class="field-checkbox" />
                            <span>Yes</span>
                          </div>
                        <% "button" -> %>
                          <button class="action-button" title={field.submit_to || "No action defined"}>
                            <%= field.label %>
                          </button>
                        <% _ -> %>
                          <input type={mobile_input_type(field.field_type)} 
                                 class="field-input" 
                                 placeholder={"Enter #{field.label}"} />
                      <% end %>
                    </div>
                  <% end %>
                  
                  <%= if length(Enum.filter(@fields, fn f -> 
                    f.form == @selected_form and
                    (is_nil(@selected_screen) or f.screen == @selected_screen) and
                    (is_nil(@selected_page) or f.page == @selected_page)
                  end)) == 0 do %>
                    <div class="empty-form">
                      <p>No fields to display</p>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="empty-state">
                  <div class="empty-icon">📱</div>
                  <p>Select a form to preview</p>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- USSD Management Tab -->
  <div :if={@active_tab == "ussd"} class="h-screen flex flex-col">
    <!-- Split Screen Layout -->
    <div class="flex-1 flex gap-4 overflow-hidden">
      <!-- Left Side: USSD Menu Management (60%) -->
      <div class="w-3/5 flex flex-col space-y-4">
        <!-- Header with Add Menu Button -->
        <div class="bg-white shadow rounded-lg p-4">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">USSD Menu Builder</h3>
            <.button phx-click="add_ussd_menu" class="bg-green-600 hover:bg-green-700">
              <.icon name="hero-plus" class="h-4 w-4 mr-2" />
              Add Menu
            </.button>
          </div>
        </div>

        <!-- USSD Menus List -->
        <div class="bg-white shadow rounded-lg flex-1 overflow-hidden">
          <div class="p-4 border-b border-gray-200">
            <h4 class="text-sm font-medium text-gray-900">USSD Menus</h4>
          </div>
          <div class="p-4 space-y-4 overflow-y-auto">
            <%= if length(@ussd_menus) == 0 do %>
              <div class="text-center py-8">
                <.icon name="hero-phone" class="mx-auto h-12 w-12 text-gray-400" />
                <h3 class="mt-2 text-sm font-medium text-gray-900">No USSD menus</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your first USSD menu.</p>
              </div>
            <% else %>
              <%= for {menu, menu_index} <- Enum.with_index(@ussd_menus) do %>
                <div class="border border-gray-200 rounded-lg p-4">
                  <!-- Menu Header -->
                  <div class="flex justify-between items-start mb-3">
                    <div>
                      <h5 class="text-sm font-medium text-gray-900"><%= menu.title %></h5>
                      <p class="text-xs text-gray-500">Menu <%= menu_index + 1 %> • <%= length(menu.options || []) %> options</p>
                    </div>
                    <div class="flex space-x-2">
                      <.button phx-click="edit_ussd_menu" phx-value-index={menu_index} size="sm" variant="outline">
                        <.icon name="hero-pencil" class="h-3 w-3" />
                      </.button>
                      <.button phx-click="delete_ussd_menu" phx-value-index={menu_index} size="sm" variant="outline" class="text-red-600 hover:text-red-700">
                        <.icon name="hero-trash" class="h-3 w-3" />
                      </.button>
                    </div>
                  </div>

                  <!-- Menu Options -->
                  <div class="space-y-2">
                    <%= for {option, option_index} <- Enum.with_index(menu.options || []) do %>
                      <div class="flex items-center justify-between bg-gray-50 rounded p-2">
                        <div class="flex-1">
                          <span class="text-sm text-gray-900"><%= option_index + 1 %>. <%= option.text %></span>
                          <span class="ml-2 text-xs text-gray-500">
                            (<%= option.action %>)
                            <%= if option.action == "submenu" and option.target_menu_id do %>
                              → Menu <%= Enum.find_index(@ussd_menus, &(&1.menu_id == option.target_menu_id)) %>
                            <% end %>
                          </span>
                        </div>
                        <div class="flex space-x-1">
                          <.button phx-click="edit_ussd_option" phx-value-menu-index={menu_index} phx-value-option-index={option_index} size="sm" variant="outline">
                            <.icon name="hero-pencil" class="h-3 w-3" />
                          </.button>
                          <.button phx-click="delete_ussd_option" phx-value-menu-index={menu_index} phx-value-option-index={option_index} size="sm" variant="outline" class="text-red-600">
                            <.icon name="hero-trash" class="h-3 w-3" />
                          </.button>
                        </div>
                      </div>
                    <% end %>
                    
                    <!-- Add Option Button -->
                    <.button phx-click="add_ussd_option" phx-value-menu-index={menu_index} variant="outline" size="sm" class="w-full">
                      <.icon name="hero-plus" class="h-3 w-3 mr-1" />
                      Add Option
                    </.button>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Right Side: USSD Simulator (40%) -->
      <div class="w-2/5 flex flex-col space-y-4">
        <!-- USSD Simulator -->
        <div class="bg-white shadow rounded-lg p-4 flex-1">
          <div class="flex justify-between items-center mb-4">
            <h4 class="text-sm font-medium text-gray-900">USSD Simulator</h4>
            <div class="flex space-x-2">
              <.button phx-click="test_ussd_flow" size="sm" variant="outline">
                <.icon name="hero-play" class="h-3 w-3 mr-1" />
                Test Flow
              </.button>
              <.button phx-click="reset_ussd_simulation" size="sm" variant="outline">
                <.icon name="hero-arrow-path" class="h-3 w-3 mr-1" />
                Reset
              </.button>
            </div>
          </div>

          <!-- USSD Device Mockup -->
          <div class="flex justify-center">
            <div class="ussd-device">
              <div class="ussd-device-header">
                <div class="ussd-status-bar">
                  <span class="text-xs">9:41</span>
                  <div class="ussd-signal-bars">
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                  </div>
                </div>
              </div>
              
              <div class="ussd-screen" id="ussd-screen">
                <%= if @form_input_mode && @current_form_field do %>
                  <!-- Form Input Mode -->
                  <div class="ussd-content">
                    <div class="ussd-title">Form: <%= @form_session.form_name %></div>
                    <div class="form-field-prompt">
                      <%= if @current_form_field.field_type == "button" do %>
                        <p class="text-sm font-medium text-center">
                          Enter <%= @current_form_field.field_order || (@form_session.current_field_index + 1) %> to continue
                        </p>
                      <% else %>
                        <p class="text-sm font-medium">
                          <%= @current_form_field.label || @current_form_field.field_name %>-<%= @form_session.current_field_index + 1 %>.
                        </p>
                        <p class="text-xs text-gray-600 mt-1">
                          Enter <%= String.downcase(@current_form_field.label || @current_form_field.field_name) %>
                        </p>
                      <% end %>
                    </div>
                    
                    <!-- Current Input Display -->
                    <div class="mt-3 p-2 bg-gray-100 rounded border text-sm">
                      <div class="flex justify-between items-center">
                        <span class="text-gray-600">Input:</span>
                        <%= if @letter_input_mode do %>
                          <span class="text-blue-600 text-xs">ABC Mode</span>
                        <% else %>
                          <span class="text-gray-600 text-xs">123 Mode</span>
                        <% end %>
                      </div>
                      <div class="font-mono text-base mt-1">
                        <%= if String.length(@current_letter_input) > 0 do %>
                          <%= @current_letter_input %>
                        <% else %>
                          <span class="text-gray-400">_</span>
                        <% end %>
                      </div>
                    </div>
                    
                    <form phx-submit="form_input_submit" class="mt-4">
                      <input type="hidden" name="input" value={@current_letter_input} />
                      <div class="grid grid-cols-2 gap-2">
                        <button type="submit" class="px-3 py-1 bg-green-600 text-white text-xs rounded">
                          Submit
                        </button>
                        <button type="button" phx-click="clear_input" class="px-3 py-1 bg-orange-600 text-white text-xs rounded">
                          Clear
                        </button>
                        <button type="button" phx-click="cancel_form_input" class="px-3 py-1 bg-red-600 text-white text-xs rounded">
                          Cancel
                        </button>
                        <button type="button" phx-click="ussd_key_press" phx-value-key="*" class="px-3 py-1 bg-blue-600 text-white text-xs rounded">
                          <%= if @letter_input_mode, do: "123", else: "ABC" %>
                        </button>
                      </div>
                    </form>
                  </div>
                <% else %>
                  <!-- Regular USSD Menu Mode -->
                  <%= if @current_ussd_menu do %>
                    <div class="ussd-content">
                      <div class="ussd-title"><%= @current_ussd_menu.title %></div>
                      
                      <!-- Scroll indicators -->
                      <%= if length(@current_ussd_menu.options || []) > 6 do %>
                        <div class="scroll-indicator top">▲ More above</div>
                        <div class="scroll-indicator bottom">▼ More below</div>
                      <% end %>
                      
                      <div class="ussd-options" id="ussd-options">
                        <%= for {option, index} <- Enum.with_index(@current_ussd_menu.options || []) do %>
                          <div class="ussd-option" phx-click="simulate_ussd_option" phx-value-option={index}>
                            <%= index + 1 %>. <%= option.text %>
                            <%= if option.action == "form" && option.form_name do %>
                              <span class="text-xs text-blue-500">[Form: <%= option.form_name %>]</span>
                            <% end %>
                          </div>
                        <% end %>
                        
                        <%= if length(@current_ussd_menu.options || []) > 0 do %>
                          <div class="ussd-option ussd-nav-option" phx-click="ussd_go_back">0. Back</div>
                          <div class="ussd-option ussd-nav-option" phx-click="ussd_cancel">#. Cancel</div>
                        <% end %>
                      </div>
                    </div>
                  <% else %>
                    <div class="ussd-content">
                      <div class="text-center py-8">
                        <p class="text-sm text-gray-500">No USSD menu selected</p>
                        <p class="text-xs text-gray-400 mt-1">Create a menu to start testing</p>
                      </div>
                    </div>
                  <% end %>
                <% end %>
              </div>
              
              <!-- Navigation Buttons (only show in form input mode) -->
              <%= if @form_input_mode do %>
                <div class="ussd-nav-buttons p-2 border-t">
                  <div class="grid grid-cols-3 gap-2">
                    <button 
                      phx-click="ussd_key_press" 
                      phx-value-key="#" 
                      class="px-2 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700"
                    >
                      ← Back
                    </button>
                    <button 
                      phx-click="form_input_submit" 
                      class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700"
                    >
                      OK
                    </button>
                    <button 
                      phx-click="cancel_form_input" 
                      class="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              <% end %>
              
              <!-- USSD Keypad -->
              <div class="ussd-keypad">
                <!-- Row 1 -->
                <div class="keypad-row">
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="1">
                    <span class="key-number">1</span>
                  </button>
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="2">
                    <span class="key-number">2</span>
                    <span class="key-letters">ABC</span>
                  </button>
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="3">
                    <span class="key-number">3</span>
                    <span class="key-letters">DEF</span>
                  </button>
                </div>
                
                <!-- Row 2 -->
                <div class="keypad-row">
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="4">
                    <span class="key-number">4</span>
                    <span class="key-letters">GHI</span>
                  </button>
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="5">
                    <span class="key-number">5</span>
                    <span class="key-letters">JKL</span>
                  </button>
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="6">
                    <span class="key-number">6</span>
                    <span class="key-letters">MNO</span>
                  </button>
                </div>
                
                <!-- Row 3 -->
                <div class="keypad-row">
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="7">
                    <span class="key-number">7</span>
                    <span class="key-letters">PQRS</span>
                  </button>
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="8">
                    <span class="key-number">8</span>
                    <span class="key-letters">TUV</span>
                  </button>
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="9">
                    <span class="key-number">9</span>
                    <span class="key-letters">WXYZ</span>
                  </button>
                </div>
                
                <!-- Row 4 -->
                <div class="keypad-row">
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="*">
                    <span class="key-number">*</span>
                    <%= if @form_input_mode do %>
                      <span class="key-letters"><%= if @letter_input_mode, do: "123", else: "ABC" %></span>
                    <% end %>
                  </button>
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="0">
                    <span class="key-number">0</span>
                    <%= if @form_input_mode do %>
                      <span class="key-letters">SPACE</span>
                    <% end %>
                  </button>
                  <button class="keypad-button" phx-click="ussd_key_press" phx-value-key="#">
                    <span class="key-number">#</span>
                    <%= if @form_input_mode do %>
                      <span class="key-letters">BACK</span>
                    <% end %>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Generated USSD Code -->
        <div class="bg-white shadow rounded-lg p-4">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Generated USSD Code</h4>
          <%= if @ussd_code do %>
            <pre class="text-xs bg-gray-100 p-3 rounded border overflow-x-auto"><%= @ussd_code %></pre>
          <% else %>
            <p class="text-sm text-gray-500">Code will be generated when you create USSD menus</p>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- USSD Edit Menu Modal -->
<.modal :if={@editing_ussd_menu} id="ussd-menu-modal" show on_cancel={JS.push("cancel_ussd_edit")}>
  <.header>
    Edit USSD Menu
    <:subtitle>Update the menu title and settings</:subtitle>
  </.header>
  
  <.simple_form for={@ussd_menu_form} phx-submit="save_ussd_menu">
    <.input field={@ussd_menu_form[:title]} type="text" label="Menu Title" required />
    <input type="hidden" name="ussd_menu[menu_id]" value={@ussd_menu_form[:menu_id].value} />
    <input type="hidden" name="ussd_menu[menu_index]" value={@ussd_menu_form[:menu_index].value} />
    
    <:actions>
      <.button type="submit">Save Menu</.button>
      <.button type="button" phx-click="cancel_ussd_edit" variant="outline">Cancel</.button>
    </:actions>
  </.simple_form>
</.modal>

<!-- USSD Edit Option Modal -->
<.modal :if={@editing_ussd_option} id="ussd-option-modal" show on_cancel={JS.push("cancel_ussd_edit")}>
  <.header>
    Edit USSD Option
    <:subtitle>Update the option text and behavior</:subtitle>
  </.header>
  
  <.simple_form for={@ussd_option_form} phx-submit="save_ussd_option">
    <.input field={@ussd_option_form[:text]} type="text" label="Option Text" required />
    
    <div x-data={"{ actionType: '#{@ussd_option_form[:action].value}' }"}>
      <.input 
        field={@ussd_option_form[:action]} 
        type="select" 
        label="Action Type" 
        options={[
          {"None", "none"},
          {"Go to Sub-Menu", "submenu"},
          {"Execute Function", "function"},
          {"Collect Form Data", "form"},
          {"End Session", "end"}
        ]}
        x-model="actionType"
      />
      
      <div x-show="actionType === 'submenu'" x-transition>
        <.input field={@ussd_option_form[:target_menu_id]} type="select" label="Target Menu" options={[
          {"Select a menu...", ""} | 
          Enum.map(@ussd_menus, fn menu -> 
            {menu.title, menu.menu_id} 
          end)
        ]} />
      </div>
      
      <div x-show="actionType === 'form'" x-transition>
        <.input field={@ussd_option_form[:form_name]} type="select" label="Form to Load" options={[
          {"Select a form...", ""} | 
          Enum.map(@unique_forms, fn form_name -> 
            {form_name, form_name} 
          end)
        ]} />
      </div>
    </div>
    
    <input type="hidden" name="ussd_option[option_id]" value={@ussd_option_form[:option_id].value} />
    <input type="hidden" name="ussd_option[menu_index]" value={@ussd_option_form[:menu_index].value} />
    <input type="hidden" name="ussd_option[option_index]" value={@ussd_option_form[:option_index].value} />
    
    <:actions>
      <.button type="submit">Save Option</.button>
      <.button type="button" phx-click="cancel_ussd_edit" variant="outline">Cancel</.button>
    </:actions>
  </.simple_form>
</.modal>

<!-- Modal for Edit/New -->
<.modal :if={@live_action in [:new, :edit]} id="field-modal" show on_cancel={JS.patch(~p"/mobileBanking/mobile-forms")}>
  <.live_component
    module={ServiceManagerWeb.Backend.MobileFormsLive.FormComponent}
    id={@field.field_id || :new}
    title={@page_title}
    action={@live_action}
    field={@field}
    patch={~p"/mobileBanking/mobile-forms"}
  />
</.modal>

<style>
  /* Mobile Phone CSS Template */
  .mobile-phone-container {
    perspective: 1000px;
  }

  .mobile-phone {
    width: 300px;
    height: 620px;
    background: #1a1a1a;
    border-radius: 35px;
    box-shadow: 
      0 25px 50px rgba(0,0,0,0.4),
      inset 0 1px 2px rgba(255,255,255,0.1);
    position: relative;
    transform: rotateX(2deg) rotateY(-2deg);
    transition: transform 0.3s ease;
    padding: 15px;
  }

  .mobile-phone:hover {
    transform: rotateX(0deg) rotateY(0deg);
  }

  .mobile-phone::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 6px;
    background: #333;
    border-radius: 3px;
  }

  .mobile-phone::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 12px;
    background: #222;
    border-radius: 50%;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.5);
  }

  .mobile-header {
    background: #000000;
    border-radius: 25px 25px 0 0;
    padding: 8px 16px;
    height: 30px;
    display: flex;
    align-items: center;
  }

  .status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    font-size: 12px;
    font-weight: 600;
    color: #ffffff;
  }

  .time {
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  }

  .battery-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .battery {
    width: 22px;
    height: 11px;
    border: 1.5px solid #ffffff;
    border-radius: 2px;
    position: relative;
    background: linear-gradient(90deg, #4ade80 70%, transparent 70%);
  }

  .battery::after {
    content: '';
    position: absolute;
    right: -4px;
    top: 2px;
    width: 2px;
    height: 7px;
    background: #ffffff;
    border-radius: 0 1px 1px 0;
  }

  .app-header {
    text-align: center;
  }

  .app-title {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }

  .mobile-screen {
    background: #ffffff;
    height: 540px;
    border-radius: 0 0 25px 25px;
    overflow-y: auto;
    padding: 20px;
    position: relative;
    border: 2px solid #000;
  }

  .form-preview {
    space-y: 12px;
  }

  .form-field {
    margin-bottom: 12px;
  }

  .field-label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
  }

  .required {
    color: #ef4444;
    margin-left: 2px;
  }

  .field-input {
    width: 100%;
    padding: 8px 10px;
    font-size: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: #ffffff;
    transition: border-color 0.2s ease;
  }

  .field-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }

  .field-checkbox {
    width: 14px;
    height: 14px;
    margin-right: 6px;
  }

  .checkbox-container {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #374151;
  }

  .action-button {
    width: 100%;
    padding: 8px;
    background: linear-gradient(135deg, #10b981, #059669);
    color: #ffffff;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    margin-top: 4px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
  }

  .action-button:hover {
    background: linear-gradient(135deg, #059669, #047857);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
    transform: translateY(-1px);
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
  }

  .empty-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .empty-form {
    text-align: center;
    padding: 20px;
    color: #6b7280;
    font-size: 12px;
  }

  /* Mobile Screen Scrollbar */
  .mobile-screen::-webkit-scrollbar {
    width: 3px;
  }

  .mobile-screen::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .mobile-screen::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .mobile-screen::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Responsive adjustments */
  @media (max-width: 1024px) {
    .mobile-phone {
      width: 280px;
      height: 580px;
      padding: 12px;
    }
    
    .mobile-screen {
      height: 500px;
      padding: 16px;
    }

    .ussd-device {
      width: 220px;
      height: 440px;
    }

    .ussd-screen {
      height: 160px;
      padding: 10px;
    }
  }

  /* ZTE Dumb Phone Styles */
  .ussd-device {
    width: 240px;
    height: 480px;
    background: linear-gradient(145deg, #2d3748, #1a202c);
    border-radius: 20px;
    box-shadow: 
      0 15px 35px rgba(0,0,0,0.5),
      inset 0 2px 4px rgba(255,255,255,0.08);
    position: relative;
    overflow: hidden;
    border: 3px solid #4a5568;
  }

  .ussd-device::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 8px;
    background: #1a1a1a;
    border-radius: 4px;
    border: 1px solid #555;
  }

  .ussd-device-header {
    background: #000000;
    height: 20px;
    border-radius: 17px 17px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }

  .ussd-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0 15px;
    color: #ffffff;
    font-size: 10px;
    font-weight: 600;
  }

  .ussd-signal-bars {
    display: flex;
    gap: 2px;
  }

  .signal-bar {
    width: 3px;
    height: 8px;
    background: #ffffff;
    border-radius: 1px;
  }

  .signal-bar:nth-child(1) { height: 4px; }
  .signal-bar:nth-child(2) { height: 6px; }
  .signal-bar:nth-child(3) { height: 8px; }
  .signal-bar:nth-child(4) { height: 10px; }

  .ussd-screen {
    background: #0a0a0a;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    padding: 12px;
    height: 180px;
    overflow-y: auto;
    border: 2px solid #333333;
    margin: 8px;
    border-radius: 3px;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.6);
    /* Enhanced scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: #00ff00 #1a1a1a;
    /* Smooth scrolling */
    scroll-behavior: smooth;
    position: relative;
  }

  .ussd-screen::-webkit-scrollbar {
    width: 6px;
  }

  .ussd-screen::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 3px;
  }

  .ussd-screen::-webkit-scrollbar-thumb {
    background: #00ff00;
    border-radius: 3px;
    opacity: 0.7;
  }

  .ussd-screen::-webkit-scrollbar-thumb:hover {
    background: #00cc00;
    opacity: 1;
  }

  .ussd-content {
    line-height: 1.4;
    min-height: 100%;
    display: flex;
    flex-direction: column;
  }

  .ussd-title {
    color: #ffffff;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
    border-bottom: 1px solid #333333;
    padding-bottom: 5px;
  }

  .ussd-options {
    margin-top: 10px;
    flex: 1;
    overflow-y: auto;
    max-height: calc(100% - 40px);
    padding-right: 2px;
  }

  .ussd-options::-webkit-scrollbar {
    width: 4px;
  }

  .ussd-options::-webkit-scrollbar-track {
    background: #0a0a0a;
  }

  .ussd-options::-webkit-scrollbar-thumb {
    background: #00ff00;
    border-radius: 2px;
    opacity: 0.5;
  }

  /* Scroll indicators */
  .scroll-indicator {
    position: absolute;
    right: 8px;
    color: #00ff00;
    font-size: 8px;
    z-index: 10;
    animation: blink 1.5s infinite;
  }

  .scroll-indicator.top {
    top: 15px;
  }

  .scroll-indicator.bottom {
    bottom: 15px;
  }

  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
  }

  /* Form field prompt area enhancement */
  .form-field-prompt {
    flex-shrink: 0;
    margin-bottom: 8px;
  }

  .ussd-option {
    padding: 4px 0;
    cursor: pointer;
    transition: background-color 0.2s;
    border-radius: 2px;
    padding-left: 4px;
  }

  .ussd-option:hover {
    background-color: rgba(0, 255, 0, 0.1);
  }

  .ussd-nav-option {
    color: #ffff00;
    margin-top: 8px;
    border-top: 1px solid #333333;
    padding-top: 8px;
  }

  .ussd-keypad {
    padding: 12px;
    background: #2d3748;
    border-radius: 0 0 17px 17px;
  }

  .keypad-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    gap: 4px;
  }

  .keypad-button {
    width: 45px;
    height: 35px;
    background: linear-gradient(145deg, #4a5568, #2d3748);
    color: #ffffff;
    border: 2px solid #555;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 
      0 2px 4px rgba(0,0,0,0.4),
      inset 0 1px 2px rgba(255,255,255,0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .key-number {
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
  }

  .key-letters {
    font-size: 7px;
    font-weight: normal;
    color: #cccccc;
    line-height: 1;
    margin-top: 1px;
  }

  .keypad-button:hover {
    background: linear-gradient(145deg, #5a6578, #3d4758);
    transform: translateY(-1px);
    box-shadow: 
      0 4px 8px rgba(0,0,0,0.5),
      inset 0 1px 2px rgba(255,255,255,0.2);
  }

  .keypad-button:active {
    transform: translateY(1px);
    box-shadow: 
      0 1px 2px rgba(0,0,0,0.4),
      inset 0 2px 4px rgba(0,0,0,0.3);
  }
</style>