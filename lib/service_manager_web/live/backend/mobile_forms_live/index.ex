defmodule ServiceManagerWeb.Backend.MobileFormsLive.Index do
  use ServiceManagerWeb, :live_view
  alias ServiceManagerWeb.Api.Services.Local.MobileAppFormsService
  alias ServiceManagerWeb.Api.Services.Local.UssdService
  alias ServiceManagerWeb.Api.MobileAppFormsSchema
  alias ServiceManagerWeb.Api.UssdMenuSchema
  alias ServiceManagerWeb.Api.UssdOptionSchema
  import ServiceManagerWeb.Utilities.PermissionHelpers

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/mobile-forms")
      |> assign(:search_query, "")
      |> assign(:selected_form_filter, "all")
      |> assign(:selected_type, "all")
      |> assign(:sort_by, "name")
      |> assign(:view_mode, "grid")
      |> assign(:active_tab, "forms")
      |> assign(:selected_form, nil)
      |> assign(:selected_screen, nil)
      |> assign(:selected_page, nil)
      |> assign(:create_form_form, to_form(%{}, as: :create_form))
      |> assign(:create_screen_form, to_form(%{}, as: :create_screen))
      |> assign(:create_page_form, to_form(%{}, as: :create_page))
      |> assign(:ussd_menus, [])
      |> assign(:current_ussd_menu, nil)
      |> assign(:ussd_menu_stack, [])
      |> assign(:ussd_code, nil)
      |> assign(:editing_ussd_menu, nil)
      |> assign(:editing_ussd_option, nil)
      |> assign(:ussd_menu_form, nil)
      |> assign(:ussd_option_form, nil)
      |> assign(:form_session, nil)
      |> assign(:current_form_field, nil)
      |> assign(:form_input_mode, false)
      |> assign(:letter_input_mode, false)
      |> assign(:current_letter_input, "")
      |> assign(:last_key_press, nil)
      |> assign(:key_press_count, 0)

    if connected?(socket) do
      :timer.send_interval(30_000, :refresh)
    end

    {:ok, load_data(socket) |> load_ussd_data()}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    case MobileAppFormsService.get_field(id) do
      {:ok, field} ->
        socket
        |> assign(:page_title, "Edit Mobile Form Field")
        |> assign(:field, field)
      {:error, _} ->
        socket
        |> put_flash(:error, "Field not found")
        |> push_navigate(to: ~p"/mobileBanking/mobile-forms")
    end
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Mobile Form Field")
    |> assign(:field, %MobileAppFormsSchema{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Mobile Forms Management")
    |> assign(:field, nil)
  end

  @impl true
  def handle_info(:refresh, socket) do
    {:noreply, load_data(socket) |> load_ussd_data()}
  end

  def handle_info({ServiceManagerWeb.Backend.MobileFormsLive.FormComponent, {:saved, _field}}, socket) do
    {:noreply,
     socket
     |> put_flash(:info, "Field saved successfully")
     |> push_navigate(to: ~p"/mobileBanking/mobile-forms")
     |> load_data()}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    case MobileAppFormsService.delete_field(id) do
      {:ok, _message} ->
        {:noreply,
         socket
         |> put_flash(:info, "Field deleted successfully")
         |> load_data()}
      {:error, message} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to delete field: #{message}")}
    end
  end

  def handle_event("select_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :active_tab, tab)}
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    {:noreply,
     socket
     |> assign(:search_query, query)
     |> load_data()}
  end

  def handle_event("filter", %{"filter" => filters}, socket) do
    {:noreply,
     socket
     |> assign(:selected_form_filter, Map.get(filters, "form", "all"))
     |> assign(:selected_type, Map.get(filters, "type", "all"))
     |> load_data()}
  end

  def handle_event("sort", %{"sort_by" => sort_by}, socket) do
    {:noreply,
     socket
     |> assign(:sort_by, sort_by)
     |> load_data()}
  end

  def handle_event("toggle_view", %{"view" => view}, socket) do
    {:noreply, assign(socket, :view_mode, view)}
  end

  def handle_event("filter_by_form", %{"form" => form}, socket) do
    form = if form == "", do: nil, else: form
    
    socket = 
      socket
      |> assign(:selected_form, form)
      |> assign(:selected_screen, nil)
      |> assign(:selected_page, nil)
      |> load_filtered_data()

    {:noreply, socket}
  end

  def handle_event("filter_by_screen", %{"screen" => screen}, socket) do
    screen = if screen == "", do: nil, else: screen
    
    socket = 
      socket
      |> assign(:selected_screen, screen)
      |> assign(:selected_page, nil)
      |> load_filtered_data()

    {:noreply, socket}
  end

  def handle_event("filter_by_page", %{"page" => page}, socket) do
    page = if page == "", do: nil, else: page
    
    socket = 
      socket
      |> assign(:selected_page, page)
      |> load_filtered_data()

    {:noreply, socket}
  end

  def handle_event("create_form", %{"create_form" => %{"form_name" => form_name, "version" => version}}, socket) do
    case MobileAppFormsService.create_form(form_name, version) do
      {:ok, _result} ->
        {:noreply,
         socket
         |> put_flash(:info, "Form '#{form_name}' created successfully")
         |> assign(:create_form_form, to_form(%{}, as: :create_form))
         |> load_data()}
      {:error, message} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to create form: #{message}")}
    end
  end

  def handle_event("create_screen", %{"create_screen" => %{"form_name" => form_name, "screen_name" => screen_name, "version" => version}}, socket) do
    case MobileAppFormsService.create_screen(form_name, screen_name, version) do
      {:ok, _result} ->
        {:noreply,
         socket
         |> put_flash(:info, "Screen '#{screen_name}' created successfully")
         |> assign(:create_screen_form, to_form(%{}, as: :create_screen))
         |> load_data()}
      {:error, message} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to create screen: #{message}")}
    end
  end

  def handle_event("create_page", %{"create_page" => %{"form_name" => form_name, "screen_name" => screen_name, "page_name" => page_name, "version" => version}}, socket) do
    case MobileAppFormsService.create_page(form_name, screen_name, page_name, version) do
      {:ok, _result} ->
        {:noreply,
         socket
         |> put_flash(:info, "Page '#{page_name}' created successfully")
         |> assign(:create_page_form, to_form(%{}, as: :create_page))
         |> load_data()}
      {:error, message} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to create page: #{message}")}
    end
  end

  def handle_event("select_hierarchy", params, socket) do
    form = Map.get(params, "form")
    screen = Map.get(params, "screen")
    page = Map.get(params, "page")
    
    socket = 
      socket
      |> assign(:selected_form, form)
      |> assign(:selected_screen, screen)
      |> assign(:selected_page, page)
      |> load_filtered_data()

    {:noreply, socket}
  end

  # USSD Event Handlers
  def handle_event("add_ussd_menu", _params, socket) do
    case UssdService.create_menu(%{
      title: "New Menu",
      version: "1.0",
      menu_order: length(socket.assigns.ussd_menus)
    }) do
      {:ok, _menu} ->
        socket = 
          socket
          |> load_ussd_data()
          |> generate_ussd_code()
          |> put_flash(:info, "Menu created successfully")
        
        {:noreply, socket}
        
      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to create menu: #{message}")}
    end
  end

  def handle_event("edit_ussd_menu", %{"index" => index}, socket) do
    index = String.to_integer(index)
    menu = Enum.at(socket.assigns.ussd_menus, index)
    
    form_data = %{
      "title" => menu.title || "",
      "menu_id" => menu.menu_id,
      "menu_index" => index
    }
    
    socket = 
      socket
      |> assign(:editing_ussd_menu, {index, menu})
      |> assign(:ussd_menu_form, to_form(form_data, as: :ussd_menu))
    
    {:noreply, socket}
  end

  def handle_event("delete_ussd_menu", %{"index" => index}, socket) do
    index = String.to_integer(index)
    menu = Enum.at(socket.assigns.ussd_menus, index)
    
    case UssdService.delete_menu(menu.menu_id) do
      {:ok, _message} ->
        socket = 
          socket
          |> load_ussd_data()
          |> assign(:current_ussd_menu, List.first(socket.assigns.ussd_menus))
          |> generate_ussd_code()
          |> put_flash(:info, "Menu deleted successfully")
        
        {:noreply, socket}
        
      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to delete menu: #{message}")}
    end
  end

  def handle_event("add_ussd_option", %{"menu-index" => menu_index}, socket) do
    menu_index = String.to_integer(menu_index)
    menu = Enum.at(socket.assigns.ussd_menus, menu_index)
    
    case UssdService.create_option(%{
      menu_id: menu.menu_id,
      text: "New Option",
      action: "none",
      option_order: length(menu.options || [])
    }) do
      {:ok, _option} ->
        socket = 
          socket
          |> load_ussd_data()
          |> generate_ussd_code()
          |> put_flash(:info, "Option added successfully")
        
        {:noreply, socket}
        
      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to add option: #{message}")}
    end
  end

  def handle_event("edit_ussd_option", %{"menu-index" => menu_index, "option-index" => option_index}, socket) do
    menu_index = String.to_integer(menu_index)
    option_index = String.to_integer(option_index)
    
    menu = Enum.at(socket.assigns.ussd_menus, menu_index)
    option = Enum.at(menu.options || [], option_index)
    
    form_data = %{
      "text" => option.text || "",
      "action" => option.action || "none",
      "target_menu_id" => option.target_menu_id || "",
      "form_name" => option.form_name || "",
      "option_id" => option.option_id,
      "menu_index" => menu_index,
      "option_index" => option_index
    }
    
    socket = 
      socket
      |> assign(:editing_ussd_option, {menu_index, option_index, option})
      |> assign(:ussd_option_form, to_form(form_data, as: :ussd_option))
    
    {:noreply, socket}
  end

  def handle_event("delete_ussd_option", %{"menu-index" => menu_index, "option-index" => option_index}, socket) do
    menu_index = String.to_integer(menu_index)
    option_index = String.to_integer(option_index)
    
    menu = Enum.at(socket.assigns.ussd_menus, menu_index)
    option = Enum.at(menu.options || [], option_index)
    
    case UssdService.delete_option(option.option_id) do
      {:ok, _message} ->
        socket = 
          socket
          |> load_ussd_data()
          |> generate_ussd_code()
          |> put_flash(:info, "Option deleted successfully")
        
        {:noreply, socket}
        
      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to delete option: #{message}")}
    end
  end

  def handle_event("generate_ussd_code", _params, socket) do
    socket = generate_ussd_code(socket)
    {:noreply, socket}
  end

  # USSD Simulator Events
  def handle_event("simulate_ussd_option", %{"option" => option_index}, socket) do
    option_index = String.to_integer(option_index)
    current_menu = socket.assigns.current_ussd_menu
    
    if current_menu && length(current_menu.options || []) > option_index do
      option = Enum.at(current_menu.options, option_index)
      
      case option.action do
        "submenu" ->
          # Navigate to the specified target menu
          if option.target_menu_id && option.target_menu_id != "" do
            target_menu = Enum.find(socket.assigns.ussd_menus, &(&1.menu_id == option.target_menu_id))
            
            if target_menu do
              socket = 
                socket
                |> assign(:current_ussd_menu, target_menu)
                |> assign(:ussd_menu_stack, [current_menu | socket.assigns.ussd_menu_stack])
              
              {:noreply, socket}
            else
              {:noreply, socket}
            end
          else
            # Fallback: navigate to next menu if no target specified
            next_menu = Enum.at(socket.assigns.ussd_menus, 1) || Enum.at(socket.assigns.ussd_menus, 0)
            
            if next_menu do
              socket = 
                socket
                |> assign(:current_ussd_menu, next_menu)
                |> assign(:ussd_menu_stack, [current_menu | socket.assigns.ussd_menu_stack])
              
              {:noreply, socket}
            else
              {:noreply, socket}
            end
          end
        
        "end" ->
          # End the USSD session
          socket = 
            socket
            |> assign(:current_ussd_menu, nil)
            |> assign(:ussd_menu_stack, [])
          
          {:noreply, socket}
        
        "function" ->
          # Execute a function (placeholder for now)
          {:noreply, put_flash(socket, :info, "Function '#{option.text}' executed")}
        
        "form" ->
          # Start form data collection
          if option.form_name && option.form_name != "" do
            case UssdService.create_form_session(option.option_id, option.form_name) do
              {:ok, session_data} ->
                case UssdService.get_next_form_field(session_data) do
                  {:ok, field, _has_more} ->
                    socket = 
                      socket
                      |> assign(:form_session, session_data)
                      |> assign(:current_form_field, field)
                      |> assign(:form_input_mode, true)
                      |> assign(:current_letter_input, "")
                      |> assign(:letter_input_mode, false)
                      |> assign(:last_key_press, nil)
                      |> assign(:key_press_count, 0)
                      |> put_flash(:info, "Starting form: #{option.form_name}")
                    
                    {:noreply, socket}
                  
                  {:completed, _data} ->
                    {:noreply, put_flash(socket, :info, "Form is empty")}
                  
                  {:error, reason} ->
                    {:noreply, put_flash(socket, :error, "Form error: #{reason}")}
                end
              
              {:error, reason} ->
                {:noreply, put_flash(socket, :error, "Failed to start form: #{reason}")}
            end
          else
            {:noreply, put_flash(socket, :error, "No form specified for this option")}
          end
        
        _ ->
          # Default behavior for "none" or unspecified actions
          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  def handle_event("ussd_go_back", _params, socket) do
    stack = socket.assigns.ussd_menu_stack
    
    if length(stack) > 0 do
      [previous_menu | remaining_stack] = stack
      
      socket = 
        socket
        |> assign(:current_ussd_menu, previous_menu)
        |> assign(:ussd_menu_stack, remaining_stack)
      
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  def handle_event("ussd_cancel", _params, socket) do
    socket = 
      socket
      |> assign(:current_ussd_menu, List.first(socket.assigns.ussd_menus))
      |> assign(:ussd_menu_stack, [])
    
    {:noreply, socket}
  end

  def handle_event("reset_ussd_simulation", _params, socket) do
    socket = 
      socket
      |> assign(:current_ussd_menu, List.first(socket.assigns.ussd_menus))
      |> assign(:ussd_menu_stack, [])
    
    {:noreply, socket}
  end

  def handle_event("test_ussd_flow", _params, socket) do
    # Start simulation with first menu
    socket = 
      socket
      |> assign(:current_ussd_menu, List.first(socket.assigns.ussd_menus))
      |> assign(:ussd_menu_stack, [])
    
    {:noreply, socket}
  end

  def handle_event("ussd_key_press", %{"key" => key}, socket) do
    if socket.assigns.form_input_mode do
      handle_form_key_press(key, socket)
    else
      # Handle keypad input for regular USSD navigation
      case key do
        "0" -> handle_event("ussd_go_back", %{}, socket)
        "#" -> handle_event("ussd_cancel", %{}, socket)
        num when num in ["1", "2", "3", "4", "5", "6", "7", "8", "9"] ->
          option_index = String.to_integer(num) - 1
          handle_event("simulate_ussd_option", %{"option" => Integer.to_string(option_index)}, socket)
        _ -> {:noreply, socket}
      end
    end
  end

  defp handle_form_key_press("#", socket) do
    # Go back to previous field in form
    if socket.assigns.form_session do
      case UssdService.go_back_form_field(socket.assigns.form_session) do
        {:ok, updated_session} ->
          # Get current field directly instead of next field
          current_field = Enum.at(updated_session.field_definitions, updated_session.current_field_index)
          
          if current_field do
            socket = 
              socket
              |> assign(:form_session, updated_session)
              |> assign(:current_form_field, current_field)
              |> assign(:current_letter_input, "")
              |> assign(:last_key_press, nil)
              |> assign(:key_press_count, 0)
              |> put_flash(:info, "Going back to previous field...")
            
            {:noreply, socket}
          else
            {:noreply, put_flash(socket, :error, "Cannot navigate back further")}
          end
        
        {:error, reason} ->
          {:noreply, put_flash(socket, :error, reason)}
      end
    else
      {:noreply, socket}
    end
  end

  defp handle_form_key_press("*", socket) do
    # Toggle letter input mode
    socket = assign(socket, :letter_input_mode, !socket.assigns.letter_input_mode)
    {:noreply, socket}
  end

  defp handle_form_key_press("0", socket) do
    if socket.assigns.letter_input_mode do
      handle_letter_input("0", socket)
    else
      # Add "0" to current input
      updated_input = socket.assigns.current_letter_input <> "0"
      socket = assign(socket, :current_letter_input, updated_input)
      {:noreply, socket}
    end
  end

  defp handle_form_key_press(key, socket) when key in ["2", "3", "4", "5", "6", "7", "8", "9"] do
    if socket.assigns.letter_input_mode do
      handle_letter_input(key, socket)
    else
      # Add number to current input
      updated_input = socket.assigns.current_letter_input <> key
      socket = assign(socket, :current_letter_input, updated_input)
      {:noreply, socket}
    end
  end

  defp handle_form_key_press("1", socket) do
    # "1" doesn't have letters, just add to input
    updated_input = socket.assigns.current_letter_input <> "1"
    socket = assign(socket, :current_letter_input, updated_input)
    {:noreply, socket}
  end

  defp handle_form_key_press(_, socket) do
    {:noreply, socket}
  end

  # Letter mapping for multi-tap input
  defp get_letters_for_key(key) do
    case key do
      "2" -> ["a", "b", "c"]
      "3" -> ["d", "e", "f"]
      "4" -> ["g", "h", "i"]
      "5" -> ["j", "k", "l"]
      "6" -> ["m", "n", "o"]
      "7" -> ["p", "q", "r", "s"]
      "8" -> ["t", "u", "v"]
      "9" -> ["w", "x", "y", "z"]
      "0" -> [" "]
      _ -> []
    end
  end

  defp handle_letter_input(key, socket) do
    current_time = System.monotonic_time(:millisecond)
    letters = get_letters_for_key(key)
    
    if length(letters) > 0 do
      if socket.assigns.last_key_press == key do
        # Same key pressed again - cycle through letters
        count = rem(socket.assigns.key_press_count, length(letters))
        letter = Enum.at(letters, count)
        
        # Replace the last character if it was from the same key
        current_input = socket.assigns.current_letter_input
        updated_input = 
          if String.length(current_input) > 0 && socket.assigns.key_press_count > 0 do
            String.slice(current_input, 0..-2) <> letter
          else
            current_input <> letter
          end
        
        socket = 
          socket
          |> assign(:current_letter_input, updated_input)
          |> assign(:key_press_count, socket.assigns.key_press_count + 1)
          |> assign(:last_key_press, key)
        
        {:noreply, socket}
      else
        # New key pressed - add first letter
        letter = Enum.at(letters, 0)
        updated_input = socket.assigns.current_letter_input <> letter
        
        socket = 
          socket
          |> assign(:current_letter_input, updated_input)
          |> assign(:key_press_count, 1)
          |> assign(:last_key_press, key)
        
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  def handle_event("ussd_input", _params, socket) do
    # Handle text input - placeholder for now
    {:noreply, socket}
  end

  # Form Input Event Handlers
  def handle_event("form_input_submit", params, socket) do
    # Handle both "input" and "value" parameters for different form submission methods
    input = params["input"] || params["value"] || ""
    
    # Use keypad input if available, otherwise use form input
    actual_input = if String.length(socket.assigns.current_letter_input) > 0 do
      socket.assigns.current_letter_input
    else
      input
    end

    if socket.assigns.form_input_mode && socket.assigns.form_session do
      case UssdService.process_form_input(socket.assigns.form_session, actual_input) do
        {:ok, updated_session} ->
          case UssdService.get_next_form_field(updated_session) do
            {:ok, next_field, _has_more} ->
              # Move to next field
              socket = 
                socket
                |> assign(:form_session, updated_session)
                |> assign(:current_form_field, next_field)
                |> assign(:current_letter_input, "")
                |> assign(:last_key_press, nil)
                |> assign(:key_press_count, 0)
                |> put_flash(:info, "Input saved. Next field...")
              
              {:noreply, socket}
            
            {:completed, collected_data} ->
              # Form completed
              socket = 
                socket
                |> assign(:form_session, nil)
                |> assign(:current_form_field, nil)
                |> assign(:form_input_mode, false)
                |> assign(:current_letter_input, "")
                |> assign(:last_key_press, nil)
                |> assign(:key_press_count, 0)
                |> put_flash(:success, "Form completed! Data collected: #{inspect(collected_data)}")
              
              {:noreply, socket}
            
            {:error, reason} ->
              {:noreply, put_flash(socket, :error, "Form error: #{reason}")}
          end
        
        {:error, reason} ->
          {:noreply, put_flash(socket, :error, "Invalid input: #{reason}")}
      end
    else
      {:noreply, socket}
    end
  end

  # Fallback for form_input_submit with no parameters or different structure
  def handle_event("form_input_submit", params, socket) when not socket.assigns.form_input_mode do
    {:noreply, socket}
  end

  def handle_event("cancel_form_input", _params, socket) do
    socket = 
      socket
      |> assign(:form_session, nil)
      |> assign(:current_form_field, nil)
      |> assign(:form_input_mode, false)
      |> assign(:current_letter_input, "")
      |> assign(:letter_input_mode, false)
      |> assign(:last_key_press, nil)
      |> assign(:key_press_count, 0)
      |> put_flash(:info, "Form input cancelled")
    
    {:noreply, socket}
  end

  def handle_event("clear_input", _params, socket) do
    socket = 
      socket
      |> assign(:current_letter_input, "")
      |> assign(:last_key_press, nil)
      |> assign(:key_press_count, 0)
    
    {:noreply, socket}
  end

  # USSD Edit Form Handlers
  def handle_event("save_ussd_menu", %{"ussd_menu" => menu_params}, socket) do
    menu_id = menu_params["menu_id"]
    title = menu_params["title"]
    
    case UssdService.update_menu(menu_id, %{title: title}) do
      {:ok, _menu} ->
        socket = 
          socket
          |> load_ussd_data()
          |> assign(:editing_ussd_menu, nil)
          |> assign(:ussd_menu_form, nil)
          |> generate_ussd_code()
          |> put_flash(:info, "Menu updated successfully")
        
        {:noreply, socket}
        
      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to update menu: #{message}")}
    end
  end

  def handle_event("save_ussd_option", %{"ussd_option" => option_params}, socket) do
    option_id = option_params["option_id"]
    text = option_params["text"]
    action = option_params["action"]
    target_menu_id = if option_params["target_menu_id"] != "", do: option_params["target_menu_id"], else: nil
    form_name = if option_params["form_name"] != "", do: option_params["form_name"], else: nil
    
    update_attrs = %{
      text: text,
      action: action,
      target_menu_id: target_menu_id,
      form_name: form_name
    }
    
    case UssdService.update_option(option_id, update_attrs) do
      {:ok, _option} ->
        socket = 
          socket
          |> load_ussd_data()
          |> assign(:editing_ussd_option, nil)
          |> assign(:ussd_option_form, nil)
          |> generate_ussd_code()
          |> put_flash(:info, "Option updated successfully")
        
        {:noreply, socket}
        
      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to update option: #{message}")}
    end
  end

  def handle_event("cancel_ussd_edit", _params, socket) do
    socket = 
      socket
      |> assign(:editing_ussd_menu, nil)
      |> assign(:editing_ussd_option, nil)
      |> assign(:ussd_menu_form, nil)
      |> assign(:ussd_option_form, nil)
    
    {:noreply, socket}
  end

  defp load_data(socket) do
    {:ok, fields} = MobileAppFormsService.list_fields()
    
    # Group fields by form name with statistics
    grouped_forms = fields
    |> Enum.group_by(& &1.form)
    |> Enum.map(fn {form_name, form_fields} ->
      field_types = form_fields |> Enum.map(& &1.field_type) |> Enum.uniq()
      has_buttons = "button" in field_types
      
      %{
        name: form_name,
        fields: form_fields,
        field_count: length(form_fields),
        screen_count: form_fields |> Enum.map(& &1.screen) |> Enum.reject(&is_nil/1) |> Enum.uniq() |> length(),
        page_count: form_fields |> Enum.map(& &1.page) |> Enum.reject(&is_nil/1) |> Enum.uniq() |> length(),
        field_types: field_types,
        has_buttons: has_buttons,
        version: form_fields |> List.first() |> Map.get(:version, "1.0"),
        updated_at: form_fields |> Enum.map(& &1.updated_at) |> Enum.max()
      }
    end)
    |> filter_forms(socket.assigns.search_query, socket.assigns.selected_form_filter, socket.assigns.selected_type)
    |> sort_forms(socket.assigns.sort_by)
    
    # Also keep individual fields for detail view
    socket
    |> assign(:fields, fields)
    |> assign(:grouped_forms, grouped_forms)
    |> assign(:unique_forms, get_unique_forms(fields))
    |> assign(:unique_screens, get_unique_screens(fields, socket.assigns[:selected_form]))
    |> assign(:unique_pages, get_unique_pages(fields, socket.assigns[:selected_form], socket.assigns[:selected_screen]))
    |> assign(:field_types, get_unique_field_types(fields))
  end

  defp load_ussd_data(socket) do
    case UssdService.list_menus() do
      {:ok, menus} ->
        # Convert to map format for compatibility with existing UI code
        ussd_menus = Enum.map(menus, &convert_menu_to_legacy_format/1)
        
        socket
        |> assign(:ussd_menus, ussd_menus)
        |> assign(:current_ussd_menu, List.first(ussd_menus))
        
      {:error, _reason} ->
        socket
        |> assign(:ussd_menus, [])
        |> assign(:current_ussd_menu, nil)
    end
  end

  defp convert_menu_to_legacy_format(menu) do
    options = Enum.map(menu.options || [], &convert_option_to_legacy_format/1)
    
    %{
      menu_id: menu.menu_id,
      title: menu.title,
      options: options,
      menu_order: menu.menu_order,
      version: menu.version,
      active: menu.active
    }
  end

  defp convert_option_to_legacy_format(option) do
    %{
      option_id: option.option_id,
      text: option.text,
      action: option.action,
      target_menu_id: option.target_menu_id,
      form_id: option.form_id,
      form_name: option.form_name,
      option_order: option.option_order,
      active: option.active
    }
  end

  defp load_filtered_data(socket) do
    filters = %{}
    filters = if socket.assigns.selected_form, do: Map.put(filters, "form", socket.assigns.selected_form), else: filters
    filters = if socket.assigns.selected_screen, do: Map.put(filters, "screen", socket.assigns.selected_screen), else: filters
    filters = if socket.assigns.selected_page, do: Map.put(filters, "page", socket.assigns.selected_page), else: filters

    {:ok, fields} = MobileAppFormsService.list_fields(filters)
    
    socket
    |> assign(:fields, fields)
    |> assign(:unique_screens, get_unique_screens(socket.assigns.fields, socket.assigns.selected_form))
    |> assign(:unique_pages, get_unique_pages(socket.assigns.fields, socket.assigns.selected_form, socket.assigns.selected_screen))
  end

  defp get_unique_forms(fields) do
    fields
    |> Enum.map(& &1.form)
    |> Enum.uniq()
    |> Enum.sort()
  end

  defp get_unique_screens(fields, selected_form) do
    fields
    |> Enum.filter(fn field -> 
      (is_nil(selected_form) or field.form == selected_form) and not is_nil(field.screen)
    end)
    |> Enum.map(& &1.screen)
    |> Enum.uniq()
    |> Enum.sort()
  end

  defp get_unique_pages(fields, selected_form, selected_screen) do
    fields
    |> Enum.filter(fn field -> 
      (is_nil(selected_form) or field.form == selected_form) and
      (is_nil(selected_screen) or field.screen == selected_screen) and
      not is_nil(field.page)
    end)
    |> Enum.map(& &1.page)
    |> Enum.uniq()
    |> Enum.sort()
  end

  defp get_unique_field_types(fields) do
    fields
    |> Enum.map(& &1.field_type)
    |> Enum.uniq()
    |> Enum.sort()
  end

  defp filter_forms(forms, search_query, form_filter, type_filter) do
    forms
    |> filter_by_search(search_query)
    |> filter_by_form_name(form_filter)
    |> filter_by_field_type(type_filter)
  end

  defp filter_by_search(forms, ""), do: forms
  defp filter_by_search(forms, query) do
    query = String.downcase(query)
    Enum.filter(forms, fn form ->
      String.contains?(String.downcase(form.name), query) ||
      Enum.any?(form.field_types, &String.contains?(String.downcase(&1), query))
    end)
  end

  defp filter_by_form_name(forms, "all"), do: forms
  defp filter_by_form_name(forms, form_name) do
    Enum.filter(forms, &(&1.name == form_name))
  end

  defp filter_by_field_type(forms, "all"), do: forms
  defp filter_by_field_type(forms, field_type) do
    Enum.filter(forms, fn form ->
      field_type in form.field_types
    end)
  end

  defp sort_forms(forms, "name") do
    Enum.sort_by(forms, & &1.name)
  end
  defp sort_forms(forms, "fields") do
    Enum.sort_by(forms, & &1.field_count, :desc)
  end
  defp sort_forms(forms, "updated") do
    Enum.sort_by(forms, & &1.updated_at, {:desc, DateTime})
  end
  defp sort_forms(forms, _), do: forms

  defp get_pages_for_screen(fields, form, screen) do
    pages = fields
    |> Enum.filter(fn field -> 
      field.form == form and field.screen == screen and not is_nil(field.page)
    end)
    |> Enum.map(& &1.page)
    |> Enum.uniq()
    |> Enum.sort()
    
    # If there are no pages, return a single "Main" page for display
    if length(pages) == 0, do: [nil], else: pages
  end

  defp mobile_input_type(field_type) do
    case field_type do
      "email" -> "email"
      "password" -> "password"
      "phone" -> "tel"
      "number" -> "number"
      "integer" -> "number"
      "date" -> "date"
      "datetime" -> "datetime-local"
      _ -> "text"
    end
  end

  defp generate_ussd_code(socket) do
    menus = socket.assigns.ussd_menus
    
    if length(menus) > 0 do
      code = generate_menu_code(menus, 0, "")
      assign(socket, :ussd_code, code)
    else
      assign(socket, :ussd_code, nil)
    end
  end

  defp generate_menu_code(menus, level, parent_code) do
    case Enum.at(menus, level) do
      nil -> 
        ""
      menu ->
        code = """
        #{if level == 0, do: "// Main USSD Menu", else: "// Menu Level #{level + 1}"}
        #{parent_code}
        CON #{menu.title || "Menu Level #{level + 1}"}
        #{generate_options_code(menu.options || [])}
        """
        
        # Generate code for sub-menus if they exist
        if level + 1 < length(menus) do
          code <> "\n" <> generate_menu_code(menus, level + 1, "#{parent_code}*1")
        else
          code
        end
    end
  end

  defp generate_options_code(options) do
    options
    |> Enum.with_index()
    |> Enum.map(fn {option, index} ->
      "#{index + 1}. #{option.text}"
    end)
    |> Enum.join("\n")
    |> case do
      "" -> ""
      opts -> opts <> "\n0. Back\n#. Cancel"
    end
  end
end