defmodule ServiceManagerWeb.Backend.UploadProfilePictureComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Schemas.AdminUsers
  alias ServiceManager.Repo

  require Logger

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 transition-opacity">
        <div class="fixed inset-0 z-50 overflow-y-auto">
          <div class="flex min-h-full items-end justify-center p-4 sm:items-center sm:p-0">
            <div class="relative transform overflow-hidden rounded-lg bg-white shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
              <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t">
                <h3 class="text-xl font-semibold text-gray-900">Update Profile Picture</h3>
                <button
                  type="button"
                  phx-click="hide_modal"
                  phx-target={@myself}
                  class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center"
                >
                  <i class="fas fa-times"></i>
                  <span class="sr-only">Close modal</span>
                </button>
              </div>

              <div class="p-4 md:p-5">
                <form
                  id="upload-form"
                  phx-submit="save_profile"
                  phx-change="validate"
                  phx-target={@myself}
                >
                  <div class="space-y-4">
                    <div class="flex flex-col items-center">
                      <div class="w-48 h-48 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                        <%= if @preview_image do %>
                          <img
                            src={@preview_image}
                            alt="Preview"
                            class="w-full h-full rounded-full object-cover"
                          />
                        <% else %>
                          <i class="fas fa-user text-6xl text-gray-400"></i>
                        <% end %>
                      </div>

                      <div class="flex flex-col items-center space-y-4">
                        <div class="flex items-center justify-center w-full">
                          <.live_file_input upload={@uploads.profile_picture} class="hidden" />
                          <label
                            for={@uploads.profile_picture.ref}
                            class="cursor-pointer py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200"
                          >
                            Choose Image
                          </label>
                        </div>

                        <%= for entry <- @uploads.profile_picture.entries do %>
                          <div class="w-full">
                            <div class="flex flex-col space-y-2">
                              <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500"><%= entry.client_name %></span>
                              </div>

                              <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div
                                  class="bg-blue-600 h-2.5 rounded-full"
                                  style={"width: #{entry.progress}%"}
                                >
                                </div>
                              </div>

                              <%= for err <- upload_errors(@uploads.profile_picture, entry) do %>
                                <p class="text-sm text-red-500"><%= error_to_string(err) %></p>
                              <% end %>
                            </div>
                          </div>
                        <% end %>
                      </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                      <button
                        type="button"
                        phx-click="hide_modal"
                        phx-target={@myself}
                        class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
                        disabled={!@preview_image}
                      >
                        Save Changes
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def update(%{current_user: current_user} = assigns, socket) do
    Logger.info("UploadProfilePictureComponent update/2 called")

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:preview_image, nil)
     |> allow_upload(:profile_picture,
       accept: ~w(.jpg .jpeg .png),
       max_entries: 1,
       max_file_size: 5_000_000,
       auto_upload: true
     )}
  end

  @impl true
  def handle_event("validate", _params, socket) do
    Logger.info("Validating file...")
    {:noreply, socket}
  end

  @impl true
  def handle_progress(:profile_picture, entry, socket) do
    Logger.info("Upload progress: #{entry.progress}%")

    if entry.done? do
      Logger.info("File upload complete, processing...")

      {:ok, binary} =
        consume_uploaded_entries(socket, :profile_picture, fn %{path: path}, _entry ->
          Logger.info("Reading file from path: #{path}")
          {:ok, File.read!(path)}
        end)
        |> List.first()

      Logger.info("Converting to base64")
      base64_image = Base.encode64(binary)
      image_data = "data:image/png;base64," <> base64_image

      Logger.info("Setting preview image")
      {:noreply, assign(socket, :preview_image, image_data)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("save_profile", params, socket) do
    Logger.info(
      "UploadProfilePictureComponent save_profile called with params: #{inspect(params)}"
    )

    Logger.info("Current assigns: #{inspect(socket.assigns)}")

    if socket.assigns.preview_image do
      Logger.info("Preview image found, updating user")

      case Repo.update(
             AdminUsers.update_changeset(socket.assigns.current_user, %{
               profile_picture: socket.assigns.preview_image
             })
           ) do
        {:ok, updated_user} ->
          Logger.info("User updated successfully")
          send(self(), {:user_updated, updated_user})

          {:noreply,
           socket
           |> put_flash(:info, "Profile picture updated successfully")}

        {:error, changeset} ->
          Logger.error("Error updating user: #{inspect(changeset)}")

          {:noreply,
           socket
           |> put_flash(:error, "Error updating profile picture")}
      end
    else
      {:noreply,
       socket
       |> put_flash(:error, "Please select an image first")}
    end
  end

  @impl true
  def handle_event("hide_modal", _, socket) do
    send(self(), :close_upload_modal)
    {:noreply, socket}
  end

  def error_to_string(:too_large), do: "File is too large"
  def error_to_string(:too_many_files), do: "Too many files"
  def error_to_string(:not_accepted), do: "Unacceptable file type"
end
