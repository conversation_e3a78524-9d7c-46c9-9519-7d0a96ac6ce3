defmodule ServiceManagerWeb.Backend.ChangePasswordLive do
  use ServiceManagerWeb, :live_view

  alias ServiceManagerWeb.Live.Admin.ChangePasswordComponent

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <.live_component
      module={ChangePasswordComponent}
      id="change-password"
      current_user={@current_user}
    />
    """
  end
end
