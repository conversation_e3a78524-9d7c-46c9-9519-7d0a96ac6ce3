defmodule ServiceManagerWeb.Backend.EditProfileComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Schemas.AdminUsers
  alias ServiceManager.Repo

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.flash_group flash={@flash} />

      <div class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 transition-opacity">
        <div class="fixed inset-0 z-50 overflow-y-auto">
          <div class="flex min-h-full items-end justify-center p-4 sm:items-center sm:p-0">
            <div class="relative transform overflow-hidden rounded-lg bg-white shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl">
              <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t">
                <h3 class="text-xl font-semibold text-gray-900">Edit Profile</h3>
                <button
                  type="button"
                  phx-click="hide_modal"
                  phx-target={@myself}
                  class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center"
                >
                  <i class="fas fa-times"></i>
                  <span class="sr-only">Close modal</span>
                </button>
              </div>

              <.simple_form for={@form} id="profile-form" phx-target={@myself} phx-submit="save">
                <div class="p-4 md:p-5 space-y-4">
                  <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                    <.input field={@form[:first_name]} type="text" label="First Name" />
                    <.input field={@form[:last_name]} type="text" label="Last Name" />
                    <.input field={@form[:email]} type="email" label="Email" />
                    <.input field={@form[:phone_number]} type="text" label="Phone Number" />
                    <.input field={@form[:date_of_birth]} type="date" label="Date of Birth" />
                    <.input field={@form[:address]} type="text" label="Address" />
                    <.input field={@form[:city]} type="text" label="City" />
                    <.input field={@form[:state]} type="text" label="State/Province" />
                    <.input field={@form[:zip]} type="text" label="ZIP/Postal Code" />
                    <.input field={@form[:country]} type="text" label="Country" />
                  </div>
                </div>

                <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b">
                  <.button phx-disable-with="Saving...">Save Changes</.button>
                  <button
                    type="button"
                    phx-click="hide_modal"
                    phx-target={@myself}
                    class="ms-3 text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10"
                  >
                    Cancel
                  </button>
                </div>
              </.simple_form>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def update(%{current_user: current_user} = assigns, socket) do
    changeset = AdminUsers.update_changeset(current_user, %{})

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:form, to_form(changeset))}
  end

  @impl true
  def handle_event("save", %{"admin_users" => params}, socket) do
    case Repo.update(AdminUsers.update_changeset(socket.assigns.current_user, params)) do
      {:ok, updated_user} ->
        send(self(), {:user_updated, updated_user})

        {:noreply,
         socket
         |> put_flash(:info, "Profile updated successfully")}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply,
         socket
         |> assign(:form, to_form(changeset))
         |> put_flash(:error, "Error updating profile")}
    end
  end

  @impl true
  def handle_event("hide_modal", _, socket) do
    send(self(), :close_modal)
    {:noreply, socket}
  end
end
