defmodule ServiceManagerWeb.Api.MobileAppFormsJSON do
  @moduledoc """
  JSON responses for Mobile App Forms API
  """

  @doc """
  Mobile app response format - matches the screenshot specification
  """
  def mobile_response(%{fields: fields}) do
    # Separate buttons from other fields
    {buttons, regular_fields} = Enum.split_with(fields, &(&1.field_type == "button"))
    
    # Group buttons by form name
    grouped_buttons = buttons
    |> Enum.group_by(& &1.form)
    |> Enum.map(fn {form_name, buttons} ->
      %{
        form_name: form_name,
        buttons: Enum.map(buttons, &format_mobile_button/1)
      }
    end)
    
    response = %{
      success: true,
      form_fields: Enum.map(regular_fields, &format_mobile_field/1)
    }
    
    # Add buttons section if there are any buttons
    if length(grouped_buttons) > 0 do
      Map.put(response, :action_buttons, grouped_buttons)
    else
      response
    end
  end

  @doc """
  Admin list response for field management
  """
  def admin_list_response(%{fields: fields}) do
    %{
      success: true,
      data: %{
        fields: Enum.map(fields, &format_admin_field/1),
        count: length(fields)
      }
    }
  end

  @doc """
  Admin show response for single field
  """
  def admin_show_response(%{field: field}) do
    %{
      success: true,
      data: %{
        field: format_admin_field(field)
      }
    }
  end

  @doc """
  Hierarchy operation response
  """
  def hierarchy_response(result) do
    %{
      success: true,
      data: result
    }
  end

  @doc """
  Forms list response
  """
  def forms_list_response(%{forms: forms}) do
    %{
      success: true,
      data: %{
        forms: forms,
        count: length(forms)
      }
    }
  end

  @doc """
  Screens list response
  """
  def screens_list_response(%{screens: screens}) do
    %{
      success: true,
      data: %{
        screens: screens,
        count: length(screens)
      }
    }
  end

  @doc """
  Pages list response
  """
  def pages_list_response(%{pages: pages}) do
    %{
      success: true,
      data: %{
        pages: pages,
        count: length(pages)
      }
    }
  end

  @doc """
  Success response
  """
  def success_response(message) do
    %{
      success: true,
      message: message
    }
  end

  @doc """
  Error response
  """
  def error_response(message) do
    %{
      success: false,
      error: message
    }
  end

  # Private helper functions

  # Format field for mobile app consumption (matches screenshot format)
  defp format_mobile_field(field) do
    base_field = %{
      isRequired: field.is_required,
      type: field.field_type,
      label: field.label,
      field: field.field_id,
      field_name: field.field_name,
      order: field.field_order
    }
    
    # Only include submit_to for button fields
    if field.field_type == "button" do
      Map.put(base_field, :submit_to, field.submit_to)
    else
      base_field
    end
  end

  # Format button for mobile app consumption
  defp format_mobile_button(button) do
    %{
      id: button.field_id,
      label: button.label,
      action: button.submit_to,
      field_name: button.field_name,
      order: button.field_order
    }
  end

  # Format field for admin management with full details
  defp format_admin_field(field) do
    %{
      field_id: field.field_id,
      form: field.form,
      screen: field.screen,
      page: field.page,
      version: field.version,
      field_name: field.field_name,
      field_type: field.field_type,
      label: field.label,
      is_required: field.is_required,
      field_order: field.field_order,
      active: field.active,
      submit_to: field.submit_to,
      inserted_at: field.inserted_at,
      updated_at: field.updated_at
    }
  end
end