defmodule ServiceManagerWeb.Api.Services.AuthenticationService do
  require Logger

  import ServiceManager.Logging.FunctionTracker

  # Alias the Accounts, UserAuth, Token and Repo modules for easier access
  alias ServiceManager.Accounts
  alias ServiceManagerWeb.UserAuth
  alias ServiceManager.Accounts.Token
  alias ServiceManager.Repo
  alias ServiceManager.Accounts.User
  alias ServiceManager.Schemas.ActiveDevice, as: Device
  alias ServiceManager.Schemas.Session.DeviceSession

  # track do
  # Function to authenticate a user
  def authenticate(user_params) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Starting authentication process"
    )

    # Extract user id, password and auth type from the user parameters
    %{"user-id" => userid, "passphrase" => password, "auth-type" => auth_type} = user_params
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Extracted user credentials")

    # Check if the user exists in the database with the provided email and password
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Attempting user verification")

    if user = Accounts.get_user_by_email_and_password(userid, password) do
      Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * User verified successfully")

      if user.disabled do
        Logger.info(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * Authentication failed: Account is disabled"
        )

        {:error, "Your account has been disabled. Please contact support for assistance."}
        |> respond()
      else
        # TODO: Start API call here

        # Pull bank account details
        Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Pulling bank details")

        case get_user_bank_info(user.account_number) do
          {:error, error_message} ->
            # In case of failure, respond with an error message
            Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * #{error_message}")

            {:error, error_message}
            |> respond()

          {:ok, bank_details} ->
            # Generate extra claims for the user
            Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Generating claims")
            claims = extra_claims(user)

            # Generate a session_id and update the user
            Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Generating session_id")

            {:ok, updated_user} =
              Repo.update(
                ServiceManager.Accounts.User.update_changeset(
                  user,
                  %{
                    session_id: Ecto.UUID.generate()
                  }
                  |> Map.merge(bank_details)
                )
              )

            _bind_response =
              case bind_device(updated_user, user_params) do
                {:error, message} ->
                  {:error, message} |> respond()

                {:ok, _message} ->
                  # Generate a token for the user
                  Logger.info(
                    "[#{DateTime.utc_now() |> DateTime.to_string()}] * Generating token"
                  )

                  token = generate_token(claims)

                  # Save the token to the database
                  Logger.info(
                    "[#{DateTime.utc_now() |> DateTime.to_string()}] * Saving token to database"
                  )

                  {:ok, db_token} = create_token(token, updated_user.id)

                  # Respond with the token
                  Logger.info(
                    "[#{DateTime.utc_now() |> DateTime.to_string()}] * Authentication successful"
                  )

                  spawn(fn ->
                    Device.find_by([user_id: updated_user.id, device_id: user_params["device_id"]])
                    |> Device.update([on_hold: false, device_screen_message: "Device Active"])
                  end)

                  # Check multi-session setting
                  session_result = if updated_user.allow_multi_session do
                    # Multi-session enabled - create or update device session
                    case DeviceSession.find_by(
                      user_id: updated_user.id |> to_string()
                    ) |> IO.inspect(label: "DEVICE-SESSION-FIND-BY") do
                      nil ->
                        # Create new device session
                        DeviceSession.create(
                          user_id: updated_user.id |> to_string(),
                          device_id: user_params["device_id"],
                          access_token: token,
                          session_token: updated_user.session_id
                        ) |> IO.inspect(label: "DEVICE-SESSION-CREATED")
                        :ok

                      existing_session ->
                        # Update existing device session
                        DeviceSession.update(existing_session, %{
                          device_id: user_params["device_id"],
                          access_token: token,
                          session_token: updated_user.session_id
                        }) |> IO.inspect(label: "DEVICE-SESSION-UPDATED")
                        :ok
                    end
                  else
                    # Multi-session disabled - check for existing sessions
                    case DeviceSession.find_by(user_id: updated_user.id |> to_string()) do
                      nil ->
                        # No existing session - create new one
                        DeviceSession.create(
                          user_id: updated_user.id |> to_string(),
                          device_id: user_params["device_id"],
                          access_token: token,
                          session_token: updated_user.session_id
                        ) |> IO.inspect(label: "DEVICE-SESSION-CREATED")
                        :ok

                      existing_session ->
                        # Check if same device
                        if existing_session.device_id == user_params["device_id"] do
                          # Same device - update session
                          DeviceSession.update(existing_session, %{
                            device_id: user_params["device_id"],
                            access_token: token,
                            session_token: updated_user.session_id
                          }) |> IO.inspect(label: "DEVICE-SESSION-UPDATED")
                          :ok
                        else
                          # Different device - return error
                          :error_multi_session
                        end
                    end
                  end

                  case session_result do
                    :error_multi_session ->
                      {:error, "You are already logged in on a different device. Please log out from the other device first or enable multi-session in your profile settings."}
                      |> respond()

                    :ok ->
                      {:ok, db_token.token}
                      |> respond(updated_user)
                  end
              end
        end
      end
    else
      # In case of failure, respond with an error message
      Logger.info(
        "[#{DateTime.utc_now() |> DateTime.to_string()}] * Authentication failed: Invalid credentials"
      )

      {:error, "Login failed. Invalid username or password, please try again."}
      |> respond()
    end
  end

  # end

  defp bind_device(user, params) do
    if params["device_id"] == nil do
      {:error, "An error occurred, device validation failed. could not identify device ..."}
    else
      device = Device.find_by(device_id: params["device_id"], user_id: user.id)
      handle_device_binding(user, params, device)
    end
  end

  defp handle_device_binding(user, params, device) do
    user_primary_device = user.primary_device_id || ""

    cond do
      user_primary_device |> String.length() <= 0 and device == nil ->
        case Device.create(
               device_id: params["device_id"],
               user_id: user.id,
               last_seen_at: NaiveDateTime.utc_now(),
               enabled: true,
               blocked: false
             ) do
          {:error, _error} ->
            {:error, "An error occurred when registering your device ..."}

          {:ok, device} ->
            updated_user =
              Repo.update(
                User.device_binding_changeset(user, %{
                  primary_device_id: device.device_id
                })
              )

            case updated_user do
              {:ok, user} ->
                {:ok, "Device linked to user account #{user.username} ..."}

              {:error, _error} ->
                {:error,
                 "An error occurred trying to link your device, please contact support ..."}
            end
        end

      user_primary_device |> String.length() <= 0 and device != nil ->
        updated_user =
          Repo.update(
            User.device_binding_changeset(user, %{
              primary_device_id: params["device_id"]
            })
          )

        case updated_user do
          {:ok, user} -> {:ok, "Device linked to user account #{user.username} ..."}
          {:error, _error} -> {:error, "An error occurred trying to link your device ..."}
        end

      user_primary_device |> String.length() > 0 and device != nil and
          user_primary_device == device.device_id ->
        {:ok, "Device validated, continuing signin ..."}

      user_primary_device |> String.length() > 0 and device != nil and
          user_primary_device != device.device_id ->
        if device.enabled == true and device.blocked == false do
          message =
            "You logged into your account on your secondary device #{device.device_name}"

          {:ok, message}
        else
          if device.blocked == true do
            {:error,
             "This device is blocked from accessing your account, please contact support..."}
          else
            if device.enabled == false do
              {:error, "This device needs to be enabled to access your account, enable this device in your device settings"}
            end
          end
        end

      user_primary_device |> String.length() > 0 and device == nil ->
        case Device.create(
               last_seen_at: DateTime.utc_now(),
               device_id: params["device_id"],
               user_id: user.id,
               enabled: false,
               blocked: true
             ) do
          {:error, _error} ->
            {:error, "An error occurred when logging unknown device ..."}

          _unknown_device ->
            {:error,
             "An unknown device attempted to log into your mobile banking account and has been blocked ..."}
        end
    end
  end

  defp get_user_bank_info(account_number) do
    case ServiceManagerWeb.Api.Services.Remote.ProfileFromRemoteService.get_profile_by_account_number_v4(
           account_number
         ) do
      {:ok, bank_details} ->
        {:ok,
         %{
           account_balance: bank_details["account_balance"],
           working_balance: bank_details["available_balance"],
           clearing_balance: bank_details["cleared_balance"],
           customer_no: bank_details["customer_no"],
           date_of_birth: bank_details["date_of_birth"],
           email: bank_details["email"],
           first_name: bank_details["first_name"],
           last_name: bank_details["last_name"],
           address: bank_details["address"],
           sync_status: "synced",
           last_sync_at: DateTime.utc_now() |> DateTime.truncate(:second)
         }}

      {:error, _error} ->
        {:error,
         "We’re unable to retrieve your account details at the moment. Please try again later or contact customer support if the issue persists."}
    end
  end


  # Function to invalidate a token
  def invalidate(token) do
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Starting token invalidation")
    chain_id = "SECURITY"
    # Check if the token exists in the database
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Checking token existence")

    case Repo.get_by(Token, token: token) do
      nil ->
        # If the token does not exist, respond with an error message
        Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Token not found")

        %{
          "message" => "Token not found",
          "status" => false,
          "data" => %{
            "user" => %{},
            "auth" => %{}
          }
        }

      db_token ->
        # If the token exists, update its revoked_at field and respond with a success message
        Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Revoking token")

        # Update the token with revoked_at timestamp
        {:ok, revoked_token} = Repo.update(Token.changeset(db_token, %{revoked_at: DateTime.utc_now()}))

        # Handle device session cleanup for multi-session support
        handle_device_session_cleanup(revoked_token)

        Logger.info(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * Token invalidated successfully"
        )

        %{
          "message" => "Token invalidated",
          "status" => true,
          "data" => %{
            "user" => %{},
            "auth" => %{}
          }
        }
    end
  end


  defp handle_device_session_cleanup(token) do
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Handling device session cleanup")

    # Find and delete corresponding device session
    case Repo.get_by(ServiceManager.Schemas.Session.DeviceSession,
                    access_token: token.token) do
      nil ->
        Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * No device session found")

      device_session ->
        Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Deleting device session")

        # Delete the device session from the database
        Repo.delete(device_session)

        # Update active device last activity
        update_active_device_status(device_session.device_id, device_session.user_id)
    end
  end

  defp update_active_device_status(device_id, user_id) do
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Updating active device status")

    case Repo.get_by(ServiceManager.Schemas.ActiveDevice,
                    device_id: device_id,
                    user_id: user_id) do
      nil ->
        Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * No active device record found")

      active_device ->
        Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Updating device last seen")

        # Update last seen timestamp for the device
        active_device
        |> ServiceManager.Schemas.ActiveDevice.changeset(%{
          last_seen_at: DateTime.utc_now()
        })
        |> Repo.update()
    end
  end


  # Private function to generate extra claims for a user
  defp extra_claims(user) do
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Generating extra claims")
    # Currently, no extra claims are generated
    %{
      # "user_id" => user.id || "",
      # "primary_device_id" => user.primary_device_id || "",
      # "session_token" => user.session_id || "",
      "access_token" => Ecto.UUID.generate(),
      # "access_code" => user.customer_no || "",
      "salt" => Ecto.UUID.generate(),
      "otp_code" => :rand.uniform(999999),
      "random_token" => Ecto.UUID.generate(),
      "token_hash" => Ecto.UUID.generate(),
      "expires_at" => DateTime.add(DateTime.utc_now(), 3600, :second),

    }
  end

  # Private function to create a token for a user
  defp create_token(token, user_id) do
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Creating token in database")
    # Create a new token with the provided token, user id and expiry time

    now = DateTime.utc_now() |> DateTime.add(2, :hour)
    expires_at = DateTime.add(now, 300, :second) |> DateTime.truncate(:second)

    %Token{}
    |> Token.changeset(%{
      token: token,
      user_id: user_id,
      # Token expires in 1 hour
      expires_at: expires_at
    })
    |> Repo.insert()
  end

  track do
    # Function to check if a token is valid
    def token_valid?(token) do
      Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Checking token validity")

      # Check if the token exists in the database
      Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Verifying token in database")

      ServiceManager.Token.verify_and_validate(token) |> IO.inspect(label: "token_valid?")



      case Repo.get_by(Token, token: token) do
        nil ->
          Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Token not found")
          false

        db_token ->
          # If the token exists, check if it has not expired and has not been revoked
          Logger.info(
            "[#{DateTime.utc_now() |> DateTime.to_string()}] * Checking token expiration and revocation"
          )

          now = DateTime.utc_now() |> DateTime.truncate(:second) |> DateTime.add(2, :hour)

          result = db_token.expires_at > now && is_nil(db_token.revoked_at)

          if result do
            # If token is still valid, update expires_at to 5 minutes forward of now
            Logger.info(
              "[#{DateTime.utc_now() |> DateTime.to_string()}] * Extending token expiration by 5 minutes"
            )

            new_expires_at = DateTime.add(now, 300, :second)

            db_token
            |> Token.changeset(%{expires_at: new_expires_at})
            |> Repo.update()
          end

          Logger.info(
            "[#{DateTime.utc_now() |> DateTime.to_string()}] * Token validity check complete: #{result}"
          )

          result
      end
    end
  end

  track do
    def refresh_token(token) do
      Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Starting token refresh")

      # Check if the token is valid
      Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Validating current token")

      case token_valid?(token) do
        false ->
          # If the token is not valid, respond with an error message
          Logger.info(
            "[#{DateTime.utc_now() |> DateTime.to_string()}] * Token refresh failed: Invalid token"
          )

          respond({:error, "Invalid token"})

        true ->
          # If the token is valid, generate a new token
          Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Generating new token")
          new_token = generate_token(extra_claims(%{}))

          # Create the new token in the database
          Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Saving new token")
          create_token(new_token, Repo.get_by(Token, token: token).user_id)

          # Invalidate the old token
          Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Invalidating old token")
          invalidate(token)

          # Respond with the new token
          Logger.info(
            "[#{DateTime.utc_now() |> DateTime.to_string()}] * Token refresh completed successfully"
          )

          respond(
            {:ok, new_token},
            Repo.get_by(ServiceManager.Accounts.User,
              id: Repo.get_by(Token, token: new_token).user_id
            )
          )
      end
    end
  end

  track do
    def verify_token(token) do
      Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Starting token verification")

      case token_valid?(token) do
        false ->
          # If the token is not valid, respond with an error message
          Logger.info(
            "[#{DateTime.utc_now() |> DateTime.to_string()}] * Token verification failed"
          )

          {:error, "Invalid token"}

        true ->
          # If the token is valid, get the user associated with the token
          Logger.info(
            "[#{DateTime.utc_now() |> DateTime.to_string()}] * Retrieving user for verified token"
          )

          user =
            Repo.get_by(ServiceManager.Accounts.User,
              id: Repo.get_by(Token, token: token).user_id
            )

          # Respond with the user
          Logger.info(
            "[#{DateTime.utc_now() |> DateTime.to_string()}] * Token verification completed successfully"
          )

          {:ok, user}
      end
    end
  end

  # Private function to generate a token
  defp generate_token(claims) do
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Generating token with claims")
    # Generate and sign a token with the provided claims
    ServiceManager.Token.generate_and_sign!(claims)
  end

  # Private function to respond with an error message
  defp respond({:error, error_info}, _user \\ %{}) do
    # Respond with a message indicating invalid API credentials
    %{
      "message" => error_info,
      "status" => false,
      "data" => %{
        "user" => %{},
        "auth" => %{}
      }
    }
  end

  # Private function to respond with a success message
  defp respond({:ok, token_info}, user) do
    # Respond with a message indicating success and the user's email and access token
    %{
      "message" => "success",
      "status" => true,
      "data" => %{
        "user" => %{
          "user-id" => user.username,
          "profile" => user
        },
        "auth" => %{
          "access_token" => token_info
        }
      }
    }
  end

  track do
    def verify_memorable_word(username, memorable_word) do
      Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Verifying memorable word")

      case Repo.get_by(ServiceManager.Accounts.User, username: username) do
        nil ->
          Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * User not found")
          {:error, "Invalid username or memorable word"}

        user ->
          if user.memorable_word == memorable_word do
            Logger.info(
              "[#{DateTime.utc_now() |> DateTime.to_string()}] * Memorable word verified"
            )

            {:ok, user}
          else
            Logger.info(
              "[#{DateTime.utc_now() |> DateTime.to_string()}] * Invalid memorable word"
            )

            {:error, "Invalid username or memorable word"}
          end
      end
    end
  end

  track do
    def reset_password(username) do
      Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Starting password reset")

      case Repo.get_by(ServiceManager.Accounts.User, username: username) do
        nil ->
          Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * User not found")
          {:error, "User not found"}

        user ->
          # Generate a secure random password (12 characters)
          new_password =
            :crypto.strong_rand_bytes(12)
            |> Base.encode64(padding: false)
            |> binary_part(0, 12)

          Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Generated new password")

          # Update user's password
          case Repo.update(
                 ServiceManager.Accounts.User.update_password_changeset(user, %{
                   password: new_password
                 })
               ) do
            {:ok, updated_user} ->
              Logger.info(
                "[#{DateTime.utc_now() |> DateTime.to_string()}] * Password updated, sending SMS"
              )

              # Send SMS with new password
              message = "Your new FDH Mobile Banking password is: #{new_password}"

              {:ok, _sms} =
                ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
                  msisdn: updated_user.phone_number,
                  message: message,
                  details: %{
                    "type" => "password_reset",
                    "user_id" => updated_user.id
                  }
                })

              {:ok,
               %{
                 "message" => "Password reset successful. Check your phone for the new password.",
                 "status" => true,
                 "data" => %{}
               }}

            {:error, changeset} ->
              Logger.error(
                "[#{DateTime.utc_now() |> DateTime.to_string()}] * Failed to update password: #{inspect(changeset.errors)}"
              )

              {:error, "Failed to reset password"}
          end
      end
    end
  end
end
