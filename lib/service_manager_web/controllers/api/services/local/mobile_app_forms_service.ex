defmodule ServiceManagerWeb.Api.Services.Local.MobileAppFormsService do
  import Ecto.Query
  alias ServiceManager.Repo
  alias ServiceManagerWeb.Api.MobileAppFormsSchema

  @doc """
  Get form fields for mobile app consumption with optional screen and page filtering
  """
  def get_form_fields(form_name, params \\ %{}) do
    query = base_query()
    |> where([f], f.form == ^form_name)
    |> where([f], f.active == true)

    query = apply_optional_filters(query, params)

    fields = query
    |> order_by([f], [f.field_order, f.inserted_at])
    |> Repo.all()

    case fields do
      [] -> {:error, "No form fields found for form: #{form_name}"}
      fields -> {:ok, fields}
    end
  end

  @doc """
  List all form fields with optional filtering
  """
  def list_fields(params \\ %{}) do
    query = base_query()
    |> apply_optional_filters(params)
    |> order_by([f], [f.form, f.screen, f.page, f.field_order, f.inserted_at])

    fields = Repo.all(query)
    {:ok, fields}
  end

  @doc """
  Get a specific field by ID
  """
  def get_field(field_id) do
    case Repo.get(MobileAppFormsSchema, field_id) do
      nil -> {:error, "Field not found"}
      field -> {:ok, field}
    end
  end

  @doc """
  Create a new form field
  """
  def create_field(attrs) do
    %MobileAppFormsSchema{}
    |> MobileAppFormsSchema.changeset(attrs)
    |> Repo.insert()
    |> case do
      {:ok, field} -> {:ok, field}
      {:error, changeset} -> {:error, format_changeset_errors(changeset)}
    end
  end

  @doc """
  Update an existing form field
  """
  def update_field(field_id, attrs) do
    case get_field(field_id) do
      {:ok, field} ->
        field
        |> MobileAppFormsSchema.changeset(attrs)
        |> Repo.update()
        |> case do
          {:ok, updated_field} -> {:ok, updated_field}
          {:error, changeset} -> {:error, format_changeset_errors(changeset)}
        end
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  Delete a form field
  """
  def delete_field(field_id) do
    case get_field(field_id) do
      {:ok, field} ->
        case Repo.delete(field) do
          {:ok, _} -> {:ok, "Field deleted successfully"}
          {:error, changeset} -> {:error, format_changeset_errors(changeset)}
        end
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  Create a new form (metadata operation)
  """
  def create_form(form_name, version \\ "1.0") do
    # Check if form already exists
    existing = base_query()
    |> where([f], f.form == ^form_name and f.version == ^version)
    |> limit(1)
    |> Repo.one()

    case existing do
      nil -> {:ok, %{form: form_name, version: version, message: "Form identifier created"}}
      _ -> {:error, "Form '#{form_name}' with version '#{version}' already exists"}
    end
  end

  @doc """
  Create a new screen for a form
  """
  def create_screen(form_name, screen_name, version \\ "1.0") do
    # Check if form exists
    form_exists = base_query()
    |> where([f], f.form == ^form_name and f.version == ^version)
    |> limit(1)
    |> Repo.one()

    case form_exists do
      nil -> {:error, "Form '#{form_name}' with version '#{version}' does not exist"}
      _ ->
        # Check if screen already exists
        existing_screen = base_query()
        |> where([f], f.form == ^form_name and f.screen == ^screen_name and f.version == ^version)
        |> limit(1)
        |> Repo.one()

        case existing_screen do
          nil -> {:ok, %{form: form_name, screen: screen_name, version: version, message: "Screen identifier created"}}
          _ -> {:error, "Screen '#{screen_name}' already exists in form '#{form_name}'"}
        end
    end
  end

  @doc """
  Create a new page for a screen
  """
  def create_page(form_name, screen_name, page_name, version \\ "1.0") do
    # Check if screen exists
    screen_exists = base_query()
    |> where([f], f.form == ^form_name and f.screen == ^screen_name and f.version == ^version)
    |> limit(1)
    |> Repo.one()

    case screen_exists do
      nil -> {:error, "Screen '#{screen_name}' does not exist in form '#{form_name}'"}
      _ ->
        # Check if page already exists
        existing_page = base_query()
        |> where([f], f.form == ^form_name and f.screen == ^screen_name and f.page == ^page_name and f.version == ^version)
        |> limit(1)
        |> Repo.one()

        case existing_page do
          nil -> {:ok, %{form: form_name, screen: screen_name, page: page_name, version: version, message: "Page identifier created"}}
          _ -> {:error, "Page '#{page_name}' already exists in screen '#{screen_name}'"}
        end
    end
  end

  @doc """
  Get list of forms
  """
  def list_forms do
    query = from f in MobileAppFormsSchema,
      select: %{form: f.form, version: f.version},
      distinct: [f.form, f.version],
      order_by: [f.form, f.version]

    forms = Repo.all(query)
    {:ok, forms}
  end

  @doc """
  Get list of screens for a form
  """
  def list_screens(form_name, version \\ nil) do
    query = base_query()
    |> where([f], f.form == ^form_name)
    |> where([f], not is_nil(f.screen))

    query = if version do
      where(query, [f], f.version == ^version)
    else
      query
    end

    screens = query
    |> select([f], %{form: f.form, screen: f.screen, version: f.version})
    |> distinct([f], [f.form, f.screen, f.version])
    |> order_by([f], [f.screen, f.version])
    |> Repo.all()

    {:ok, screens}
  end

  @doc """
  Get list of pages for a screen
  """
  def list_pages(form_name, screen_name, version \\ nil) do
    query = base_query()
    |> where([f], f.form == ^form_name and f.screen == ^screen_name)
    |> where([f], not is_nil(f.page))

    query = if version do
      where(query, [f], f.version == ^version)
    else
      query
    end

    pages = query
    |> select([f], %{form: f.form, screen: f.screen, page: f.page, version: f.version})
    |> distinct([f], [f.form, f.screen, f.page, f.version])
    |> order_by([f], [f.page, f.version])
    |> Repo.all()

    {:ok, pages}
  end

  # Private helper functions

  defp base_query do
    from f in MobileAppFormsSchema
  end

  defp apply_optional_filters(query, params) do
    query
    |> filter_by_screen(params)
    |> filter_by_page(params)
    |> filter_by_version(params)
    |> filter_by_active(params)
  end

  defp filter_by_screen(query, %{"screen" => screen}) when not is_nil(screen) do
    where(query, [f], f.screen == ^screen)
  end
  defp filter_by_screen(query, %{screen: screen}) when not is_nil(screen) do
    where(query, [f], f.screen == ^screen)
  end
  defp filter_by_screen(query, _), do: query

  defp filter_by_page(query, %{"page" => page}) when not is_nil(page) do
    where(query, [f], f.page == ^page)
  end
  defp filter_by_page(query, %{page: page}) when not is_nil(page) do
    where(query, [f], f.page == ^page)
  end
  defp filter_by_page(query, _), do: query

  defp filter_by_version(query, %{"version" => version}) when not is_nil(version) do
    where(query, [f], f.version == ^version)
  end
  defp filter_by_version(query, %{version: version}) when not is_nil(version) do
    where(query, [f], f.version == ^version)
  end
  defp filter_by_version(query, _), do: query

  defp filter_by_active(query, %{"active" => active}) when is_boolean(active) do
    where(query, [f], f.active == ^active)
  end
  defp filter_by_active(query, %{active: active}) when is_boolean(active) do
    where(query, [f], f.active == ^active)
  end
  defp filter_by_active(query, _), do: query

  defp format_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
    |> Enum.join(", ")
  end
end